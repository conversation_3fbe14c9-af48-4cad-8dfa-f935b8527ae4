﻿$(function () {
    require.config({
        paths: {
            'v6-request': 'https://static.pkulaw.com/statics/npm/v6-request',
            'addlogs': 'https://static.pkulaw.com/statics/npm/addlogs'
        }
    })
    var jsUrl = "https://static.pkulaw.com/statics/npm/complete-info.js";
    require(['v6-request', jsUrl, 'addlogs'], function (_ref, _ref2, _ref3) {
        var store = _ref.store;
        var fbCompleteInfo = _ref2.fbCompleteInfo;
        var funlogsCode = _ref3.funlogsCode;
        window.CompleteInfo = function () {
            if (!window.Complete) {
                window.Complete = new Vue({
                    el: '#completeInfo',
                    data: function data() {
                        return {
                            renderData: {
                                token: $("#access_token").val(),
                                currentEnv: $('#v6Environment').val(),// 环境配置 1:测试环境 test 2:预发布环境 pre 2:正式环境 prod
                                width: '800px',
                                gid: $('#articleGid').val()
                            },
                            show: false
                        };
                    },
                    components: {
                        fbCompleteInfo: fbCompleteInfo
                    },
                    methods: {
                        handleShow: function handleShow() {
                            this.show = true;
                        },
                        submitExpressions: function submitExpressions(data) {
                            var url = window.location.href;
                            console.log(data);
                            if (url.indexOf('?') > -1) {
                                url = url + '&jiamizi=' + data.sign;
                            } else {
                                url = url + '?jiamizi=' + data.sign;
                            }
                            window.location.href = url;
                            this.handleClose();
                            delete window.Complete;
                        },
                        handleClose: function handleClose() {
                            this.show = false;
                            delete window.Complete;
                        }
                    }
                });
            }
        };
    });
})

