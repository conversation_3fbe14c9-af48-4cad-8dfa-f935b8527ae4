(['C:\\Users\\<USER>\\Desktop\\ee\\pkulaw\\pkulaw\\北大法典爬虫GUI.py'],
 ['C:\\Users\\<USER>\\Desktop\\ee\\pkulaw\\pkulaw'],
 [],
 [('D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.10.18 | packaged by conda-forge | (main, Jun  4 2025, 14:42:04) [MSC '
 'v.1943 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('北大法典爬虫GUI',
   'C:\\Users\\<USER>\\Desktop\\ee\\pkulaw\\pkulaw\\北大法典爬虫GUI.py',
   'PYSOURCE')],
 [('subprocess',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\subprocess.py',
   'PYMODULE'),
  ('selectors',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\selectors.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\contextlib.py',
   'PYMODULE'),
  ('threading',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\_threading_local.py',
   'PYMODULE'),
  ('signal',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\signal.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.server',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xmlrpc\\server.py',
   'PYMODULE'),
  ('pydoc',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\pydoc.py',
   'PYMODULE'),
  ('getopt',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\getopt.py',
   'PYMODULE'),
  ('gettext',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\gettext.py',
   'PYMODULE'),
  ('copy',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\copy.py',
   'PYMODULE'),
  ('webbrowser',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\webbrowser.py',
   'PYMODULE'),
  ('glob',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\glob.py',
   'PYMODULE'),
  ('fnmatch',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\fnmatch.py',
   'PYMODULE'),
  ('shlex',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\shlex.py',
   'PYMODULE'),
  ('email.message',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\string.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\calendar.py',
   'PYMODULE'),
  ('argparse',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\argparse.py',
   'PYMODULE'),
  ('email',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('quopri',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\quopri.py',
   'PYMODULE'),
  ('uu', 'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\uu.py', 'PYMODULE'),
  ('optparse',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\optparse.py',
   'PYMODULE'),
  ('textwrap',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\textwrap.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\tty.py', 'PYMODULE'),
  ('tokenize',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\token.py',
   'PYMODULE'),
  ('sysconfig',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('pprint',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\dataclasses.py',
   'PYMODULE'),
  ('platform',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\platform.py',
   'PYMODULE'),
  ('pkgutil',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\zipimport.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('typing',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\typing.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('pathlib',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\pathlib.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\py_compile.py',
   'PYMODULE'),
  ('lzma',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\lzma.py',
   'PYMODULE'),
  ('_compression',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\bz2.py', 'PYMODULE'),
  ('importlib',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._common',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib._adapters',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('csv', 'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\csv.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('socketserver',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\socketserver.py',
   'PYMODULE'),
  ('html',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\html\\entities.py',
   'PYMODULE'),
  ('inspect',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\inspect.py',
   'PYMODULE'),
  ('dis', 'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\dis.py', 'PYMODULE'),
  ('opcode',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\opcode.py',
   'PYMODULE'),
  ('ast', 'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\ast.py', 'PYMODULE'),
  ('http.server',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\http\\server.py',
   'PYMODULE'),
  ('http',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('ipaddress',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\ipaddress.py',
   'PYMODULE'),
  ('getpass',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\netrc.py',
   'PYMODULE'),
  ('ssl', 'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('hashlib',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\hashlib.py',
   'PYMODULE'),
  ('bisect',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\bisect.py',
   'PYMODULE'),
  ('mimetypes',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\mimetypes.py',
   'PYMODULE'),
  ('gzip',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\gzip.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('http.client',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\http\\client.py',
   'PYMODULE'),
  ('decimal',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\contextvars.py',
   'PYMODULE'),
  ('numbers',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\numbers.py',
   'PYMODULE'),
  ('datetime',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\_strptime.py',
   'PYMODULE'),
  ('base64',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\base64.py',
   'PYMODULE'),
  ('hmac',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\hmac.py',
   'PYMODULE'),
  ('struct',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\struct.py',
   'PYMODULE'),
  ('socket',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\socket.py',
   'PYMODULE'),
  ('tempfile',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\tempfile.py',
   'PYMODULE'),
  ('shutil',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\tarfile.py',
   'PYMODULE'),
  ('logging',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\pickle.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\runpy.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_py_abc',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\_py_abc.py',
   'PYMODULE'),
  ('stringprep',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('PyQt5',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('random',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\statistics.py',
   'PYMODULE'),
  ('fractions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\fractions.py',
   'PYMODULE'),
  ('DrissionPage',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\__init__.py',
   'PYMODULE'),
  ('DrissionPage.version',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\version.py',
   'PYMODULE'),
  ('DrissionPage._pages.web_page',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_pages\\web_page.py',
   'PYMODULE'),
  ('DrissionPage._pages', '-', 'PYMODULE'),
  ('DrissionPage._units.setter',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_units\\setter.py',
   'PYMODULE'),
  ('DrissionPage._units', '-', 'PYMODULE'),
  ('DrissionPage.errors',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\errors.py',
   'PYMODULE'),
  ('DrissionPage._functions.web',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_functions\\web.py',
   'PYMODULE'),
  ('DrissionPage._functions', '-', 'PYMODULE'),
  ('DataRecorder.tools',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DataRecorder\\tools.py',
   'PYMODULE'),
  ('DataRecorder',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DataRecorder\\__init__.py',
   'PYMODULE'),
  ('DataRecorder.recorder',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DataRecorder\\recorder.py',
   'PYMODULE'),
  ('json',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('DataRecorder.style.cell_style',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DataRecorder\\style\\cell_style.py',
   'PYMODULE'),
  ('DataRecorder.style',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DataRecorder\\style\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('defusedxml',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('defusedxml.common',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('__future__',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\__future__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('lxml',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('bs4',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4.element',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bs4.css',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('soupsieve',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('bs4.dammit',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('bs4.builder',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('html.parser',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\html\\parser.py',
   'PYMODULE'),
  ('_markupbase',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\_markupbase.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('cgi', 'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\cgi.py', 'PYMODULE'),
  ('difflib',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\difflib.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('doctest',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\doctest.py',
   'PYMODULE'),
  ('unittest',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('pdb', 'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\pdb.py', 'PYMODULE'),
  ('code',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\codeop.py',
   'PYMODULE'),
  ('bdb', 'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\cmd.py', 'PYMODULE'),
  ('lxml.cssselect',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('cssselect',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cssselect\\__init__.py',
   'PYMODULE'),
  ('cssselect.xpath',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cssselect\\xpath.py',
   'PYMODULE'),
  ('cssselect.parser',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cssselect\\parser.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('openpyxl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('DataRecorder.setter',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DataRecorder\\setter.py',
   'PYMODULE'),
  ('DataRecorder.base',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DataRecorder\\base.py',
   'PYMODULE'),
  ('DataRecorder.filler',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DataRecorder\\filler.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('DataRecorder.db_recorder',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DataRecorder\\db_recorder.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('DataRecorder.byte_recorder',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DataRecorder\\byte_recorder.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('DrissionPage._functions.tools',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_functions\\tools.py',
   'PYMODULE'),
  ('win32con',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('DrissionPage._configs.options_manage',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_configs\\options_manage.py',
   'PYMODULE'),
  ('DrissionPage._configs', '-', 'PYMODULE'),
  ('configparser',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\configparser.py',
   'PYMODULE'),
  ('DrissionPage._units.cookies_setter',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_units\\cookies_setter.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('h2.events',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2\\events.py',
   'PYMODULE'),
  ('h2',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2\\__init__.py',
   'PYMODULE'),
  ('h2.settings',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2\\settings.py',
   'PYMODULE'),
  ('h2.exceptions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2\\exceptions.py',
   'PYMODULE'),
  ('h2.errors',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2\\errors.py',
   'PYMODULE'),
  ('hyperframe.frame',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\hyperframe\\frame.py',
   'PYMODULE'),
  ('hyperframe',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\hyperframe\\__init__.py',
   'PYMODULE'),
  ('hyperframe.flags',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\hyperframe\\flags.py',
   'PYMODULE'),
  ('hyperframe.exceptions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\hyperframe\\exceptions.py',
   'PYMODULE'),
  ('h2.connection',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2\\connection.py',
   'PYMODULE'),
  ('h2.windows',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2\\windows.py',
   'PYMODULE'),
  ('h2.utilities',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2\\utilities.py',
   'PYMODULE'),
  ('hpack',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\hpack\\__init__.py',
   'PYMODULE'),
  ('hpack.struct',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\hpack\\struct.py',
   'PYMODULE'),
  ('h2.stream',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2\\stream.py',
   'PYMODULE'),
  ('h2.frame_buffer',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2\\frame_buffer.py',
   'PYMODULE'),
  ('hpack.exceptions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\hpack\\exceptions.py',
   'PYMODULE'),
  ('hpack.hpack',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\hpack\\hpack.py',
   'PYMODULE'),
  ('hpack.huffman_table',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\hpack\\huffman_table.py',
   'PYMODULE'),
  ('hpack.huffman_constants',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\hpack\\huffman_constants.py',
   'PYMODULE'),
  ('hpack.huffman',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\hpack\\huffman.py',
   'PYMODULE'),
  ('hpack.compat',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\hpack\\compat.py',
   'PYMODULE'),
  ('hpack.table',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\hpack\\table.py',
   'PYMODULE'),
  ('h2.config',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2\\config.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('socks',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('cryptography',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('DrissionPage._functions.settings',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_functions\\settings.py',
   'PYMODULE'),
  ('DrissionPage._functions.texts',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_functions\\texts.py',
   'PYMODULE'),
  ('DrissionPage._functions.cookies',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_functions\\cookies.py',
   'PYMODULE'),
  ('tldextract',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\tldextract\\__init__.py',
   'PYMODULE'),
  ('tldextract.tldextract',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\tldextract\\tldextract.py',
   'PYMODULE'),
  ('tldextract.suffix_list',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\tldextract\\suffix_list.py',
   'PYMODULE'),
  ('requests_file',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\requests_file.py',
   'PYMODULE'),
  ('tldextract.remote',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\tldextract\\remote.py',
   'PYMODULE'),
  ('tldextract.cache',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\tldextract\\cache.py',
   'PYMODULE'),
  ('filelock',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\filelock\\__init__.py',
   'PYMODULE'),
  ('filelock.version',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\filelock\\version.py',
   'PYMODULE'),
  ('filelock.asyncio',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\filelock\\asyncio.py',
   'PYMODULE'),
  ('filelock._windows',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\filelock\\_windows.py',
   'PYMODULE'),
  ('filelock._util',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\filelock\\_util.py',
   'PYMODULE'),
  ('filelock._unix',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\filelock\\_unix.py',
   'PYMODULE'),
  ('filelock._soft',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\filelock\\_soft.py',
   'PYMODULE'),
  ('filelock._error',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\filelock\\_error.py',
   'PYMODULE'),
  ('filelock._api',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\filelock\\_api.py',
   'PYMODULE'),
  ('tldextract._version',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\tldextract\\_version.py',
   'PYMODULE'),
  ('DrissionPage._base.base',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_base\\base.py',
   'PYMODULE'),
  ('DrissionPage._base', '-', 'PYMODULE'),
  ('DrissionPage._functions.locator',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_functions\\locator.py',
   'PYMODULE'),
  ('DrissionPage._functions.by',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_functions\\by.py',
   'PYMODULE'),
  ('DrissionPage._functions.elements',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_functions\\elements.py',
   'PYMODULE'),
  ('DrissionPage._elements.none_element',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_elements\\none_element.py',
   'PYMODULE'),
  ('DrissionPage._elements', '-', 'PYMODULE'),
  ('DownloadKit',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DownloadKit\\__init__.py',
   'PYMODULE'),
  ('DownloadKit.downloadKit',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DownloadKit\\downloadKit.py',
   'PYMODULE'),
  ('DownloadKit.setter',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DownloadKit\\setter.py',
   'PYMODULE'),
  ('DownloadKit.mission',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DownloadKit\\mission.py',
   'PYMODULE'),
  ('DownloadKit._funcs',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DownloadKit\\_funcs.py',
   'PYMODULE'),
  ('DrissionPage._pages.session_page',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_pages\\session_page.py',
   'PYMODULE'),
  ('DrissionPage._elements.session_element',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_elements\\session_element.py',
   'PYMODULE'),
  ('DrissionPage._pages.chromium_page',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_pages\\chromium_page.py',
   'PYMODULE'),
  ('DrissionPage._units.waiter',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_units\\waiter.py',
   'PYMODULE'),
  ('DrissionPage._pages.chromium_base',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_pages\\chromium_base.py',
   'PYMODULE'),
  ('DrissionPage._units.states',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_units\\states.py',
   'PYMODULE'),
  ('DrissionPage._units.scroller',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_units\\scroller.py',
   'PYMODULE'),
  ('DrissionPage._units.screencast',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_units\\screencast.py',
   'PYMODULE'),
  ('DrissionPage._units.rect',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_units\\rect.py',
   'PYMODULE'),
  ('DrissionPage._units.listener',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_units\\listener.py',
   'PYMODULE'),
  ('DrissionPage._base.driver',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_base\\driver.py',
   'PYMODULE'),
  ('websocket',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\websocket\\__init__.py',
   'PYMODULE'),
  ('websocket._socket',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\websocket\\_socket.py',
   'PYMODULE'),
  ('websocket._utils',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\websocket\\_utils.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\websocket\\_ssl_compat.py',
   'PYMODULE'),
  ('websocket._exceptions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\websocket\\_exceptions.py',
   'PYMODULE'),
  ('websocket._core',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\websocket\\_core.py',
   'PYMODULE'),
  ('websocket._http',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\websocket\\_http.py',
   'PYMODULE'),
  ('websocket._url',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\websocket\\_url.py',
   'PYMODULE'),
  ('websocket._handshake',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\websocket\\_handshake.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\websocket\\_cookiejar.py',
   'PYMODULE'),
  ('websocket._app',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\websocket\\_app.py',
   'PYMODULE'),
  ('websocket._logging',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\websocket\\_logging.py',
   'PYMODULE'),
  ('websocket._abnf',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\websocket\\_abnf.py',
   'PYMODULE'),
  ('DrissionPage._units.console',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_units\\console.py',
   'PYMODULE'),
  ('DrissionPage._units.actions',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_units\\actions.py',
   'PYMODULE'),
  ('DrissionPage._functions.keys',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_functions\\keys.py',
   'PYMODULE'),
  ('DrissionPage._elements.chromium_element',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_elements\\chromium_element.py',
   'PYMODULE'),
  ('DrissionPage._pages.chromium_frame',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_pages\\chromium_frame.py',
   'PYMODULE'),
  ('DrissionPage._units.selector',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_units\\selector.py',
   'PYMODULE'),
  ('DrissionPage._units.clicker',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_units\\clicker.py',
   'PYMODULE'),
  ('DrissionPage._units.downloader',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_units\\downloader.py',
   'PYMODULE'),
  ('DrissionPage._configs.session_options',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_configs\\session_options.py',
   'PYMODULE'),
  ('DrissionPage._configs.chromium_options',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_configs\\chromium_options.py',
   'PYMODULE'),
  ('DrissionPage._base.chromium',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_base\\chromium.py',
   'PYMODULE'),
  ('DrissionPage._pages.mix_tab',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_pages\\mix_tab.py',
   'PYMODULE'),
  ('DrissionPage._pages.chromium_tab',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_pages\\chromium_tab.py',
   'PYMODULE'),
  ('DrissionPage._functions.browser',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\DrissionPage\\_functions\\browser.py',
   'PYMODULE')],
 [('python310.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\python310.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'BINARY'),
  ('select.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\sip.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\etree.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\_elementpath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\sax.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\objectify.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\charset_normalizer\\md.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\html\\diff.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\clean.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\html\\clean.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\builder.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('win32\\win32process.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\win32\\win32process.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\_cffi_backend.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'D:\\ProgramData\\miniconda3\\envs\\spider\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\MSVCP140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('liblzma.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('libbz2.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Library\\bin\\libbz2.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('libexpat.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Library\\bin\\libexpat.dll',
   'BINARY'),
  ('ffi-8.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Library\\bin\\ffi-8.dll',
   'BINARY'),
  ('python3.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\python3.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes310.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\pywin32_system32\\pywintypes310.dll',
   'BINARY'),
  ('sqlite3.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Library\\bin\\sqlite3.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\ucrtbase.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\METADATA',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography-45.0.4.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\INSTALLER',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography-45.0.4.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\WHEEL',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography-45.0.4.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\RECORD',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography-45.0.4.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('h2-3.2.0.dist-info\\INSTALLER',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2-3.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('h2-3.2.0.dist-info\\top_level.txt',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2-3.2.0.dist-info\\top_level.txt',
   'DATA'),
  ('h2-3.2.0.dist-info\\RECORD',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2-3.2.0.dist-info\\RECORD',
   'DATA'),
  ('h2-3.2.0.dist-info\\LICENSE',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2-3.2.0.dist-info\\LICENSE',
   'DATA'),
  ('h2-3.2.0.dist-info\\METADATA',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2-3.2.0.dist-info\\METADATA',
   'DATA'),
  ('h2-3.2.0.dist-info\\WHEEL',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2-3.2.0.dist-info\\WHEEL',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\ee\\pkulaw\\pkulaw\\build\\北大法宝爬虫\\base_library.zip',
   'DATA')],
 [('linecache',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\linecache.py',
   'PYMODULE'),
  ('copyreg',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\copyreg.py',
   'PYMODULE'),
  ('collections.abc',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('locale',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\locale.py',
   'PYMODULE'),
  ('sre_constants',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\sre_constants.py',
   'PYMODULE'),
  ('re', 'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\re.py', 'PYMODULE'),
  ('keyword',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\keyword.py',
   'PYMODULE'),
  ('abc', 'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\abc.py', 'PYMODULE'),
  ('sre_parse',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\sre_parse.py',
   'PYMODULE'),
  ('sre_compile',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\sre_compile.py',
   'PYMODULE'),
  ('operator',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\operator.py',
   'PYMODULE'),
  ('io', 'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\io.py', 'PYMODULE'),
  ('genericpath',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\genericpath.py',
   'PYMODULE'),
  ('_weakrefset',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\_weakrefset.py',
   'PYMODULE'),
  ('heapq',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\heapq.py',
   'PYMODULE'),
  ('enum',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\enum.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('codecs',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\codecs.py',
   'PYMODULE'),
  ('weakref',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\weakref.py',
   'PYMODULE'),
  ('functools',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\functools.py',
   'PYMODULE'),
  ('types',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\types.py',
   'PYMODULE'),
  ('traceback',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\traceback.py',
   'PYMODULE'),
  ('reprlib',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\reprlib.py',
   'PYMODULE'),
  ('stat',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\stat.py',
   'PYMODULE'),
  ('_collections_abc',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('posixpath',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\posixpath.py',
   'PYMODULE'),
  ('warnings',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\warnings.py',
   'PYMODULE'),
  ('ntpath',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\ntpath.py',
   'PYMODULE'),
  ('os', 'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\os.py', 'PYMODULE')])
