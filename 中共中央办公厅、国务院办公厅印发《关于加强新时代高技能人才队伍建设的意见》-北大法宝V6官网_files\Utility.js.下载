﻿var $U =
{

    showProgress : function(message, target)
    {
        layerLoad.Open();
    },

    hideProgress : function()
    {
        layerLoad.Close();
    },

    confirm : function(title, message, okH<PERSON><PERSON>,cancelHandler)
    {
        //todo
        layer.confirm(message, options, yes, cancel);
    },

    messageBox : function(title, message, isError, onOk) {
        layer.msg(message, options, end);
    },

    blockUI : function()
    {
        layerLoad.Open();
    },

    unblockUI : function()
    {
        layerLoad.Close();
    },

    center : function(e)
    {
        var x = (($U.getViewPortWidth() - e[0].offsetWidth) /2);
        var y = (($U.getViewPortHeight() - e[0].offsetHeight) /2) + $U.getViewPortScrollY();

        $U.setLocation(e, x, y);
    },

    setLocation : function(e, x, y)
    {
        e.css({position : 'absolute', left : (x + 'px'), top : (y + 'px')});
    },

    getViewPortWidth : function()
    {
        var width = 0;

        if ((document.documentElement) && (document.documentElement.clientWidth))
        {
            width = document.documentElement.clientWidth;
        }
        else if ((document.body) && (document.body.clientWidth))
        {
            width = document.body.clientWidth;
        }
        else if (window.innerWidth)
        {
            width = window.innerWidth;
        }

        return width;
    },

    getViewPortHeight : function()
    {
        var height = 0;

        if (window.innerHeight)
        {
            height = (window.innerHeight);
        }
        else if ((document.documentElement) && (document.documentElement.clientHeight))
        {
            height = document.documentElement.clientHeight;
        }

        return height;
    },

    getContentHeight : function()
    {
        if (document.body)
        {
            if (document.body.scrollHeight)
            {
                return document.body.scrollHeight;
            }

            if (document.body.offsetHeight)
            {
                return document.body.offsetHeight;
            }
        }

        return 0;
    },

    getViewPortScrollX : function()
    {
        var scrollX = 0;

        if ((document.documentElement) && (document.documentElement.scrollLeft))
        {
            scrollX = document.documentElement.scrollLeft;
        }
        else if ((document.body) && (document.body.scrollLeft))
        {
            scrollX = document.body.scrollLeft;
        }
        else if (window.pageXOffset)
        {
            scrollX = window.pageXOffset;
        }
        else if (window.scrollX)
        {
            scrollX = window.scrollX;
        }

        return scrollX;
    },

    getViewPortScrollY : function()
    {
        var scrollY = 0;

        if ((document.documentElement) && (document.documentElement.scrollTop))
        {
            scrollY = document.documentElement.scrollTop;
        }
        else if ((document.body) && (document.body.scrollTop))
        {
            scrollY = document.body.scrollTop;
        }
        else if (window.pageYOffset)
        {
            scrollY = window.pageYOffset;
        }
        else if (window.scrollY)
        {
            scrollY = window.scrollY;
        }

        return scrollY;
    }
};