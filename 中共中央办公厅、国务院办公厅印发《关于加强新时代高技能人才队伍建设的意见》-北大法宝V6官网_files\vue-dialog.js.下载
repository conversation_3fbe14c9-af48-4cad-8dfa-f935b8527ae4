﻿
//订阅支付弹框
$(function () {
    var subScribeEnv = $("#subScribeEnv").val();
    var isUseNewScript = $("#isUseNewScript").val();
    var jsUrl = "https://resources.pkulaw.cn/common-resourcse/sdk/js/fbSubscribe/release.js";
    if (!isUseNewScript) {
        jsUrl = "https://static.pkulaw.cn/esmodule/fbSubscribe/release.js";
    }
    var usertoken = $('#access_token');
    var token = usertoken.length ? usertoken.val() : '';
    var purchasePage = $("#purchasePage").val();
    var productId = $("#productId").val();
    var paybuylayerrefershtokenurl = $("#paybuylayerrefershtokenurl").val();
    var paybuylayerapi = $("#paybuylayerapi").val();
    var userType = $("#lUserType").val();
    var userName = $("#lUserName").val();
    var nickName = $("#ILogiName").val();
    var aid = $("#KeycloakAid").val();
    var phoneNumber = $("#phoneNum").val();
    var iframeClass = new iframeModule({
        //平台id，默认1(pc端)，非必传
        platform: 1,
        //测试环境地址，必传
        BASE_URL: paybuylayerapi + "/",
        //token必传
        access_token: "Bearer " + token,
        //用户信息，必传
        userInfo: {
            preferred_username: userName,
            nickname: nickName,
            phone: phoneNumber,
            aid: aid.lenght != 0 ? aid.split(',') : ''
        }
    },
    )


    //require([jsUrl], function ({ fbSubscribe }) {
    //    console.log(fbSubscribe);
    window.CallWindowVue = function (obj) {
        console.log(1);
        if (!window.SubVues) {
            console.log(2);
            window.SubVues = new Vue({
                el: '#opareBox',
                components: {
                    fbModule: fbModule.submodule
                },

                data: function () {
                    return {
                        language: obj.locale,
                        userInfo: {
                            username: $("#LoginName").val(), //obj.userInfo.username, // 需要token里面的用户名
                            userType: $("#isGroupUser").val() =="True"?"1":"0" ,//obj.userInfo.userType,
                        },
                        access_token: $("#AccessToken").val(), //obj.userInfo.token,
                        base_url: $("#rootUrl").val()// 'https://test1.pkulaw.com'
                    }
                },
                methods: {

                    openDialog(obj) {
                        this.$refs['fbModule'].start({
                            condition: obj.fieldNodes,//"{\"Fields\":[{\"Keywords\":\"民法典1122211122\",\"KeywordsField\":\"Title\",\"MatchType\":\"Exact\",\"MatchSpan\":\"0\"}],\"ClusterFilters\":{}}",
                            conditionType: obj.conditionType,//2
                            library: obj.library,
                            displayCondition: obj.fieldNodesHtml// '<div id=condtion-wrapper><p class=category><span class=fullName>标题<span class=filterSpan>包含</span></span><span class="subCombineAs mrSpan"></span><span class="part"> <span class="all-content"><span class="bilateralMr">{</span><span class="text_word">(1)</span><span class="filterSpan">或者</span><span class="text_word">(2)</span><span class="bilateralMr">}</span></span> </span></p><p class=category><span>，</span><span class="subCombineAs spanIehandle">并且</span><span class="part"><span class="fullName mrSpan">全文<span class="filterSpan">包含</span></span></span><span class="subCombineAs mrSpan"></span><span class="part"> <span class="all-content"><span class="bilateralMr">{</span><span class="text_word">(1)</span><span class="filterSpan">并且</span><span class="text_word">(2)</span><span class="subCombineAs">&nbsp;|</span><span class="subCombineAs">同段</span><span class="bilateralMr">}</span></span></span><p></div>'
                        })
                    },
                    openPayModal() {
                        console.log('openPayModal');
                        iframeClass.openModule({
                            type: 'openProduct',
                            data: {
                                id: "subscribe",  //产品id
                                goodsType: 0,  //商品类型非必填，默认0; 1:积分,2:版本升级
                                purchasePage: 'subscribe_page1',  //购买页
                                channelType: ''  //下单引导来源,非必填,取url参数channel
                            }
                        })
                    },
                    subResult(str) {
                        console.log('subResult', str);
                        if (str == "sub") {
                            $("#btn-subscribe").find(".subText").text("已订阅");
                            $("#btn-subscribe").addClass("subscribe_btn_disabled");
                            $("#btnsubscribe").text("UnSubscribe");
                        } else {
                            $("#btnsubscribe").text("Subscribe");
                        }

                    },
                    handleSaveCallBack: function () {

                    },
                    handleSpeedCallBack: function () {
                        $("#btnsubscribe").text("Subscribe");
                    },

                },
                template: '<fbModule ref="fbModule" :access_token="access_token" :base_url="base_url" :userInfo="userInfo" :language="language" v-on:openPayModal="openPayModal" v-on:subResult="subResult" />'

            });
        }

        SubVues.openDialog(obj);
    }
    //})

    //自定义排序
    require.config({
        paths: {
            'v6-request': 'https://static.pkulaw.com/statics/npm/v6-request',
            'addlogs': 'https://static.pkulaw.com/statics/npm/addlogs'
        }
    })

    var jsUrl = "https://resources.pkulaw.cn/common-resourcse/sdk/js/customSort/release.js?v=1";
    require(['v6-request', jsUrl, 'addlogs'], function ({ store }, { fbCustomSort }, { funlogsCode }) {
        store.set('VUE_APP_KC_BASE_API', 'https://www.pkulaw.com')
        window.CustomSort = function () {
            if (!window.Sorts) {
                window.Sorts = new Vue({
                    el: '#customSort',
                    components: {
                        fbCustomSort: fbCustomSort
                    },
                    data: function () {
                        return {
                            renderData: {
                                token: '',
                                currentEnv: 'test', // 环境配置 1:测试环境 test 2:预发布环境 pre 2:正式环境 prod    
                                afterSearch: false,
                                userInfo: { // 用户信息
                                    name: 'user',
                                    id: '111',
                                    type: 0,
                                },
                                library: 'chl',
                                menuCode: 'law',
                                close: true
                            },
                            show: false,
                        }
                    },
                    methods: {
                        handleCustom: function (sortobj) {
                            this.show = true;
                            this.renderData = sortobj;
                        },
                        submitExpressions(data) {
                            //console.log('data submit', data);
                            // this.show = false
                            var num = data.length;
                            var expressions = data.map(obj => obj.sortValue).join(',');
                            $("input[name='IsCustomSortSearch']").val("True");
                            $("input[name='CustomSortExpression']").val("CustomSort Desc," + expressions);
                            $(".custom-item span").text(num + "个字段排序中")
                            $("#right_form").submit();
                            this.handleSortClose()                          
                        },
                        handleSortClose: function () {
                            this.show = false                            
                        },
                    }
                })
            }
        }
    })
});

