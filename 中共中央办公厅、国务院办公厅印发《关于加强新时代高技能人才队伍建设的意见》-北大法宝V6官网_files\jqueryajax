(function(n){function i(n,t){for(var i=window,r=(n||"").split(".");i&&r.length;)i=i[r.shift()];return typeof i=="function"?i:(t.push(n),Function.constructor.apply(null,t))}function u(n){return n==="GET"||n==="POST"}function o(n,t){u(t)||n.setRequestHeader("X-HTTP-Method-Override",t)}function s(t,i,r){var u;r.indexOf("application/x-javascript")===-1&&(u=(t.getAttribute("data-ajax-mode")||"").toUpperCase(),n(t.getAttribute("data-ajax-update")).each(function(t,r){var f;switch(u){case"BEFORE":f=r.firstChild;n("<div />").html(i).contents().each(function(){r.insertBefore(this,f)});break;case"AFTER":n("<div />").html(i).contents().each(function(){r.appendChild(this)});break;case"REPLACE-WITH":n(r).replaceWith(i);break;default:n(r).html(i)}}))}function f(t,r){var e,h,f,c;(e=t.getAttribute("data-ajax-confirm"),!e||window.confirm(e))&&(h=n(t.getAttribute("data-ajax-loading")),c=parseInt(t.getAttribute("data-ajax-loading-duration"),10)||0,n.extend(r,{type:t.getAttribute("data-ajax-method")||undefined,url:t.getAttribute("data-ajax-url")||undefined,cache:!!t.getAttribute("data-ajax-cache"),beforeSend:function(n){var r;return o(n,f),r=i(t.getAttribute("data-ajax-begin"),["xhr"]).apply(t,arguments),r!==!1&&h.show(c),r},complete:function(){h.hide(c);i(t.getAttribute("data-ajax-complete"),["xhr","status"]).apply(t,arguments)},success:function(n,r,u){s(t,n,u.getResponseHeader("Content-Type")||"text/html");i(t.getAttribute("data-ajax-success"),["data","status","xhr"]).apply(t,arguments)},error:function(){i(t.getAttribute("data-ajax-failure"),["xhr","status","error"]).apply(t,arguments)}}),r.data.push({name:"X-Requested-With",value:"XMLHttpRequest"}),f=r.type.toUpperCase(),u(f)||(r.type="POST",r.data.push({name:"X-HTTP-Method-Override",value:f})),n.ajax(r))}function h(t){var i=n(t).data(e);return!i||!i.validate||i.validate()}var t="unobtrusiveAjaxClick",r="unobtrusiveAjaxClickTarget",e="unobtrusiveValidation";n(document).on("click","a[data-ajax=true]",function(n){n.preventDefault();f(this,{url:this.href,type:"GET",data:[]})});n(document).on("click","form[data-ajax=true] input[type=image]",function(i){var r=i.target.name,u=n(i.target),f=n(u.parents("form")[0]),e=u.offset();f.data(t,[{name:r+".x",value:Math.round(i.pageX-e.left)},{name:r+".y",value:Math.round(i.pageY-e.top)}]);setTimeout(function(){f.removeData(t)},0)});n(document).on("click","form[data-ajax=true] :submit",function(i){var f=i.currentTarget.name,e=n(i.target),u=n(e.parents("form")[0]);u.data(t,f?[{name:f,value:i.currentTarget.value}]:[]);u.data(r,e);setTimeout(function(){u.removeData(t);u.removeData(r)},0)});n(document).on("submit","form[data-ajax=true]",function(i){var e=n(this).data(t)||[],u=n(this).data(r),o=u&&u.hasClass("cancel");(i.preventDefault(),o||h(this))&&f(this,{url:this.action,type:this.method||"GET",data:e.concat(n(this).serializeArray())})})})(jQuery)