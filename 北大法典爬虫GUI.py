from DrissionPage import Chromium, ChromiumPage, ChromiumOptions
import time
import random
import os
import sys
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QVBoxLayout, QHBoxLayout,
                            QWidget, QLabel, QLineEdit, QTextEdit, QFileDialog, QComboBox,
                            QProgressBar, QMessageBox, QGroupBox, QSpinBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QMutex
from PyQt5.QtGui import QFont, QIcon

class CrawlerThread(QThread):
    """爬虫线程，避免界面卡死"""
    update_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int)
    finished_signal = pyqtSignal(bool, str)
    
    def __init__(self, crawler, mode):
        super().__init__()
        self.crawler = crawler
        self.mode = mode
        
    def run(self):
        try:
            # 重置爬虫状态
            self.crawler.state = 1
            
            if self.mode == 'collect':
                self.crawler.collect_urls(self)
                remaining = len(self.crawler.read_urls_from_file())
                self.finished_signal.emit(True, f"本页URL收集完成，当前共有{remaining}个URL待下载")
            elif self.mode == 'download':
                self.crawler.download_content(self)
                remaining = len(self.crawler.read_urls_from_file())
                if remaining > 0:
                    self.finished_signal.emit(True, f"本批次下载完成，还有{remaining}个URL待下载")
                else:
                    self.finished_signal.emit(True, "所有URL已下载完成")
                
        except Exception as e:
            self.update_signal.emit(f"发生错误: {str(e)}")
            self.finished_signal.emit(False, f"爬虫任务失败: {str(e)}")


class PkulawCrawler:
    def __init__(self):
        # 文件路径设置
        # 使用相对路径，这样打包后也能正常工作
        base_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        self.urls_file = os.path.join(base_dir, 'urls.txt')
        self.folder_path = os.path.join(base_dir, 'downloads')

        # 爬虫状态
        self.state = 1

        # 等待时间区间设置
        self.min_wait_time = 0.1
        self.max_wait_time = 10

        # 多线程设置
        self.thread_count = 3  # 默认3个线程
        self.download_mutex = QMutex()  # 用于线程安全的文件操作

        # 确保保存目录存在
        if not os.path.exists(self.folder_path):
            os.makedirs(self.folder_path)

        # 获取Edge浏览器路径
        self.edge_path = self.get_edge_path()

        # 初始化浏览器连接
        self.init_browser()

    def get_edge_path(self):
        """获取Edge浏览器路径"""
        possible_paths = [
            r'C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe',
            r'C:\Program Files\Microsoft\Edge\Application\msedge.exe',
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path

        # 如果都没找到，返回默认路径
        return r'C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe'

    def init_browser(self):
        """初始化浏览器连接"""
        try:
            print("正在连接到Edge浏览器...")
            # 配置Edge浏览器路径
            co = ChromiumOptions()
            co.set_browser_path(self.edge_path)
            page = Chromium(addr_or_opts=co)
            self.page = page.latest_tab
            print(f"已连接到浏览器，当前页面标题: {self.page.title}")
        except Exception as e:
            print(f"连接浏览器时出错: {e}")
            self.page = None
    
    def set_folder_path(self, path):
        """设置保存文件夹路径"""
        self.folder_path = path
        if not os.path.exists(self.folder_path):
            os.makedirs(self.folder_path)
    
    def collect_urls(self, thread=None):
        """收集列表页面的URL"""
        if thread:
            thread.update_signal.emit("开始收集URL...")
        else:
            print("开始收集URL...")
        
        try:
            # 每次点击按钮时重新连接浏览器
            if thread:
                thread.update_signal.emit("正在连接到Edge浏览器...")
            else:
                print("正在连接到Edge浏览器...")

            # 配置Edge浏览器路径
            co = ChromiumOptions()
            co.set_browser_path(self.edge_path)
            browser = Chromium(addr_or_opts=co)
            page = browser.latest_tab
            
            if thread:
                thread.update_signal.emit(f'当前页面标题: {page.title}')
            else:
                print(f'当前页面标题: {page.title}')
            
            # 获取已存储的URL集合
            existing_urls = self.read_urls_from_file()
            
            # 获取页面上的元素
            target = page.ele('tag:tbody')
            if not target:
                if thread:
                    thread.update_signal.emit("未找到tbody元素，请确认页面已正确加载")
                else:
                    print("未找到tbody元素，请确认页面已正确加载")
                return
                
            items = target.eles('tag:tr')
            
            # 计数器
            new_count = 0
            total_items = len(items)
            
            for i, item in enumerate(items):
                url = item.ele('tag:a').attr('href')
                if url not in existing_urls:
                    # 添加到集合并写入文件
                    existing_urls.add(url)
                    self.append_url_to_file(url)
                    if thread:
                        thread.update_signal.emit(f'添加成功: {url}')
                    else:
                        print(f'添加成功: {url}')
                    new_count += 1
                else:
                    if thread:
                        thread.update_signal.emit(f'已存在: {url}')
                    else:
                        print(f'已存在: {url}')
                
                # 更新进度
                if thread:
                    progress = int((i + 1) / total_items * 100)
                    thread.progress_signal.emit(progress)
            
            if thread:
                thread.update_signal.emit(f"URL收集完成，新增{new_count}个URL")
            else:
                print(f"URL收集完成，新增{new_count}个URL")
            
        except Exception as e:
            if thread:
                thread.update_signal.emit(f"收集URL时出错: {e}")
            else:
                print(f"收集URL时出错: {e}")
    
    def download_single_url(self, url, thread=None):
        """下载单个URL的内容"""
        try:
            # 等待时间
            wait_time = random.uniform(self.min_wait_time, self.max_wait_time)
            if thread:
                thread.update_signal.emit(f"等待{wait_time:.1f}秒...")
            time.sleep(wait_time)

            if self.state == 0:
                return False, "程序被中断"

            # 创建新的浏览器实例用于下载
            co = ChromiumOptions()
            co.set_browser_path(self.edge_path)
            page = ChromiumPage(addr_or_opts=co)

            try:
                # 下载内容
                page.get(url)
                time.sleep(1)  # 减少等待时间

                # 尝试点击"继续阅读"按钮获取完整内容
                try:
                    continue_btn = page.ele('#continueTips')
                    if continue_btn:
                        continue_btn.click()
                        time.sleep(2)  # 减少等待时间
                except:
                    pass

                # 获取文本内容
                wenben_element = page.ele('#divFullText')
                if not wenben_element:
                    selectors = ['.fulltext', '.fulltext-wrap', '.content', '.main-content']
                    for selector in selectors:
                        wenben_element = page.ele(selector)
                        if wenben_element:
                            break

                if not wenben_element:
                    return False, f"未找到内容元素: {url}"

                wenben = wenben_element.text
                if not wenben or not wenben.strip():
                    return False, f"内容为空: {url}"

                # 获取标题
                title = None
                try:
                    title_element = page.ele('.content .title')
                    if title_element:
                        title = title_element.text.strip()
                except:
                    pass

                if not title:
                    try:
                        page_title = page.title
                        if page_title and '北大法宝' in page_title:
                            title = page_title.replace('-北大法宝V6官网', '').replace('北大法宝V6官网', '').strip()
                    except:
                        pass

                if not title:
                    try:
                        wenben_lines = wenben.split('\n')
                        for line in wenben_lines:
                            line = line.strip()
                            if line and len(line) > 5:
                                title = line
                                break
                    except:
                        pass

                if not title:
                    title = f"文档_{url.split('/')[-1]}"

                # 处理文件名中的非法字符
                for c in "*?:<>|/\\":
                    if c in title:
                        title = title.replace(c, '某')

                # 线程安全的文件保存
                self.download_mutex.lock()
                try:
                    file_path = os.path.join(self.folder_path, f'{title}.txt')
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(wenben)

                    # 下载成功后从文件中删除该URL
                    self.remove_url_from_file(url)
                finally:
                    self.download_mutex.unlock()

                return True, title

            finally:
                page.quit()

        except Exception as e:
            return False, f'下载失败 {url}: {e}'

    def download_content(self, thread=None):
        """使用多线程下载URL对应的详细内容"""
        # 重置爬虫状态，确保每次下载都是从正常状态开始
        self.state = 1

        if thread:
            thread.update_signal.emit("开始多线程下载内容...")
        else:
            print("开始多线程下载内容...")
        
        # 读取所有URL
        urls = self.read_urls_from_file()

        if not urls:
            if thread:
                thread.update_signal.emit("没有URL需要下载")
            else:
                print("没有URL需要下载")
            return

        total_urls = len(urls)
        if thread:
            thread.update_signal.emit(f"共有{total_urls}个URL等待下载，使用{self.thread_count}个线程")
        else:
            print(f"共有{total_urls}个URL等待下载，使用{self.thread_count}个线程")

        # 下载计数
        success_count = 0
        failed_count = 0

        # 设置每次下载的URL数量限制
        batch_size = 50  # 减少批次大小，提高响应速度
        urls_list = list(urls)[:batch_size]

        # 使用线程池进行多线程下载
        with ThreadPoolExecutor(max_workers=self.thread_count) as executor:
            # 提交所有下载任务
            future_to_url = {executor.submit(self.download_single_url, url, thread): url for url in urls_list}

            # 处理完成的任务
            for i, future in enumerate(as_completed(future_to_url)):
                if self.state == 0:
                    if thread:
                        thread.update_signal.emit("程序被中断，取消剩余任务")
                    else:
                        print("程序被中断，取消剩余任务")
                    # 取消未完成的任务
                    for f in future_to_url:
                        f.cancel()
                    break

                url = future_to_url[future]
                try:
                    success, result = future.result()
                    if success:
                        success_count += 1
                        if thread:
                            thread.update_signal.emit(f"下载成功: {result}")
                        else:
                            print(f"下载成功: {result}")
                    else:
                        failed_count += 1
                        if thread:
                            thread.update_signal.emit(f"下载失败: {result}")
                        else:
                            print(f"下载失败: {result}")
                except Exception as e:
                    failed_count += 1
                    if thread:
                        thread.update_signal.emit(f'下载异常 {url}: {e}')
                    else:
                        print(f'下载异常 {url}: {e}')

                # 更新进度
                if thread:
                    progress = int((i + 1) / len(urls_list) * 100)
                    thread.progress_signal.emit(progress)

        remaining = len(self.read_urls_from_file())
        if thread:
            thread.update_signal.emit(f"多线程下载完成，成功{success_count}个，失败{failed_count}个，还剩{remaining}个URL待下载")
        else:
            print(f"多线程下载完成，成功{success_count}个，失败{failed_count}个，还剩{remaining}个URL待下载")
    
    def read_urls_from_file(self):
        """从文件读取URL集合"""
        urls = set()
        if os.path.exists(self.urls_file):
            with open(self.urls_file, 'r', encoding='utf-8') as f:
                for line in f:
                    url = line.strip()
                    if url:
                        urls.add(url)
        return urls
    
    def append_url_to_file(self, url):
        """将URL追加到文件"""
        with open(self.urls_file, 'a', encoding='utf-8') as f:
            f.write(url + '\n')
    
    def remove_url_from_file(self, url_to_remove):
        """从文件中删除指定URL（线程安全）"""
        self.download_mutex.lock()
        try:
            urls = self.read_urls_from_file()
            urls.discard(url_to_remove)

            with open(self.urls_file, 'w', encoding='utf-8') as f:
                for url in urls:
                    f.write(url + '\n')
        finally:
            self.download_mutex.unlock()


class PkulawCrawlerGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.crawler = PkulawCrawler()
        self.init_ui()
        
    def init_ui(self):
        """初始化UI界面"""
        self.setWindowTitle('北大法宝爬虫 - by hjhhoni')
        self.setGeometry(300, 300, 800, 600)
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                border: 1px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4a86e8;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #3a76d8;
            }
            QPushButton:pressed {
                background-color: #2a66c8;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QLineEdit, QComboBox {
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 6px;
                background-color: white;
            }
            QTextEdit {
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: white;
                font-family: Consolas, Monaco, monospace;
            }
            QProgressBar {
                border: 1px solid #cccccc;
                border-radius: 4px;
                text-align: center;
                background-color: #f0f0f0;
            }
            QProgressBar::chunk {
                background-color: #4a86e8;
                width: 10px;
                margin: 0.5px;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 设置组
        settings_group = QGroupBox("设置")
        settings_layout = QVBoxLayout(settings_group)
        
        # 文件夹选择
        folder_layout = QHBoxLayout()
        folder_label = QLabel("保存文件夹:")
        self.folder_edit = QLineEdit(self.crawler.folder_path)
        self.folder_edit.setReadOnly(True)
        browse_button = QPushButton("浏览...")
        browse_button.setFixedWidth(100)
        browse_button.clicked.connect(self.browse_folder)
        
        folder_layout.addWidget(folder_label)
        folder_layout.addWidget(self.folder_edit)
        folder_layout.addWidget(browse_button)
        settings_layout.addLayout(folder_layout)
        
        # URL文件路径
        url_layout = QHBoxLayout()
        url_label = QLabel("URL文件:")
        self.url_edit = QLineEdit(self.crawler.urls_file)
        self.url_edit.setReadOnly(True)
        url_layout.addWidget(url_label)
        url_layout.addWidget(self.url_edit)
        settings_layout.addLayout(url_layout)
        
        # 等待时间设置
        wait_time_layout = QHBoxLayout()
        wait_time_label = QLabel("等待时间区间(秒):")
        self.min_wait_edit = QLineEdit(str(self.crawler.min_wait_time))
        self.min_wait_edit.setFixedWidth(50)
        wait_time_to_label = QLabel("到")
        self.max_wait_edit = QLineEdit(str(self.crawler.max_wait_time))
        self.max_wait_edit.setFixedWidth(50)
        wait_time_layout.addWidget(wait_time_label)
        wait_time_layout.addWidget(self.min_wait_edit)
        wait_time_layout.addWidget(wait_time_to_label)
        wait_time_layout.addWidget(self.max_wait_edit)
        wait_time_layout.addStretch()
        settings_layout.addLayout(wait_time_layout)

        # 线程数量设置
        thread_layout = QHBoxLayout()
        thread_label = QLabel("下载线程数:")
        self.thread_count_spin = QSpinBox()
        self.thread_count_spin.setMinimum(1)
        self.thread_count_spin.setMaximum(10)
        self.thread_count_spin.setValue(self.crawler.thread_count)
        self.thread_count_spin.setFixedWidth(60)
        thread_tip_label = QLabel("(建议1-5个，过多可能被限制)")
        thread_tip_label.setStyleSheet("color: #666666; font-size: 12px;")
        thread_layout.addWidget(thread_label)
        thread_layout.addWidget(self.thread_count_spin)
        thread_layout.addWidget(thread_tip_label)
        thread_layout.addStretch()
        settings_layout.addLayout(thread_layout)
        
        main_layout.addWidget(settings_group)
        
        # 操作按钮组
        action_group = QGroupBox("操作")
        action_layout = QHBoxLayout(action_group)
        
        self.collect_button = QPushButton("收集URL")
        self.collect_button.clicked.connect(lambda: self.start_crawler('collect'))
        
        self.download_button = QPushButton("下载内容")
        self.download_button.clicked.connect(lambda: self.start_crawler('download'))
        
        self.stop_button = QPushButton("停止爬虫")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_crawler)
        
        action_layout.addWidget(self.collect_button)
        action_layout.addWidget(self.download_button)
        action_layout.addWidget(self.stop_button)
        
        main_layout.addWidget(action_group)
        
        # 进度条
        progress_group = QGroupBox("进度")
        progress_layout = QVBoxLayout(progress_group)
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(self.progress_bar)
        
        main_layout.addWidget(progress_group)
        
        # 日志输出
        log_group = QGroupBox("日志")
        log_layout = QVBoxLayout(log_group)
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        main_layout.addWidget(log_group)
        
        # 添加版权信息
        copyright_label = QLabel("©2025 hjhhoni. All Rights Reserved.")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("color: #888888; font-size: 17px; font-weight: bold;")
        main_layout.addWidget(copyright_label)
        
        # 状态栏
        self.statusBar().showMessage('就绪')
        
        # 显示窗口
        self.show()
        
        # 初始日志
        if self.crawler.page:
            self.log(f"已连接到浏览器，当前页面标题: {self.crawler.page.title}")
            self.log("请导航到目标页面，然后点击'收集URL'按钮")
        else:
            self.log("连接浏览器失败，请确保Edge浏览器已安装")
    
    def browse_folder(self):
        """浏览并选择保存文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择保存文件夹", self.crawler.folder_path)
        if folder:
            self.folder_edit.setText(folder)
            self.crawler.set_folder_path(folder)
            self.log("已设置保存文件夹: " + folder)
    
    def log(self, message):
        """添加日志消息"""
        self.log_text.append(message)
        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
    
    def start_crawler(self, mode):
        """启动爬虫"""
        # 更新等待时间设置
        try:
            min_wait = float(self.min_wait_edit.text())
            max_wait = float(self.max_wait_edit.text())
            if min_wait > 0 and max_wait >= min_wait:
                self.crawler.min_wait_time = min_wait
                self.crawler.max_wait_time = max_wait
            else:
                self.log("等待时间设置无效，使用默认值")
                self.min_wait_edit.setText(str(0.1))
                self.max_wait_edit.setText(str(2))
                self.crawler.min_wait_time = 0.1
                self.crawler.max_wait_time = 2
        except ValueError:
            self.log("等待时间必须是数字，使用默认值")
            self.min_wait_edit.setText(str(0.1))
            self.max_wait_edit.setText(str(2))
            self.crawler.min_wait_time = 0.1
            self.crawler.max_wait_time = 2

        # 更新线程数量设置
        self.crawler.thread_count = self.thread_count_spin.value()
        self.log(f"设置下载线程数: {self.crawler.thread_count}")
        
        # 重置爬虫状态
        self.crawler.state = 1
        
        self.collect_button.setEnabled(False)
        self.download_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setValue(0)
        
        # 创建线程并连接信号
        self.crawler_thread = CrawlerThread(self.crawler, mode)
        self.crawler_thread.update_signal.connect(self.log)
        self.crawler_thread.progress_signal.connect(self.update_progress)
        self.crawler_thread.finished_signal.connect(self.crawler_finished)
        self.crawler_thread.start()
    
    def stop_crawler(self):
        """停止爬虫"""
        self.crawler.state = 0
        self.log("正在停止爬虫...")
        self.stop_button.setEnabled(False)
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def crawler_finished(self, success, message):
        """爬虫完成回调"""
        self.log(message)
        self.collect_button.setEnabled(True)
        self.download_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setValue(0)
        self.statusBar().showMessage('就绪')
        
        if success:
            QMessageBox.information(self, "完成", message)
        else:
            QMessageBox.warning(self, "错误", message)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    gui = PkulawCrawlerGUI()
    sys.exit(app.exec_())