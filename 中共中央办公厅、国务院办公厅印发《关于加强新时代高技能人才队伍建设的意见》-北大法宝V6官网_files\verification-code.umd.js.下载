!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.fbVerificationCode=t()}(this,function(){"use strict";function e(e,t){return e(t={exports:{}},t.exports),t.exports}function t(e){return n.stringify.apply(n,arguments)}var a=e(function(e){e=e.exports={version:"2.6.12"};"number"==typeof __e&&(__e=e)}),n=(a.version,a.JSON||(a.JSON={stringify:JSON.stringify})),a=e(function(e){e.exports={default:t,__esModule:!0}}),o=(a=a)&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a,r={appId:"196335483",addScript:function(){var e;"function"==typeof TencentCaptcha?console.log("已经加载过这个js了"):((e=document.createElement("script")).type="text/javascript",e.src="https://turing.captcha.qcloud.com/TCaptcha.js",document.getElementsByTagName("head")[0].appendChild(e))},captcha:null,validationUrl:"",validationCallback:null,validationErrorCallback:null,validaType:null,validation:function(e,t){r.validaType=e;try{r.captcha=new TencentCaptcha(t&&t.appId||this.appId,this.callback,{needFeedBack:!1,loading:!0}),r.captcha.show()}catch(e){this.loadErrorCallback()}},callback:function(e){if(0!=e.ret||e.errorCode)2==e.ret&&r.validationErrorCallback();else if("2"==r.validaType)return r.validationCallback(e),!1;e.ticket},loadErrorCallback:function(){var e="terror_1001_"+this.appId+"_"+Math.floor((new Date).getTime()/1e3);this.callback({ret:0,randstr:"@"+Math.random().toString(36).substr(2),ticket:e,errorCode:1001,errorMessage:"jsload_error"})},Errorcallback:function(e){var t;console.log("callback:",e),0===e.ret&&(e="【randstr】->【"+e.randstr+"】      【ticket】->【"+e.ticket+"】",(t=document.createElement("input")).value=e,document.body.appendChild(t),t.select(),document.body.removeChild(t))},loginAjax:function(t){var a,e;if((t=t||{}).type=(t.type||"GET").toUpperCase(),t.url=t.url||"",t.async=t.async||!0,t.data=t.data||null,t.dataType=t.dataType||"json",t.contentType=t.contentType||"application/x-www-form-urlencoded",a=window.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP"),"application/x-www-form-urlencoded"==t.contentType){for(var n in e=[],t.data)e.push(n+"="+t.data[n]);e=e.join("&")}else e=o(t.data);a.onreadystatechange=function(){var e;4==a.readyState&&(200<=(e=a.status)&&e<300?(console.log(a.responseText),t.success&&t.success(a.responseText)):t.error&&t.error(e))},"GET"==t.type?(a.open("GET",t.url+"?"+e,t.async),a.send(null)):"POST"==t.type&&(a.open("POST",t.url,t.async),a.setRequestHeader("Content-Type",t.contentType),a.send(e))}};return r});
