# 北大法宝爬虫

## 更新
- 2025年8月8日：附件下载版本发布（结决非IP登录下打开新标签页丢失登录信息的问题以及支持附件同名下载）


## 项目简介

北大法宝爬虫是一个用于自动收集和下载北大法宝网站法律法规文档的工具。该工具提供了友好的图形用户界面，支持URL收集和内容下载功能，并能够自动下载文档附件。

## 功能特性

- **URL收集**：自动从北大法宝网站的高级检索后更多列表页面收集法律法规的URL
- **内容下载**：下载法律法规的详细内容，并保存为文本文件
- **附件下载**：自动识别并下载法律法规关联的附件文件
- **断点续传**：支持中断后继续下载，已下载的内容不会重复下载
- **随机等待**：内置随机等待时间，避免频繁请求被网站封禁
- **进度显示**：实时显示下载进度和日志信息
- **错误处理**：完善的错误处理机制，网络异常时会自动中断并提示

## 技术架构

- **前端界面**：使用PyQt5构建图形用户界面
- **浏览器自动化**：使用DrissionPage库控制Chrome浏览器
- **多线程处理**：使用QThread实现后台任务处理，避免界面卡死
- **文件操作**：支持文本文件的读写和管理

## 打包成exe文件
- 安装pyinstaller
  ```
  pip install pyinstaller
  ```
- 打包
  ```
  python build_exe.py
  ```
- 打包完成后，在dist文件夹中即可找到可执行文件
- 切换版本支付改build_exe.py的绝对路径即可，改成对应版本的py文件的绝对路径



## 使用方法
### 界面预览
<img src="https://img.alicdn.com/bao/uploaded/i1/O1CN015SZXmB1GEj8s9JXu4_!!4611686018427383567-53-fleamarket.heic_Q90.jpg" alt="界面预览"></img>
### 演示视频
<iframe src="https://xianyu-video.alicdn.com/aus/xianyu_item/1811478524/ff7f208d127f4ff38a9f71ed1f003b4e.mp4" 
        scrolling="no" 
        border="0" 
        frameborder="no" 
        framespacing="0" 
        allowfullscreen="true"> 
</iframe>
Gitee无法引入外链视频，请访问[闲鱼商品链接](https://www.goofish.com/item?spm=a21ybx.personal.feeds.2.5d1e6ac2lRHgaw&id=945035916410&categoryId=50023914)查看演示视频。

### 环境准备

1. 安装Chrome浏览器
2. 启动Chrome浏览器的远程调试模式（端口9333）
   ```
   chrome.exe --remote-debugging-port=9333
   ```

### 基本操作

1. **启动程序**：运行北大法宝爬虫下载附件版GUI.py或编译后的可执行文件
2. **设置保存路径**：点击「浏览...」按钮选择文件保存位置
3. **设置等待时间**：设置每次请求之间的随机等待时间区间（秒）
4. **收集URL**：
   - 在Chrome浏览器中打开北大法宝网站并登录
   - 导航到需要爬取的法律法规列表页面
   - 返回爬虫程序，点击「收集URL」按钮
5. **下载内容**：
   - 点击「下载内容」按钮开始下载
   - 程序会自动下载文本内容和相关附件
   - 下载过程中可以通过「停止爬虫」按钮中断

### 注意事项

- 爬取的URL会保存在程序目录下的urls.txt文件中
- 下载的内容默认保存在程序目录下的downloads文件夹中
- 下载成功后，对应的URL会从urls.txt文件中删除，实现断点续传
- 文件名中的非法字符会被替换为「某」字符
- 若需要更多的定制工作可以访问闲鱼商品：https://www.goofish.com/item?spm=a21ybx.personal.feeds.2.5d1e6ac2lRHgaw&id=945035916410&categoryId=50023914

## 界面说明

- **设置区域**：配置保存路径、URL文件路径和等待时间
- **操作按钮**：收集URL、下载内容和停止爬虫
- **进度条**：显示当前操作的进度
- **日志区域**：显示程序运行的详细日志信息

## 开发者信息

- 作者：hjhhoni(github同名)
- 版权所有 © 2025 hjhhoni. All Rights Reserved.
- qq：2228788600
- vx：15347611393

## 免责声明

本工具仅供学习和研究使用，请勿用于任何商业用途。使用本工具时请遵守相关法律法规和网站的使用条款，由使用不当造成的任何问题，开发者不承担任何责任。

