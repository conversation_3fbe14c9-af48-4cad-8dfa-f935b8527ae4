
function processData(arr) {
    arr.forEach(item => {
        Object.assign(item, {
            topic: `${item.name} (${item.value})`
        })
        if (item.children && item.children.length > 0) {
            processData(item.children);
        }
    });
}
function formatData(origin_data) {
    // 设计图上第二层的背景色不同，所以需要单独设置一下
    var arr = JSON.parse(JSON.stringify(origin_data));
    arr.forEach((item, index) => {
        Object.assign(item, {
            "background-color": "#218FC4",
            "foreground-color": "#fff",
            "leading-line-color": "#C8DDE7",
            "topic": `${item.name} (${item.value})`,
            "direction": index > 2 ? "left" : "right"
        })
        if(item.children && item.children.length > 0) {
            processData(item.children)
        }
    })
    return arr
}
/**
 * 封装jsmind
 */
function jsMindConfig(dom, data, callback) {
    var sData = formatData(data[0].children)
    // 处理数据
    var jmdata = Object.assign({
        "id": "root",
        "topic": data[0].name,
        children: sData || []
    })
    var mind = {
        meta: {
        }, // 这个属性必须要有，我也不知道为啥
        format: "node_tree",
        data: jmdata || []
    };
    // 处理配置
    var options = {
        container: dom, // 容器id
        editable: false, //是否启用编辑
        theme: 'fbfullText',
        view: {
            node_overflow: 'wrap', // 节点文本过长时的样式, hidden 为超出显示...
            line_width: 2, // 线条宽度
            line_color: '#C0E2FF' // 线条颜色
        }
    }
    // 生成
    var jm = new jsMind(options);
    jm.show(mind);
    var clickEnv = callback && callback.click
    jm.add_event_listener(function(type, data, topic) {
        if(type == '4' && clickEnv) {
            clickEnv(data)
        }
    })
    // 可接收也可不接收
    // return jm
}

function echartsSunburst(dom, data) {
    if (!window.echarts) return false
    var myChart = echarts.init(dom);
    var option = {
        series: {
            type: 'sunburst',
            emphasis: {
                focus: 'ancestor'// 在高亮图形时，是否淡出其它数据的图形已达到聚焦的效果
            },
            data: data,
            radius: [0, '90%'],
            label: {
                rotate: 'radial'
            },
            levels: [{}, {
                itemStyle: {
                    color: '#9f1514'
                },
                label: {
                    rotate: '0',
                    color: '#fff',
                    width: 80,
                    height: 20,
                    lineHeight: 24,
                    overflow: "breakAll",
                }
            },
            {
                itemStyle: {
                    color: '#e05718'
                },
                label: {
                    rotate: '0',
                    color: '#fff',
                    width: 30,
                    height: 20,
                    lineHeight: 14,
                    overflow: "breakAll",
                }
            },
            {
                itemStyle: {
                    color: '#f9aa50'
                },
                label: {
                    rotate: 0,
                    color: '#fff',
                    width: 30,
                    height: 20,
                    lineHeight: 14,
                    overflow: "breakAll",
                }
            }, {
                itemStyle: {
                    color: '#fad968'
                },
                label: {
                    rotate: 0,
                    color: '#fff',
                    width: 30,
                    height: 20,
                    lineHeight: 14,
                    overflow: "breakAll",
                }
            }]
        }
    }
    option && myChart.setOption(option);
}