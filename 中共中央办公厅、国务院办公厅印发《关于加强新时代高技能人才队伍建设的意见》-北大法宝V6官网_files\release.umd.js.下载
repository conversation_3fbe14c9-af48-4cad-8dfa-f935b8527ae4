!function(n,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r():"function"==typeof define&&define.amd?define(r):((n="undefined"!=typeof globalThis?globalThis:n||self).fbModule=n.fbModule||{},n.fbModule.knowledge=r())}(this,function(){"use strict";var n={name:"index",props:{type:{type:String},size:{type:String},round:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},methods:{handleClick(){this.disabled||this.$emit("handleClick")}}};function r(n,r,e,t,o,a,i,s,d,l){const A="function"==typeof e?e.options:e;let p;if(n&&n.render&&(A.render=n.render,A.staticRenderFns=n.staticRenderFns,A._compiled=!0),t&&(A._scopeId=t),r&&(p=function(n){r.call(this,s(n))}),p)if(A.functional){const n=A.render;A.render=function(r,e){return p.call(e),n(r,e)}}else{const n=A.beforeCreate;A.beforeCreate=n?[].concat(n,p):[p]}return e}const e="undefined"!=typeof navigator&&/msie [6-9]\\b/.test(navigator.userAgent.toLowerCase());function t(n){return(n,r)=>function(n,r){const t=e?r.media||"default":n,i=a[t]||(a[t]={ids:new Set,styles:[]});if(!i.ids.has(n)){i.ids.add(n);let e=r.source;if(r.map&&(e+="\n/*# sourceURL="+r.map.sources[0]+" */",e+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(r.map))))+" */"),i.element||(i.element=document.createElement("style"),i.element.type="text/css",r.media&&i.element.setAttribute("media",r.media),void 0===o&&(o=document.head||document.getElementsByTagName("head")[0]),o.appendChild(i.element)),"styleSheet"in i.element)i.styles.push(e),i.element.styleSheet.cssText=i.styles.filter(Boolean).join("\n");else{const n=i.ids.size-1,r=document.createTextNode(e),t=i.element.childNodes;t[n]&&i.element.removeChild(t[n]),t.length?i.element.insertBefore(r,t[n]):i.element.appendChild(r)}}}(n,r)}let o;const a={};const i=n;var s=function(){var n=this;return(n._self._c||n.$createElement)("div",{staticClass:"btnModule",class:[n.type,n.size,n.round?"round":"",n.disabled?"disabled":""],on:{click:n.handleClick}},[n._t("default")],2)};s._withStripped=!0;const d={name:"index",components:{BtnModule:r({render:s,staticRenderFns:[]},function(n){n&&n("data-v-26e6ecb6_0",{source:".btnModule[data-v-26e6ecb6] {\n  cursor: pointer;\n  display: inline-block;\n  border-radius: 6px;\n  border: 1px solid rgb(33, 143, 196);\n  background-color: #fff;\n  text-align: center;\n  color: rgb(33, 143, 196);\n  padding: 9px 20px;\n  font-size: 14px;\n  line-height: 1;\n  transition: 0.1s;\n  font-weight: 500;\n}\n.btnModule[data-v-26e6ecb6]:hover {\n  color: rgb(33, 143, 196);\n  border-color: #c6e2ff;\n  background-color: #ecf5ff;\n}\n.btnModule.round[data-v-26e6ecb6] {\n  border-radius: 17px;\n}\n.btnModule.primary[data-v-26e6ecb6] {\n  color: #fff;\n  background-color: rgb(33, 143, 196);\n  border-color: rgb(33, 143, 196);\n}\n.btnModule.primary[data-v-26e6ecb6]:hover {\n  background: rgba(33, 143, 196, 0.9);\n  border-color: rgba(33, 143, 196, 0.9);\n}\n.btnModule.large[data-v-26e6ecb6] {\n  padding: 12px 20px;\n}\n.btnModule.large.round[data-v-26e6ecb6] {\n  border-radius: 19px;\n}\n.btnModule.small[data-v-26e6ecb6] {\n  padding: 8px 15px;\n  font-size: 12px;\n  border-radius: 4px;\n}\n.btnModule.small.round[data-v-26e6ecb6] {\n  border-radius: 14px;\n}\n.btnModule.mini[data-v-26e6ecb6] {\n  padding: 5px 10px;\n  font-size: 12px;\n  border-radius: 4px;\n}\n.btnModule.mini.round[data-v-26e6ecb6] {\n  border-radius: 11px;\n}\n.btnModule.text[data-v-26e6ecb6] {\n  border: 1px solid transparent;\n  padding: 0;\n}\n.btnModule.text[data-v-26e6ecb6]:hover {\n  color: rgba(33, 143, 196, 0.8);\n  border-color: transparent;\n  background-color: transparent;\n}\n.btnModule.disabled[data-v-26e6ecb6] {\n  cursor: not-allowed;\n  color: rgb(144, 147, 153);\n  border-color: rgb(144, 147, 153);\n  background-color: rgb(248, 248, 250);\n}\n\n/*# sourceMappingURL=btn.vue.map */",map:{version:3,sources:["C:\\Users\\<USER>\\Desktop\\Knowledge\\src\\module\\btn.vue","btn.vue"],names:[],mappings:"AAmCA;EACA,eAAA;EACA,qBAAA;EACA,kBAAA;EACA,mCAAA;EACA,sBAAA;EACA,kBAAA;EACA,wBAAA;EACA,iBAAA;EACA,eAAA;EACA,cAAA;EACA,gBAAA;EACA,gBAAA;AClCA;ADmCA;EACA,wBAAA;EACA,qBAAA;EACA,yBAAA;ACjCA;ADmCA;EACA,mBAAA;ACjCA;ADmCA;EACA,WAAA;EACA,mCAAA;EACA,+BAAA;ACjCA;ADkCA;EACA,mCAAA;EACA,qCAAA;AChCA;ADyCA;EACA,kBAAA;ACvCA;ADwCA;EACA,mBAAA;ACtCA;ADyCA;EACA,iBAAA;EACA,eAAA;EACA,kBAAA;ACvCA;ADwCA;EACA,mBAAA;ACtCA;ADyCA;EACA,iBAAA;EACA,eAAA;EACA,kBAAA;ACvCA;ADwCA;EACA,mBAAA;ACtCA;ADyCA;EACA,6BAAA;EACA,UAAA;ACvCA;ADwCA;EACA,8BAAA;EACA,yBAAA;EACA,6BAAA;ACtCA;ADyCA;EACA,mBAAA;EACA,yBAAA;EACA,gCAAA;EACA,oCAAA;ACvCA;;AAEA,kCAAkC",file:"btn.vue",sourcesContent:["<template>\r\n    <div class=\"btnModule\" :class=\"[type, size, round ? 'round' : '', disabled ? 'disabled' : '']\" @click=\"handleClick\">\r\n        <slot />\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: 'index',\r\n    props: {\r\n        type: {\r\n            type: String\r\n        },\r\n        size: {\r\n            type: String\r\n        },\r\n        round: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        disabled: {\r\n            type: Boolean,\r\n            default: false\r\n        }\r\n    },\r\n    methods: {\r\n        handleClick(){\r\n            if(this.disabled) return;\r\n            this.$emit('handleClick')\r\n        }\r\n    }\r\n}\r\n<\/script>\r\n\r\n<style lang=\"scss\" scoped>\r\n    .btnModule{\r\n        cursor: pointer;\r\n        display: inline-block;\r\n        border-radius: 6px;\r\n        border: 1px solid rgba(33, 143, 196, 1);\r\n        background-color: #fff;\r\n        text-align: center;\r\n        color: rgba(33, 143, 196, 1);\r\n        padding: 9px 20px;\r\n        font-size: 14px;\r\n        line-height: 1;\r\n        transition: .1s;\r\n        font-weight: 500;\r\n        &:hover{\r\n            color: rgba(33, 143, 196, 1);\r\n            border-color: #c6e2ff;\r\n            background-color: #ecf5ff;\r\n        }\r\n        &.round{\r\n            border-radius: 17px;\r\n        }\r\n        &.primary{\r\n            color: #fff;\r\n            background-color: rgba(33, 143, 196, 1);\r\n            border-color: rgba(33, 143, 196, 1);\r\n            &:hover{\r\n                background: rgba(33, 143, 196, .9);\r\n                border-color: rgba(33, 143, 196, .9);\r\n            }\r\n            // &.disabled{\r\n            //     cursor: not-allowed;\r\n            //     color: #fff;\r\n            //     background-color: rgba(33, 143, 196, 1);\r\n            //     border-color: rgba(33, 143, 196, 1);\r\n            // }\r\n        }\r\n        &.large{\r\n            padding: 12px 20px;\r\n            &.round{\r\n                border-radius: 19px;\r\n            }\r\n        }\r\n        &.small{\r\n            padding: 8px 15px;\r\n            font-size: 12px;\r\n            border-radius: 4px;\r\n            &.round{\r\n                border-radius: 14px;\r\n            }\r\n        }\r\n        &.mini{\r\n            padding: 5px 10px;\r\n            font-size: 12px;\r\n            border-radius: 4px;\r\n            &.round{\r\n                border-radius: 11px;\r\n            }\r\n        }\r\n        &.text{\r\n            border: 1px solid transparent;\r\n            padding: 0;\r\n            &:hover{\r\n                color: rgba(33, 143, 196, .8);\r\n                border-color: transparent;\r\n                background-color: transparent;\r\n            }\r\n        }\r\n        &.disabled{\r\n            cursor: not-allowed;\r\n            color: rgba(144, 147, 153, 1);\r\n            border-color: rgba(144, 147, 153, 1);\r\n            background-color: rgba(248, 248, 250, 1);\r\n        }\r\n    }\r\n</style>",".btnModule {\n  cursor: pointer;\n  display: inline-block;\n  border-radius: 6px;\n  border: 1px solid rgb(33, 143, 196);\n  background-color: #fff;\n  text-align: center;\n  color: rgb(33, 143, 196);\n  padding: 9px 20px;\n  font-size: 14px;\n  line-height: 1;\n  transition: 0.1s;\n  font-weight: 500;\n}\n.btnModule:hover {\n  color: rgb(33, 143, 196);\n  border-color: #c6e2ff;\n  background-color: #ecf5ff;\n}\n.btnModule.round {\n  border-radius: 17px;\n}\n.btnModule.primary {\n  color: #fff;\n  background-color: rgb(33, 143, 196);\n  border-color: rgb(33, 143, 196);\n}\n.btnModule.primary:hover {\n  background: rgba(33, 143, 196, 0.9);\n  border-color: rgba(33, 143, 196, 0.9);\n}\n.btnModule.large {\n  padding: 12px 20px;\n}\n.btnModule.large.round {\n  border-radius: 19px;\n}\n.btnModule.small {\n  padding: 8px 15px;\n  font-size: 12px;\n  border-radius: 4px;\n}\n.btnModule.small.round {\n  border-radius: 14px;\n}\n.btnModule.mini {\n  padding: 5px 10px;\n  font-size: 12px;\n  border-radius: 4px;\n}\n.btnModule.mini.round {\n  border-radius: 11px;\n}\n.btnModule.text {\n  border: 1px solid transparent;\n  padding: 0;\n}\n.btnModule.text:hover {\n  color: rgba(33, 143, 196, 0.8);\n  border-color: transparent;\n  background-color: transparent;\n}\n.btnModule.disabled {\n  cursor: not-allowed;\n  color: rgb(144, 147, 153);\n  border-color: rgb(144, 147, 153);\n  background-color: rgb(248, 248, 250);\n}\n\n/*# sourceMappingURL=btn.vue.map */"]},media:void 0})},i,"data-v-26e6ecb6",0,0,0,t)},props:{dialogVisible:{type:Boolean,default:!0},title:{type:String,default:"提示"},width:{type:String}},computed:{dialogStyle(){let n={};return this.width&&(n.width=this.width),n}},mounted(){document.body.classList.add("dialog-sub-scroll")},methods:{close(){this.$emit("close")},confirm(){this.$emit("confirm")}}};var l=function(){var n=this,r=n._self._c||n.$createElement;return n.dialogVisible?r("div",{staticClass:"dialogModule",style:n.dialogStyle},[r("div",{staticClass:"dialog-head"},[n._t("head",function(){return[r("span",{staticClass:"dialog-title"},[n._v(n._s(n.title))])]}),n._v(" "),r("span",{staticClass:"iconfont icon-guanbi-icon dialog-close",on:{click:n.close}})],2),n._v(" "),r("div",{staticClass:"dialog-content"},[n._t("default")],2),n._v(" "),r("div",{staticClass:"dialog-foot"},[n._t("foot",function(){return[r("BtnModule",{on:{click:n.close}},[n._v("取 消")]),n._v(" "),r("BtnModule",{attrs:{type:"primary"},on:{click:n.confirm}},[n._v("确 认")])]})],2)]):n._e()};l._withStripped=!0;const A=r({render:l,staticRenderFns:[]},function(n){n&&(n("data-v-fbfcf64a_0",{source:"\n.dialog-sub-scroll{\r\n    overflow: hidden;\n}\r\n",map:{version:3,sources:["C:\\Users\\<USER>\\Desktop\\Knowledge\\src\\module\\Dialog.vue"],names:[],mappings:";AAgEA;IACA,gBAAA;AACA",file:"Dialog.vue",sourcesContent:['<template>\r\n    <div v-if="dialogVisible" class="dialogModule" :style="dialogStyle">\r\n        <div class="dialog-head">\r\n            <slot name="head">\r\n                <span class="dialog-title">{{ title }}</span>\r\n            </slot>\r\n            <span class="iconfont icon-guanbi-icon dialog-close" @click="close"></span>\r\n        </div>\r\n        <div class="dialog-content">\r\n            <slot></slot>\r\n        </div>\r\n        <div class="dialog-foot">\r\n            <slot name="foot">\r\n                <BtnModule @click="close">取 消</BtnModule>\r\n                <BtnModule type="primary" @click="confirm">确 认</BtnModule>\r\n            </slot>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport BtnModule from \'./btn\'\r\nexport default {\r\n    name: \'index\',\r\n    components: {\r\n        BtnModule\r\n    },\r\n    props: {\r\n        dialogVisible: {\r\n            type: Boolean,\r\n            default: true\r\n        },\r\n        title: {\r\n            type: String,\r\n            default: \'提示\'\r\n        },\r\n        width: {\r\n            type: String\r\n        }\r\n    },\r\n    computed: {\r\n        dialogStyle() {\r\n            let style = {}\r\n            if(this.width){\r\n                style.width = this.width\r\n            }\r\n            return style\r\n        }\r\n    },\r\n    mounted(){\r\n        // body增加class去除滚动条    \r\n        document.body.classList.add(\'dialog-sub-scroll\')\r\n    },\r\n    methods: {\r\n        close() {\r\n            this.$emit(\'close\')\r\n        },\r\n        confirm(){\r\n            this.$emit(\'confirm\')\r\n        }\r\n    }\r\n}\r\n<\/script>\r\n<style>\r\n.dialog-sub-scroll{\r\n    overflow: hidden;\r\n}\r\n</style>\r\n<style lang="scss" scoped>\r\n.dialogModule{\r\n    position: absolute;\r\n    top: 15vh;\r\n    left: 0;\r\n    right: 0;\r\n    margin: 0 auto;\r\n    width: 50vw;\r\n    border-radius: 10px;\r\n    background: rgba(255, 255, 255, 1);\r\n    box-shadow: 0px 4px 8px  rgba(124, 158, 174, 0.3);\r\n    .dialog-head{\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        padding: 20px 20px 15px;\r\n        border-bottom: 1px solid rgba(228, 231, 235, 1);\r\n        line-height: 1;\r\n        .dialog-title{\r\n            padding-left: 10px;\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            color: rgba(47, 46, 63, 1);\r\n        }\r\n        .dialog-close{\r\n            color: rgba(144, 147, 153, 1);\r\n            cursor: pointer;\r\n            &:hover{\r\n                color: rgba(33, 143, 196, 1);\r\n            }\r\n        }\r\n    }\r\n    .dialog-content{\r\n        padding: 20px 30px;\r\n    }\r\n    .dialog-foot{\r\n        padding: 10px 30px 30px;\r\n        display: flex;\r\n        justify-content: center;\r\n        gap: 40px;\r\n    }\r\n}\r\n/* 屏幕高度小于720px时 */\r\n@media (max-height: 720px) {\r\n    .dialogModule{\r\n        top: 10vh;\r\n    }\r\n}\r\n</style>']},media:void 0}),n("data-v-fbfcf64a_1",{source:'@charset "UTF-8";\n.dialogModule[data-v-fbfcf64a] {\n  position: absolute;\n  top: 15vh;\n  left: 0;\n  right: 0;\n  margin: 0 auto;\n  width: 50vw;\n  border-radius: 10px;\n  background: rgb(255, 255, 255);\n  box-shadow: 0px 4px 8px rgba(124, 158, 174, 0.3);\n}\n.dialogModule .dialog-head[data-v-fbfcf64a] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 20px 15px;\n  border-bottom: 1px solid rgb(228, 231, 235);\n  line-height: 1;\n}\n.dialogModule .dialog-head .dialog-title[data-v-fbfcf64a] {\n  padding-left: 10px;\n  font-size: 16px;\n  font-weight: 500;\n  color: rgb(47, 46, 63);\n}\n.dialogModule .dialog-head .dialog-close[data-v-fbfcf64a] {\n  color: rgb(144, 147, 153);\n  cursor: pointer;\n}\n.dialogModule .dialog-head .dialog-close[data-v-fbfcf64a]:hover {\n  color: rgb(33, 143, 196);\n}\n.dialogModule .dialog-content[data-v-fbfcf64a] {\n  padding: 20px 30px;\n}\n.dialogModule .dialog-foot[data-v-fbfcf64a] {\n  padding: 10px 30px 30px;\n  display: flex;\n  justify-content: center;\n  gap: 40px;\n}\n\n/* 屏幕高度小于720px时 */\n@media (max-height: 720px) {\n.dialogModule[data-v-fbfcf64a] {\n    top: 10vh;\n}\n}\n\n/*# sourceMappingURL=Dialog.vue.map */',map:{version:3,sources:["Dialog.vue","C:\\Users\\<USER>\\Desktop\\Knowledge\\src\\module\\Dialog.vue"],names:[],mappings:"AAAA,gBAAgB;ACqEhB;EACA,kBAAA;EACA,SAAA;EACA,OAAA;EACA,QAAA;EACA,cAAA;EACA,WAAA;EACA,mBAAA;EACA,8BAAA;EACA,gDAAA;ADnEA;ACoEA;EACA,aAAA;EACA,8BAAA;EACA,mBAAA;EACA,uBAAA;EACA,2CAAA;EACA,cAAA;ADlEA;ACmEA;EACA,kBAAA;EACA,eAAA;EACA,gBAAA;EACA,sBAAA;ADjEA;ACmEA;EACA,yBAAA;EACA,eAAA;ADjEA;ACkEA;EACA,wBAAA;ADhEA;ACoEA;EACA,kBAAA;ADlEA;ACoEA;EACA,uBAAA;EACA,aAAA;EACA,uBAAA;EACA,SAAA;ADlEA;;ACqEA,iBAAA;AACA;AACA;IACA,SAAA;ADlEE;AACF;;AAEA,qCAAqC",file:"Dialog.vue",sourcesContent:['@charset "UTF-8";\n.dialogModule {\n  position: absolute;\n  top: 15vh;\n  left: 0;\n  right: 0;\n  margin: 0 auto;\n  width: 50vw;\n  border-radius: 10px;\n  background: rgb(255, 255, 255);\n  box-shadow: 0px 4px 8px rgba(124, 158, 174, 0.3);\n}\n.dialogModule .dialog-head {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 20px 15px;\n  border-bottom: 1px solid rgb(228, 231, 235);\n  line-height: 1;\n}\n.dialogModule .dialog-head .dialog-title {\n  padding-left: 10px;\n  font-size: 16px;\n  font-weight: 500;\n  color: rgb(47, 46, 63);\n}\n.dialogModule .dialog-head .dialog-close {\n  color: rgb(144, 147, 153);\n  cursor: pointer;\n}\n.dialogModule .dialog-head .dialog-close:hover {\n  color: rgb(33, 143, 196);\n}\n.dialogModule .dialog-content {\n  padding: 20px 30px;\n}\n.dialogModule .dialog-foot {\n  padding: 10px 30px 30px;\n  display: flex;\n  justify-content: center;\n  gap: 40px;\n}\n\n/* 屏幕高度小于720px时 */\n@media (max-height: 720px) {\n  .dialogModule {\n    top: 10vh;\n  }\n}\n\n/*# sourceMappingURL=Dialog.vue.map */','<template>\r\n    <div v-if="dialogVisible" class="dialogModule" :style="dialogStyle">\r\n        <div class="dialog-head">\r\n            <slot name="head">\r\n                <span class="dialog-title">{{ title }}</span>\r\n            </slot>\r\n            <span class="iconfont icon-guanbi-icon dialog-close" @click="close"></span>\r\n        </div>\r\n        <div class="dialog-content">\r\n            <slot></slot>\r\n        </div>\r\n        <div class="dialog-foot">\r\n            <slot name="foot">\r\n                <BtnModule @click="close">取 消</BtnModule>\r\n                <BtnModule type="primary" @click="confirm">确 认</BtnModule>\r\n            </slot>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport BtnModule from \'./btn\'\r\nexport default {\r\n    name: \'index\',\r\n    components: {\r\n        BtnModule\r\n    },\r\n    props: {\r\n        dialogVisible: {\r\n            type: Boolean,\r\n            default: true\r\n        },\r\n        title: {\r\n            type: String,\r\n            default: \'提示\'\r\n        },\r\n        width: {\r\n            type: String\r\n        }\r\n    },\r\n    computed: {\r\n        dialogStyle() {\r\n            let style = {}\r\n            if(this.width){\r\n                style.width = this.width\r\n            }\r\n            return style\r\n        }\r\n    },\r\n    mounted(){\r\n        // body增加class去除滚动条    \r\n        document.body.classList.add(\'dialog-sub-scroll\')\r\n    },\r\n    methods: {\r\n        close() {\r\n            this.$emit(\'close\')\r\n        },\r\n        confirm(){\r\n            this.$emit(\'confirm\')\r\n        }\r\n    }\r\n}\r\n<\/script>\r\n<style>\r\n.dialog-sub-scroll{\r\n    overflow: hidden;\r\n}\r\n</style>\r\n<style lang="scss" scoped>\r\n.dialogModule{\r\n    position: absolute;\r\n    top: 15vh;\r\n    left: 0;\r\n    right: 0;\r\n    margin: 0 auto;\r\n    width: 50vw;\r\n    border-radius: 10px;\r\n    background: rgba(255, 255, 255, 1);\r\n    box-shadow: 0px 4px 8px  rgba(124, 158, 174, 0.3);\r\n    .dialog-head{\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        padding: 20px 20px 15px;\r\n        border-bottom: 1px solid rgba(228, 231, 235, 1);\r\n        line-height: 1;\r\n        .dialog-title{\r\n            padding-left: 10px;\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            color: rgba(47, 46, 63, 1);\r\n        }\r\n        .dialog-close{\r\n            color: rgba(144, 147, 153, 1);\r\n            cursor: pointer;\r\n            &:hover{\r\n                color: rgba(33, 143, 196, 1);\r\n            }\r\n        }\r\n    }\r\n    .dialog-content{\r\n        padding: 20px 30px;\r\n    }\r\n    .dialog-foot{\r\n        padding: 10px 30px 30px;\r\n        display: flex;\r\n        justify-content: center;\r\n        gap: 40px;\r\n    }\r\n}\r\n/* 屏幕高度小于720px时 */\r\n@media (max-height: 720px) {\r\n    .dialogModule{\r\n        top: 10vh;\r\n    }\r\n}\r\n</style>']},media:void 0}))},d,"data-v-fbfcf64a",0,0,0,t);const p={name:"index",props:{type:{type:String},size:{type:String},round:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},methods:{handleClick(){this.disabled||this.$emit("handleClick")}}};var c=function(){var n=this;return(n._self._c||n.$createElement)("div",{staticClass:"btnModule",class:[n.type,n.size,n.round?"round":"",n.disabled?"disabled":""],on:{click:n.handleClick}},[n._t("default")],2)};c._withStripped=!0;const u=r({render:c,staticRenderFns:[]},function(n){n&&n("data-v-982a4cf6_0",{source:".btnModule[data-v-982a4cf6] {\n  cursor: pointer;\n  display: inline-block;\n  border-radius: 6px;\n  border: 1px solid rgb(33, 143, 196);\n  background-color: #fff;\n  text-align: center;\n  color: rgb(33, 143, 196);\n  padding: 9px 20px;\n  font-size: 14px;\n  line-height: 1;\n  transition: 0.1s;\n  font-weight: 500;\n}\n.btnModule[data-v-982a4cf6]:hover {\n  color: rgb(33, 143, 196);\n  border-color: #c6e2ff;\n  background-color: #ecf5ff;\n}\n.btnModule.round[data-v-982a4cf6] {\n  border-radius: 17px;\n}\n.btnModule.primary[data-v-982a4cf6] {\n  color: #fff;\n  background-color: rgb(33, 143, 196);\n  border-color: rgb(33, 143, 196);\n}\n.btnModule.primary[data-v-982a4cf6]:hover {\n  background: rgba(33, 143, 196, 0.9);\n  border-color: rgba(33, 143, 196, 0.9);\n}\n.btnModule.large[data-v-982a4cf6] {\n  padding: 12px 20px;\n}\n.btnModule.large.round[data-v-982a4cf6] {\n  border-radius: 19px;\n}\n.btnModule.small[data-v-982a4cf6] {\n  padding: 8px 15px;\n  font-size: 12px;\n  border-radius: 4px;\n}\n.btnModule.small.round[data-v-982a4cf6] {\n  border-radius: 14px;\n}\n.btnModule.mini[data-v-982a4cf6] {\n  padding: 5px 10px;\n  font-size: 12px;\n  border-radius: 4px;\n}\n.btnModule.mini.round[data-v-982a4cf6] {\n  border-radius: 11px;\n}\n.btnModule.text[data-v-982a4cf6] {\n  border: 1px solid transparent;\n  padding: 0;\n}\n.btnModule.text[data-v-982a4cf6]:hover {\n  color: rgba(33, 143, 196, 0.8);\n  border-color: transparent;\n  background-color: transparent;\n}\n.btnModule.disabled[data-v-982a4cf6] {\n  cursor: not-allowed;\n  color: rgb(144, 147, 153);\n  border-color: rgb(144, 147, 153);\n  background-color: rgb(248, 248, 250);\n}\n\n/*# sourceMappingURL=Btn.vue.map */",map:{version:3,sources:["C:\\Users\\<USER>\\Desktop\\Knowledge\\src\\module\\Btn.vue","Btn.vue"],names:[],mappings:"AAmCA;EACA,eAAA;EACA,qBAAA;EACA,kBAAA;EACA,mCAAA;EACA,sBAAA;EACA,kBAAA;EACA,wBAAA;EACA,iBAAA;EACA,eAAA;EACA,cAAA;EACA,gBAAA;EACA,gBAAA;AClCA;ADmCA;EACA,wBAAA;EACA,qBAAA;EACA,yBAAA;ACjCA;ADmCA;EACA,mBAAA;ACjCA;ADmCA;EACA,WAAA;EACA,mCAAA;EACA,+BAAA;ACjCA;ADkCA;EACA,mCAAA;EACA,qCAAA;AChCA;ADyCA;EACA,kBAAA;ACvCA;ADwCA;EACA,mBAAA;ACtCA;ADyCA;EACA,iBAAA;EACA,eAAA;EACA,kBAAA;ACvCA;ADwCA;EACA,mBAAA;ACtCA;ADyCA;EACA,iBAAA;EACA,eAAA;EACA,kBAAA;ACvCA;ADwCA;EACA,mBAAA;ACtCA;ADyCA;EACA,6BAAA;EACA,UAAA;ACvCA;ADwCA;EACA,8BAAA;EACA,yBAAA;EACA,6BAAA;ACtCA;ADyCA;EACA,mBAAA;EACA,yBAAA;EACA,gCAAA;EACA,oCAAA;ACvCA;;AAEA,kCAAkC",file:"Btn.vue",sourcesContent:["<template>\r\n    <div class=\"btnModule\" :class=\"[type, size, round ? 'round' : '', disabled ? 'disabled' : '']\" @click=\"handleClick\">\r\n        <slot />\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: 'index',\r\n    props: {\r\n        type: {\r\n            type: String\r\n        },\r\n        size: {\r\n            type: String\r\n        },\r\n        round: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        disabled: {\r\n            type: Boolean,\r\n            default: false\r\n        }\r\n    },\r\n    methods: {\r\n        handleClick(){\r\n            if(this.disabled) return;\r\n            this.$emit('handleClick')\r\n        }\r\n    }\r\n}\r\n<\/script>\r\n\r\n<style lang=\"scss\" scoped>\r\n    .btnModule{\r\n        cursor: pointer;\r\n        display: inline-block;\r\n        border-radius: 6px;\r\n        border: 1px solid rgba(33, 143, 196, 1);\r\n        background-color: #fff;\r\n        text-align: center;\r\n        color: rgba(33, 143, 196, 1);\r\n        padding: 9px 20px;\r\n        font-size: 14px;\r\n        line-height: 1;\r\n        transition: .1s;\r\n        font-weight: 500;\r\n        &:hover{\r\n            color: rgba(33, 143, 196, 1);\r\n            border-color: #c6e2ff;\r\n            background-color: #ecf5ff;\r\n        }\r\n        &.round{\r\n            border-radius: 17px;\r\n        }\r\n        &.primary{\r\n            color: #fff;\r\n            background-color: rgba(33, 143, 196, 1);\r\n            border-color: rgba(33, 143, 196, 1);\r\n            &:hover{\r\n                background: rgba(33, 143, 196, .9);\r\n                border-color: rgba(33, 143, 196, .9);\r\n            }\r\n            // &.disabled{\r\n            //     cursor: not-allowed;\r\n            //     color: #fff;\r\n            //     background-color: rgba(33, 143, 196, 1);\r\n            //     border-color: rgba(33, 143, 196, 1);\r\n            // }\r\n        }\r\n        &.large{\r\n            padding: 12px 20px;\r\n            &.round{\r\n                border-radius: 19px;\r\n            }\r\n        }\r\n        &.small{\r\n            padding: 8px 15px;\r\n            font-size: 12px;\r\n            border-radius: 4px;\r\n            &.round{\r\n                border-radius: 14px;\r\n            }\r\n        }\r\n        &.mini{\r\n            padding: 5px 10px;\r\n            font-size: 12px;\r\n            border-radius: 4px;\r\n            &.round{\r\n                border-radius: 11px;\r\n            }\r\n        }\r\n        &.text{\r\n            border: 1px solid transparent;\r\n            padding: 0;\r\n            &:hover{\r\n                color: rgba(33, 143, 196, .8);\r\n                border-color: transparent;\r\n                background-color: transparent;\r\n            }\r\n        }\r\n        &.disabled{\r\n            cursor: not-allowed;\r\n            color: rgba(144, 147, 153, 1);\r\n            border-color: rgba(144, 147, 153, 1);\r\n            background-color: rgba(248, 248, 250, 1);\r\n        }\r\n    }\r\n</style>",".btnModule {\n  cursor: pointer;\n  display: inline-block;\n  border-radius: 6px;\n  border: 1px solid rgb(33, 143, 196);\n  background-color: #fff;\n  text-align: center;\n  color: rgb(33, 143, 196);\n  padding: 9px 20px;\n  font-size: 14px;\n  line-height: 1;\n  transition: 0.1s;\n  font-weight: 500;\n}\n.btnModule:hover {\n  color: rgb(33, 143, 196);\n  border-color: #c6e2ff;\n  background-color: #ecf5ff;\n}\n.btnModule.round {\n  border-radius: 17px;\n}\n.btnModule.primary {\n  color: #fff;\n  background-color: rgb(33, 143, 196);\n  border-color: rgb(33, 143, 196);\n}\n.btnModule.primary:hover {\n  background: rgba(33, 143, 196, 0.9);\n  border-color: rgba(33, 143, 196, 0.9);\n}\n.btnModule.large {\n  padding: 12px 20px;\n}\n.btnModule.large.round {\n  border-radius: 19px;\n}\n.btnModule.small {\n  padding: 8px 15px;\n  font-size: 12px;\n  border-radius: 4px;\n}\n.btnModule.small.round {\n  border-radius: 14px;\n}\n.btnModule.mini {\n  padding: 5px 10px;\n  font-size: 12px;\n  border-radius: 4px;\n}\n.btnModule.mini.round {\n  border-radius: 11px;\n}\n.btnModule.text {\n  border: 1px solid transparent;\n  padding: 0;\n}\n.btnModule.text:hover {\n  color: rgba(33, 143, 196, 0.8);\n  border-color: transparent;\n  background-color: transparent;\n}\n.btnModule.disabled {\n  cursor: not-allowed;\n  color: rgb(144, 147, 153);\n  border-color: rgb(144, 147, 153);\n  background-color: rgb(248, 248, 250);\n}\n\n/*# sourceMappingURL=Btn.vue.map */"]},media:void 0})},p,"data-v-982a4cf6",0,0,0,t);const g={name:"index",props:{radioId:{type:String,default:""},modelValue:{type:String,default:""},disabled:{type:Boolean,default:!1}},methods:{}};var m=function(){var n=this,r=n._self._c||n.$createElement;return r("div",{staticClass:"radioBox",class:{active:n.modelValue==n.radioId}},[r("div",{class:{disabled:n.disabled}},[r("span",{staticClass:"radio"}),n._t("default")],2)])};m._withStripped=!0;var b="/gateway/gpt/lvaiduo/asset/auth/product/LawyerAI",h="/gateway/gptkgbase/lib/included",f="/gateway/gptkgbase/doc/inertAndInclude-fbData",C="/fabao-auth/auth/v1/productAuthCheckFromToken",x="/gateway/user/api/User/PkulawUserInfo/groupusermessage";const v={name:"index",components:{Dialog:A,BtnModule:u,RadioModule:r({render:m,staticRenderFns:[]},function(n){n&&n("data-v-4aec05ea_0",{source:'.radioBox[data-v-4aec05ea] {\n  display: inline-block;\n}\n.radioBox > div[data-v-4aec05ea] {\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n.radioBox .radio[data-v-4aec05ea] {\n  border: 1px solid #dcdfe6;\n  border-radius: 100%;\n  width: 12px;\n  height: 12px;\n  background-color: #fff;\n}\n.radioBox:hover .radio[data-v-4aec05ea] {\n  border-color: rgba(33, 143, 196, 0.5);\n}\n.radioBox.active[data-v-4aec05ea] {\n  color: rgb(33, 143, 196);\n}\n.radioBox.active .radio[data-v-4aec05ea] {\n  position: relative;\n  background-color: rgb(33, 143, 196);\n  border-color: rgb(33, 143, 196);\n}\n.radioBox.active .radio[data-v-4aec05ea]::after {\n  width: 3px;\n  height: 7px;\n  position: absolute;\n  top: 1px;\n  left: 4px;\n  content: "";\n  border: 1px solid #fff;\n  border-left: 0;\n  border-top: 0;\n  transform: rotate(45deg) scaleY(1);\n}\n.radioBox.active[data-v-4aec05ea]:hover {\n  color: rgba(33, 143, 196, 0.8);\n}\n.radioBox.active:hover .radio[data-v-4aec05ea] {\n  border-color: rgba(33, 143, 196, 0.8);\n  background-color: rgba(33, 143, 196, 0.8);\n}\n.radioBox .disabled[data-v-4aec05ea] {\n  cursor: not-allowed;\n  color: #c0c4cc;\n}\n.radioBox .disabled .radio[data-v-4aec05ea] {\n  border-color: #dcdfe6;\n}\n\n/*# sourceMappingURL=Radio.vue.map */',map:{version:3,sources:["C:\\Users\\<USER>\\Desktop\\Knowledge\\src\\module\\Radio.vue","Radio.vue"],names:[],mappings:"AAgCA;EACA,qBAAA;AC/BA;ADgCA;EACA,eAAA;EACA,aAAA;EACA,mBAAA;EACA,QAAA;AC9BA;ADgCA;EACA,yBAAA;EACA,mBAAA;EACA,WAAA;EACA,YAAA;EACA,sBAAA;AC9BA;ADiCA;EACA,qCAAA;AC/BA;ADkCA;EACA,wBAAA;AChCA;ADiCA;EACA,kBAAA;EACA,mCAAA;EACA,+BAAA;AC/BA;ADgCA;EACA,UAAA;EACA,WAAA;EACA,kBAAA;EACA,QAAA;EACA,SAAA;EACA,WAAA;EACA,sBAAA;EACA,cAAA;EACA,aAAA;EACA,kCAAA;AC9BA;ADiCA;EACA,8BAAA;AC/BA;ADgCA;EACA,qCAAA;EACA,yCAAA;AC9BA;ADkCA;EACA,mBAAA;EACA,cAAA;AChCA;ADiCA;EACA,qBAAA;AC/BA;;AAEA,oCAAoC",file:"Radio.vue",sourcesContent:['<template>\r\n    <div class="radioBox" :class="{\'active\':modelValue==radioId}" >\r\n        <div :class="{\'disabled\':disabled}">\r\n            <span class="radio"></span><slot />\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: \'index\',\r\n    props: {\r\n        radioId: {\r\n            type: String,\r\n            default: \'\'\r\n        },\r\n        modelValue: {\r\n            type: String,\r\n            default: \'\'\r\n        },\r\n        disabled: {\r\n            type: Boolean,\r\n            default: false\r\n        }\r\n    }, \r\n    methods: {\r\n\r\n    }\r\n}\r\n<\/script>\r\n\r\n<style lang="scss" scoped>\r\n.radioBox{\r\n    display: inline-block;\r\n    >div{\r\n        cursor: pointer;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 5px;\r\n    }\r\n    .radio{\r\n        border: 1px solid #dcdfe6;\r\n        border-radius: 100%;\r\n        width: 12px;\r\n        height: 12px;\r\n        background-color: #fff;\r\n    }\r\n    &:hover{\r\n        .radio{\r\n            border-color: rgba(33, 143, 196, 0.5);\r\n        }\r\n    }\r\n    &.active{\r\n        color: rgba(33, 143, 196, 1);\r\n        .radio{\r\n            position: relative;\r\n            background-color:rgba(33, 143, 196, 1);\r\n            border-color: rgba(33, 143, 196, 1);\r\n            &::after{\r\n                width: 3px;\r\n                height: 7px;\r\n                position: absolute;\r\n                top: 1px;\r\n                left: 4px;\r\n                content: "";\r\n                border: 1px solid #fff;\r\n                border-left: 0;\r\n                border-top: 0;\r\n                transform: rotate(45deg) scaleY(1);\r\n            }\r\n        }\r\n        &:hover{\r\n            color: rgba(33, 143, 196, 0.8);\r\n            .radio{\r\n                border-color: rgba(33, 143, 196, 0.8);\r\n                background-color:rgba(33, 143, 196, 0.8);\r\n            }\r\n        }\r\n    }\r\n    .disabled{\r\n        cursor: not-allowed;\r\n        color: #c0c4cc;\r\n        .radio{\r\n            border-color: #dcdfe6\r\n        }\r\n    }\r\n}\r\n</style>','.radioBox {\n  display: inline-block;\n}\n.radioBox > div {\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n.radioBox .radio {\n  border: 1px solid #dcdfe6;\n  border-radius: 100%;\n  width: 12px;\n  height: 12px;\n  background-color: #fff;\n}\n.radioBox:hover .radio {\n  border-color: rgba(33, 143, 196, 0.5);\n}\n.radioBox.active {\n  color: rgb(33, 143, 196);\n}\n.radioBox.active .radio {\n  position: relative;\n  background-color: rgb(33, 143, 196);\n  border-color: rgb(33, 143, 196);\n}\n.radioBox.active .radio::after {\n  width: 3px;\n  height: 7px;\n  position: absolute;\n  top: 1px;\n  left: 4px;\n  content: "";\n  border: 1px solid #fff;\n  border-left: 0;\n  border-top: 0;\n  transform: rotate(45deg) scaleY(1);\n}\n.radioBox.active:hover {\n  color: rgba(33, 143, 196, 0.8);\n}\n.radioBox.active:hover .radio {\n  border-color: rgba(33, 143, 196, 0.8);\n  background-color: rgba(33, 143, 196, 0.8);\n}\n.radioBox .disabled {\n  cursor: not-allowed;\n  color: #c0c4cc;\n}\n.radioBox .disabled .radio {\n  border-color: #dcdfe6;\n}\n\n/*# sourceMappingURL=Radio.vue.map */']},media:void 0})},g,"data-v-4aec05ea",0,0,0,t)},props:{ai_url:{type:String},data:{type:Array},getAxios:{type:Function},postAxios:{type:Function}},data:()=>({loading:!1,value:"",fileList:[],hasList:!0}),created(){this.getAxios(this.ai_url+h,{start:0,size:100}).then(n=>{this.fileList=n;let r=[];this.fileList.forEach(n=>{n.allowIncluded&&r.push(n.id)}),this.hasList=!!r.length})},methods:{getCheckType(n){return this.value.includes(n.id)},changeVal(n){this.value=n},confirm(){this.loading||(this.loading=!0,this.postAxios(this.ai_url+f,{libIds:[this.value],docs:this.data}).then(n=>{this.$emit("changeModule",!1,403==n.code?{type:"error",default:n.msg}:{type:"success",default:"收录成功"}),this.$emit("addLog")}).catch(()=>{this.$emit("openMessage","error","收录失败")}).finally(()=>{this.loading=!1}))},close(){this.$emit("changeModule",!1)}}};var y=function(){var n=this,r=n._self._c||n.$createElement;return r("Dialog",{attrs:{width:"520px",title:"收录到知识库"},on:{close:n.close},scopedSlots:n._u([{key:"head",fn:function(){return[r("div",{staticClass:"flex title"},[n._v("\n            收录到\n            "),r("span",{staticClass:"iconfont icon-a-01-zhishiku-icon"}),n._v(" "),r("span",[n._v("律AI多知识库")])])]},proxy:!0},{key:"foot",fn:function(){return[r("BtnModule",{staticClass:"btn",on:{handleClick:n.close}},[n._v("取消")]),n._v(" "),n.hasList?r("BtnModule",{staticClass:"btn",attrs:{type:n.value?"primary":"",disabled:!n.value},on:{handleClick:n.confirm}},[n._v("收录")]):n._e()]},proxy:!0}])},[n._v(" "),r("div",{staticClass:"main"},[n._l(n.fileList,function(e){return[e.allowIncluded?r("div",{key:e.id,staticClass:"item flex",on:{click:function(r){return n.changeVal(e.id)}}},[r("RadioModule",{attrs:{radioId:e.id,modelValue:n.value}}),n._v(" "),r("div",{staticClass:"name"},[n._v(n._s(e.name))])],1):n._e()]}),n._v(" "),n.hasList?n._e():r("div",{staticClass:"empty"},[r("div",{staticClass:"empty-text"},[n._v("\n                暂无可收录的律AI多知识库\n            ")])])],2)])};y._withStripped=!0;const E=r({render:y,staticRenderFns:[]},function(n){n&&n("data-v-68810726_0",{source:".flex[data-v-68810726] {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n.title[data-v-68810726] {\n  font-size: 16px;\n  padding-left: 10px;\n  color: rgb(47, 46, 63);\n  align-items: self-start;\n}\n.title .iconfont[data-v-68810726] {\n  font-size: 20px;\n}\n.title span[data-v-68810726] {\n  color: rgb(33, 143, 196);\n}\n.main[data-v-68810726] {\n  max-height: 50vh;\n  overflow: auto;\n}\n.main .item[data-v-68810726] {\n  cursor: pointer;\n  gap: 5px;\n  padding: 10px;\n}\n.main .item .name[data-v-68810726] {\n  min-width: 0;\n  flex: 1;\n  line-height: 1.6;\n  word-wrap: break-word;\n}\n.main .item[data-v-68810726]:hover {\n  border-radius: 4px;\n  background: rgb(248, 248, 250);\n}\n.main .empty[data-v-68810726] {\n  text-align: center;\n  padding: 40px 0;\n}\n.main .empty .empty-text[data-v-68810726] {\n  color: rgb(156, 163, 175);\n}\n.btn[data-v-68810726] {\n  width: 58px;\n}\n\n/*# sourceMappingURL=index.vue.map */",map:{version:3,sources:["C:\\Users\\<USER>\\Desktop\\Knowledge\\src\\components\\index.vue","index.vue"],names:[],mappings:"AA4HA;EACA,aAAA;EACA,mBAAA;EACA,QAAA;AC3HA;AD6HA;EACA,eAAA;EACA,kBAAA;EACA,sBAAA;EACA,uBAAA;AC1HA;AD2HA;EACA,eAAA;ACzHA;AD2HA;EACA,wBAAA;ACzHA;AD4HA;EACA,gBAAA;EACA,cAAA;ACzHA;AD0HA;EACA,eAAA;EACA,QAAA;EACA,aAAA;ACxHA;ADyHA;EACA,YAAA;EACA,OAAA;EACA,gBAAA;EACA,qBAAA;ACvHA;ADyHA;EACA,kBAAA;EACA,8BAAA;ACvHA;AD0HA;EACA,kBAAA;EACA,eAAA;ACxHA;ADyHA;EACA,yBAAA;ACvHA;AD2HA;EACA,WAAA;ACxHA;;AAEA,oCAAoC",file:"index.vue",sourcesContent:['<template>\r\n    <Dialog width="520px" title="收录到知识库" @close="close">\r\n        <template #head>\r\n           <div class="flex title">\r\n                收录到\r\n                <span class="iconfont icon-a-01-zhishiku-icon"></span>\r\n                <span>律AI多知识库</span>\r\n           </div>\r\n        </template>\r\n        <div class="main">\r\n            <template v-for="item in fileList">\r\n                <div class="item flex" v-if="item.allowIncluded" @click="changeVal(item.id)" :key="item.id">\r\n                    <RadioModule \r\n                        :radioId="item.id" \r\n                        :modelValue="value" />\r\n                    <div class="name">{{ item.name }}</div>\r\n                </div>\r\n            </template>\r\n            <div class="empty" v-if="!hasList">\r\n                <div class="empty-text">\r\n                    暂无可收录的律AI多知识库\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <template #foot>\r\n            <BtnModule class="btn" @handleClick="close">取消</BtnModule>\r\n            <BtnModule class="btn" v-if="hasList" :type="!value?\'\':\'primary\'" :disabled="!value" @handleClick="confirm">收录</BtnModule>\r\n        </template>\r\n    </Dialog>\r\n</template>\r\n\r\n<script>\r\nimport Dialog from \'../module/Dialog\'\r\nimport BtnModule from \'../module/Btn\'\r\nimport RadioModule from \'../module/Radio\'\r\nimport apis from \'../utils/api\'\r\nexport default {\r\n    name: \'index\',\r\n    components: {\r\n        Dialog,\r\n        BtnModule,\r\n        RadioModule\r\n    },\r\n    props: {\r\n        ai_url: {\r\n            type: String\r\n        },\r\n\r\n        data: {\r\n            type: Array\r\n        },\r\n        getAxios: {\r\n            type: Function\r\n        },\r\n        postAxios: {\r\n            type: Function\r\n        }\r\n    },\r\n    data() {\r\n        return {\r\n            loading: false,\r\n            value: \'\',\r\n            fileList: [],\r\n\r\n            hasList: true\r\n        }   \r\n    },\r\n    created(){\r\n        this.getAxios(this.ai_url+apis.getList,{\r\n            start: 0,\r\n            size: 100\r\n        }).then(res=>{\r\n            this.fileList = res\r\n\r\n            let arr = []\r\n            this.fileList.forEach(item=>{\r\n                if(item.allowIncluded){\r\n                    arr.push(item.id)\r\n                }\r\n            })\r\n            this.hasList = !!arr.length\r\n        })\r\n    },\r\n    methods: {\r\n        getCheckType(item){\r\n            return this.value.includes(item.id)\r\n        },\r\n        changeVal(id){\r\n            this.value = id\r\n        },\r\n        confirm(){\r\n            if(!this.loading){\r\n                this.loading = true\r\n                this.postAxios(this.ai_url+apis.addDocs,{\r\n                    libIds: [this.value],\r\n                    docs: this.data\r\n                }).then(res=>{\r\n                    if(res.code==403){\r\n                        this.$emit(\'changeModule\', false, {\r\n                            type: \'error\',\r\n                            default: res.msg\r\n                        })\r\n                    }else{\r\n                        this.$emit(\'changeModule\', false, {\r\n                            type: \'success\',\r\n                            default: \'收录成功\'\r\n                        })\r\n                    }\r\n                    this.$emit(\'addLog\')\r\n                }).catch(()=>{\r\n                    this.$emit(\'openMessage\', \'error\', \'收录失败\')\r\n                }).finally(()=>{\r\n                    this.loading = false\r\n                })\r\n            }\r\n        },\r\n        close(){\r\n            this.$emit(\'changeModule\', false)\r\n        }\r\n    }\r\n}\r\n<\/script>\r\n\r\n<style lang="scss" scoped>\r\n.flex{\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 5px;\r\n}\r\n.title{\r\n    font-size: 16px;\r\n    padding-left: 10px;\r\n    color: rgba(47, 46, 63, 1);\r\n    align-items: self-start;\r\n    .iconfont{\r\n        font-size: 20px;\r\n    }\r\n    span{\r\n        color: rgba(33, 143, 196, 1)\r\n    }\r\n}\r\n.main{\r\n    max-height: 50vh;\r\n    overflow: auto;\r\n    .item{\r\n        cursor: pointer;\r\n        gap: 5px;\r\n        padding: 10px;\r\n        .name{\r\n            min-width: 0;\r\n            flex: 1;\r\n            line-height: 1.6;\r\n            word-wrap: break-word;\r\n        }\r\n        &:hover{\r\n            border-radius: 4px;\r\n            background: rgba(248, 248, 250, 1);\r\n        }\r\n    }\r\n    .empty{\r\n        text-align: center;\r\n        padding: 40px 0;\r\n        .empty-text{\r\n            color: rgba(156, 163, 175, 1);\r\n        }\r\n    }\r\n}\r\n.btn{\r\n    width: 58px;\r\n}\r\n</style>',".flex {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.title {\n  font-size: 16px;\n  padding-left: 10px;\n  color: rgb(47, 46, 63);\n  align-items: self-start;\n}\n.title .iconfont {\n  font-size: 20px;\n}\n.title span {\n  color: rgb(33, 143, 196);\n}\n\n.main {\n  max-height: 50vh;\n  overflow: auto;\n}\n.main .item {\n  cursor: pointer;\n  gap: 5px;\n  padding: 10px;\n}\n.main .item .name {\n  min-width: 0;\n  flex: 1;\n  line-height: 1.6;\n  word-wrap: break-word;\n}\n.main .item:hover {\n  border-radius: 4px;\n  background: rgb(248, 248, 250);\n}\n.main .empty {\n  text-align: center;\n  padding: 40px 0;\n}\n.main .empty .empty-text {\n  color: rgb(156, 163, 175);\n}\n\n.btn {\n  width: 58px;\n}\n\n/*# sourceMappingURL=index.vue.map */"]},media:void 0})},v,"data-v-68810726",0,0,0,t);const w={name:"index",components:{Dialog:A,BtnModule:u},props:["libraryName"],methods:{close(){this.$emit("changeModule",!1)}}};var B=function(){var n=this,r=n._self._c||n.$createElement;return r("Dialog",{attrs:{width:"420px",title:"提示"},on:{close:n.close},scopedSlots:n._u([{key:"foot",fn:function(){return[r("BtnModule",{staticClass:"btn",attrs:{type:"primary"},on:{handleClick:n.close}},[n._v("完成")])]},proxy:!0}])},[r("div",{staticClass:"main"},["lvAiduo"==n.libraryName?[r("div",{staticClass:"dl"},[r("img",{attrs:{src:"https://static.pkulaw.com/statics/img/sub/error.svg",width:"71"}}),n._v(" "),r("p",{staticClass:"desc"},[n._v("您当前没有律爱多权限，扫描下方二维码可以")]),n._v(" "),r("p",{staticClass:"desc"},[n._v("免费领取律爱多试用权益")])]),n._v(" "),r("div",{staticClass:"img-box"},[r("img",{attrs:{src:"https://static.pkulaw.com/assets/icon/LAID.a92a4dfb.png",width:"130",height:"130"}})])]:r("div",{staticClass:"text"},[n._v("\n            您当前没有"+n._s(n.libraryName)+"权限或权限已过期\n        ")])],2)])};B._withStripped=!0;const k=r({render:B,staticRenderFns:[]},function(n){n&&n("data-v-c390e3be_0",{source:".main[data-v-c390e3be] {\n  text-align: center;\n}\n.main .dl .desc[data-v-c390e3be] {\n  line-height: 20px;\n  color: rgb(96, 98, 102);\n}\n.main .dl .img-box[data-v-c390e3be] {\n  padding: 20px 0;\n}\n.main img[data-v-c390e3be] {\n  display: block;\n  margin: 0 auto;\n}\n.main .text[data-v-c390e3be] {\n  padding-top: 10px;\n}\n\n/*# sourceMappingURL=alert.vue.map */",map:{version:3,sources:["C:\\Users\\<USER>\\Desktop\\Knowledge\\src\\components\\alert.vue","alert.vue"],names:[],mappings:"AA0CA;EACA,kBAAA;ACzCA;AD2CA;EACA,iBAAA;EACA,uBAAA;ACzCA;AD2CA;EACA,eAAA;ACzCA;AD4CA;EACA,cAAA;EACA,cAAA;AC1CA;AD4CA;EACA,iBAAA;AC1CA;;AAEA,oCAAoC",file:"alert.vue",sourcesContent:['<template>\r\n    <Dialog width="420px" title="提示" @close="close">\r\n        <div class="main">\r\n            <template v-if="libraryName==\'lvAiduo\'">\r\n                <div class="dl">\r\n                    <img src="https://static.pkulaw.com/statics/img/sub/error.svg" width="71">\r\n                    <p class="desc">您当前没有律爱多权限，扫描下方二维码可以</p>\r\n                    <p class="desc">免费领取律爱多试用权益</p>\r\n                </div>\r\n                <div class="img-box">\r\n                    <img src="https://static.pkulaw.com/assets/icon/LAID.a92a4dfb.png" width="130" height="130">\r\n                </div>\r\n            </template>\r\n            <div class="text" v-else>\r\n                您当前没有{{libraryName}}权限或权限已过期\r\n            </div>\r\n        </div>\r\n        <template #foot>\r\n            <BtnModule class="btn" type="primary" @handleClick="close">完成</BtnModule>\r\n        </template>\r\n    </Dialog>\r\n</template>\r\n\r\n<script>\r\nimport Dialog from \'../module/Dialog\'\r\nimport BtnModule from \'../module/Btn\'\r\nexport default {\r\n    name: \'index\',\r\n    components: {\r\n        Dialog,\r\n        BtnModule\r\n    },\r\n    props: [\'libraryName\'],\r\n    methods: {\r\n        close(){\r\n            this.$emit(\'changeModule\', false)\r\n        }\r\n    }\r\n}\r\n<\/script>\r\n\r\n<style lang="scss" scoped>\r\n.main{\r\n    text-align: center;\r\n    .dl{\r\n        .desc{\r\n            line-height: 20px;\r\n            color: rgb(96, 98, 102);\r\n        }\r\n        .img-box{\r\n            padding: 20px 0;\r\n        }\r\n    }\r\n    img{\r\n        display: block;\r\n        margin: 0 auto;\r\n    }\r\n    .text{\r\n        padding-top: 10px;\r\n    }\r\n}\r\n</style>',".main {\n  text-align: center;\n}\n.main .dl .desc {\n  line-height: 20px;\n  color: rgb(96, 98, 102);\n}\n.main .dl .img-box {\n  padding: 20px 0;\n}\n.main img {\n  display: block;\n  margin: 0 auto;\n}\n.main .text {\n  padding-top: 10px;\n}\n\n/*# sourceMappingURL=alert.vue.map */"]},media:void 0})},w,"data-v-c390e3be",0,0,0,t);const M={name:"index",components:{Dialog:A,BtnModule:u},props:{base_url:{type:String},postAxios:{type:Function}},data:()=>({userKey:""}),created(){this.getUserKey()},methods:{getUserKey(){this.postAxios(this.base_url+x).then(n=>{this.userKey=n})},close(){this.$emit("changeModule",!1)},confirm(){let n=window.location.href;window.location.href="https://cas.pkulaw.com/auth/realms/fabao/sms/remove-sessions?redirect_uri="+encodeURIComponent("https://cas.pkulaw.com/auth/realms/fabao/protocol/openid-connect/auth?g="+this.userKey+"&scope=openid&response_type=code&iframe=1&client_id=pkulaw&redirect_uri=https://cas.pkulaw.com/auth/realms/fabao/sms/login-success.html?redirect_path="+n)}}};var _=function(){var n=this,r=n._self._c||n.$createElement;return r("Dialog",{attrs:{width:"480px",dialogVisible:!!n.userKey},on:{close:n.close},scopedSlots:n._u([{key:"foot",fn:function(){return[r("BtnModule",{staticClass:"btn",on:{handleClick:n.close}},[n._v("我知道了")])]},proxy:!0}])},[r("div",{staticClass:"main"},[r("p",[n._v("北大法宝针对集团账号进行个性化及安全升级")]),n._v(" "),r("p",[n._v("目前该功能仅对个人账号正常使用，请前往"),r("span",{on:{click:n.confirm}},[n._v("进入/创建个人账号")])])])])};_._withStripped=!0;const D=r({render:_,staticRenderFns:[]},function(n){n&&n("data-v-70dbc8ef_0",{source:".main[data-v-70dbc8ef] {\n  padding: 20px 0;\n  line-height: 30px;\n}\n.main span[data-v-70dbc8ef] {\n  color: rgb(33, 143, 196);\n  cursor: pointer;\n}\n\n/*# sourceMappingURL=IsCompany.vue.map */",map:{version:3,sources:["C:\\Users\\<USER>\\Desktop\\Knowledge\\src\\components\\IsCompany.vue","IsCompany.vue"],names:[],mappings:"AAqEA;EACA,eAAA;EACA,iBAAA;ACpEA;ADqEA;EACA,wBAAA;EACA,eAAA;ACnEA;;AAEA,wCAAwC",file:"IsCompany.vue",sourcesContent:['<template>\r\n    <Dialog width="480px" @close="close" :dialogVisible="!!userKey">\r\n        <div class="main">\r\n            <p>北大法宝针对集团账号进行个性化及安全升级</p>\r\n            <p>目前该功能仅对个人账号正常使用，请前往<span @click="confirm">进入/创建个人账号</span></p>\r\n        </div>\r\n        <template #foot>\r\n            <BtnModule class="btn" @handleClick="close">我知道了</BtnModule>\r\n        </template>\r\n    </Dialog>\r\n</template>\r\n\r\n<script>\r\nimport Dialog from \'../module/Dialog\'\r\nimport BtnModule from \'../module/Btn\'\r\nimport apis from \'../utils/api\'\r\nexport default {\r\n    name: \'index\',\r\n    components: {\r\n        Dialog,\r\n        BtnModule\r\n    },\r\n    props: {\r\n        base_url: {\r\n            type: String\r\n        },\r\n        postAxios: {\r\n            type: Function\r\n        }\r\n    },\r\n    data() {\r\n        return {\r\n            userKey: \'\'\r\n        }   \r\n    },\r\n    created(){\r\n        this.getUserKey()\r\n    },\r\n    methods: {\r\n        getUserKey(){\r\n            this.postAxios(this.base_url + apis.getUserKey).then((res) => {\r\n                console.log(res)\r\n                this.userKey = res;\r\n            })\r\n        },\r\n        close(){\r\n            this.$emit(\'changeModule\', false)\r\n        },\r\n        confirm(){\r\n            let href = window.location.href\r\n            let url =\r\n                {"VUE_AI_URL":"https://ai.pkulaw.com","VUE_BASE_URL":"https://www.pkulaw.com","VUE_GATEWAY_URL":"https://gateway.pkulaw.com","VUE_KC_BASE":"https://cas.pkulaw.com"}[\'VUE_KC_BASE\'] +\r\n                "/auth/realms/fabao/protocol/openid-connect/auth?g=" +\r\n                this.userKey +\r\n                "&scope=openid" +\r\n                "&response_type=code" +\r\n                "&iframe=1" +\r\n                "&client_id=pkulaw" +\r\n                "&redirect_uri=" +\r\n                {"VUE_AI_URL":"https://ai.pkulaw.com","VUE_BASE_URL":"https://www.pkulaw.com","VUE_GATEWAY_URL":"https://gateway.pkulaw.com","VUE_KC_BASE":"https://cas.pkulaw.com"}[\'VUE_KC_BASE\'] +\r\n                "/auth/realms/fabao/sms/login-success.html?redirect_path=" +\r\n                href;\r\n            window.location.href = {"VUE_AI_URL":"https://ai.pkulaw.com","VUE_BASE_URL":"https://www.pkulaw.com","VUE_GATEWAY_URL":"https://gateway.pkulaw.com","VUE_KC_BASE":"https://cas.pkulaw.com"}[\'VUE_KC_BASE\'] + "/auth/realms/fabao/sms/remove-sessions?redirect_uri=" + encodeURIComponent(url)\r\n        }\r\n    }\r\n}\r\n<\/script>\r\n\r\n<style lang="scss" scoped>\r\n.main{\r\n   padding: 20px 0;\r\n   line-height: 30px;\r\n   span{\r\n       color: rgb(33, 143, 196);\r\n       cursor: pointer;\r\n   }\r\n}\r\n</style>',".main {\n  padding: 20px 0;\n  line-height: 30px;\n}\n.main span {\n  color: rgb(33, 143, 196);\n  cursor: pointer;\n}\n\n/*# sourceMappingURL=IsCompany.vue.map */"]},media:void 0})},M,"data-v-70dbc8ef",0,0,0,t);const L={name:"index",props:{time:{type:Number}},data:()=>({active:!1,type:"",msg:"",timer:null}),methods:{start(n,r){this.timer&&clearTimeout(this.timer),this.active=!0,this.type=n,this.msg=r,this.timer=setTimeout(()=>{this.active=!1,this.$emit("close")},this.time)}},beforeDestroy(){this.timer&&(clearTimeout(this.timer),this.timer=null)}};var U=function(){var n=this,r=n._self._c||n.$createElement;return r("div",{staticClass:"messageBox",class:{active:n.active}},[["success"==n.type?r("span",{staticClass:"iconfont icon-icon_suc"}):n._e(),n._v(" "),"error"==n.type?r("span",{staticClass:"iconfont icon-icon_warning"}):n._e(),n._v(" "),r("span",[n._v(n._s(n.msg))])]],2)};U._withStripped=!0;const S={name:"index",components:{addModule:E,alertModule:k,isCompany:D,MessageModule:r({render:U,staticRenderFns:[]},function(n){n&&n("data-v-1904a750_0",{source:".messageBox[data-v-1904a750] {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  position: fixed;\n  top: 0;\n  left: 50%;\n  transform: translate(-50%, -100%);\n  z-index: 9999;\n  opacity: 0;\n  transition: all 0.3s ease-in-out;\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.7);\n  font-size: 16px;\n  font-weight: 400;\n  line-height: 24px;\n  color: rgb(255, 255, 255);\n  padding: 18px 20px;\n  white-space: nowrap;\n}\n.messageBox .iconfont[data-v-1904a750] {\n  font-size: 21px;\n}\n.messageBox .icon-icon_suc[data-v-1904a750] {\n  color: rgb(103, 194, 58);\n}\n.messageBox .icon-icon_warning[data-v-1904a750] {\n  color: rgb(221, 162, 60);\n}\n.messageBox.active[data-v-1904a750] {\n  opacity: 1;\n  transform: translate(-50%, 20px);\n}\n\n/*# sourceMappingURL=message.vue.map */",map:{version:3,sources:["C:\\Users\\<USER>\\Desktop\\Knowledge\\src\\module\\message.vue","message.vue"],names:[],mappings:"AAmDA;EACA,aAAA;EACA,mBAAA;EACA,SAAA;EACA,eAAA;EACA,MAAA;EACA,SAAA;EACA,iCAAA;EACA,aAAA;EACA,UAAA;EACA,gCAAA;EACA,mBAAA;EACA,8BAAA;EACA,eAAA;EACA,gBAAA;EACA,iBAAA;EACA,yBAAA;EACA,kBAAA;EACA,mBAAA;AClDA;ADmDA;EACA,eAAA;ACjDA;ADmDA;EACA,wBAAA;ACjDA;ADmDA;EACA,wBAAA;ACjDA;ADmDA;EACA,UAAA;EACA,gCAAA;ACjDA;;AAEA,sCAAsC",file:"message.vue",sourcesContent:["<template>\r\n    <div class=\"messageBox\" :class=\"{active: active}\">\r\n        <template>\r\n            <span class=\"iconfont icon-icon_suc\" v-if=\"type=='success'\"></span>\r\n            <span class=\"iconfont icon-icon_warning\" v-if=\"type=='error'\"></span>\r\n            <span>{{ msg }}</span>\r\n        </template>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: 'index',\r\n    props: {\r\n        time: {\r\n            type: Number\r\n        }\r\n    },\r\n    data() {\r\n        return {\r\n            active: false,\r\n            type: '',\r\n            msg: '',\r\n            timer: null\r\n        }   \r\n    },\r\n    methods: {\r\n        start(type,msg){\r\n            if(this.timer){\r\n                clearTimeout(this.timer)\r\n            }\r\n            this.active = true\r\n            this.type = type\r\n            this.msg = msg\r\n            this.timer = setTimeout(() => {\r\n                this.active = false\r\n                this.$emit('close')\r\n            }, this.time)\r\n        }\r\n    },\r\n    beforeDestroy(){\r\n        if(this.timer){\r\n            console.log('clearTimeout', this.timer)\r\n            clearTimeout(this.timer)\r\n            this.timer = null\r\n        }\r\n    }\r\n}\r\n<\/script>\r\n\r\n<style lang=\"scss\" scoped>\r\n .messageBox{\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n    position: fixed;\r\n    top: 0;\r\n    left: 50%;\r\n    transform: translate(-50%, -100%);\r\n    z-index: 9999;\r\n    opacity: 0;\r\n    transition: all 0.3s ease-in-out;\r\n    border-radius: 10px;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    font-size: 16px;\r\n    font-weight: 400;\r\n    line-height: 24px;\r\n    color: rgba(255, 255, 255, 1);\r\n    padding: 18px 20px;\r\n    white-space: nowrap;\r\n    .iconfont{\r\n        font-size: 21px;\r\n    }\r\n    .icon-icon_suc{\r\n        color: rgba(103, 194, 58, 1);\r\n    }\r\n    .icon-icon_warning{\r\n        color: rgba(221, 162, 60, 1);\r\n    }\r\n    &.active{\r\n        opacity: 1;\r\n        transform: translate(-50%, 20px);\r\n    }\r\n }\r\n</style>",".messageBox {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  position: fixed;\n  top: 0;\n  left: 50%;\n  transform: translate(-50%, -100%);\n  z-index: 9999;\n  opacity: 0;\n  transition: all 0.3s ease-in-out;\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.7);\n  font-size: 16px;\n  font-weight: 400;\n  line-height: 24px;\n  color: rgb(255, 255, 255);\n  padding: 18px 20px;\n  white-space: nowrap;\n}\n.messageBox .iconfont {\n  font-size: 21px;\n}\n.messageBox .icon-icon_suc {\n  color: rgb(103, 194, 58);\n}\n.messageBox .icon-icon_warning {\n  color: rgb(221, 162, 60);\n}\n.messageBox.active {\n  opacity: 1;\n  transform: translate(-50%, 20px);\n}\n\n/*# sourceMappingURL=message.vue.map */"]},media:void 0})},L,"data-v-1904a750",0,0,0,t)},props:{time:{type:Number,default:3e3},ai_url:{type:String,default:()=>"https://ai.pkulaw.com"},base_url:{type:String,default:()=>"https://www.pkulaw.com"},gateway_url:{type:String,default:()=>"https://gateway.pkulaw.com"},userType:{type:Number,default:0},library:{type:String,default:""},libraryName:{type:String,default:""},getAxios:{type:Function,default:()=>Promise.resolve()},postAxios:{type:Function,default:()=>Promise.resolve()}},data:()=>({openDialog:!1,hasLvaiduo:"",data:[],haveAuth:!1,LawyerAI:null}),computed:{name(){return this.haveAuth?"lvAiduo":this.libraryName}},methods:{start(n){if(1==this.userType)return this.changeModule(!0),void(this.hasLvaiduo="company");this.data=n,this.postAxios(this.gateway_url+C,{product:this.library,productType:"expiration"}).then(n=>{this.haveAuth=n.haveAuth,this.haveAuth?this.getLvaiduo():(this.changeModule(!0),this.hasLvaiduo="alert")})},async getLvaiduo(){try{if(null===this.LawyerAI){const n=await this.getAxios(this.ai_url+b);this.LawyerAI=n.LawyerAI}this.changeModule(!0),this.hasLvaiduo=this.LawyerAI?"knowledge":"alert"}catch(n){console.error("获取律爱多状态失败:",n)}},changeModule(n,r){this.openDialog=n,n||(document.body.classList.remove("dialog-sub-scroll"),r?(this.timer=!0,this.openMessage(r.type,r.default)):this.$emit("close"))},openMessage(n,r){this.$refs.messageRef.start(n,r)},close(){this.timer&&(this.$emit("close"),this.timer=!1)},addLog(){this.$emit("addLog")}}};var R=function(){var n=this,r=n._self._c||n.$createElement;return r("div",{staticClass:"app"},[n.openDialog?r("div",{staticClass:"knowledge"},["knowledge"==n.hasLvaiduo?r("addModule",{attrs:{ai_url:n.ai_url,data:n.data,getAxios:n.getAxios,postAxios:n.postAxios},on:{changeModule:n.changeModule,openMessage:n.openMessage,addLog:n.addLog}}):"alert"==n.hasLvaiduo?r("alertModule",{attrs:{libraryName:n.name},on:{changeModule:n.changeModule}}):"company"==n.hasLvaiduo?r("isCompany",{attrs:{base_url:n.base_url,postAxios:n.postAxios},on:{changeModule:n.changeModule}}):n._e()],1):n._e(),n._v(" "),r("MessageModule",{ref:"messageRef",attrs:{time:n.time},on:{close:n.close}})],1)};R._withStripped=!0;return r({render:R,staticRenderFns:[]},function(n){n&&n("data-v-9107a454_0",{source:".app[data-v-9107a454] {\n  box-sizing: content-box;\n  margin: 0;\n  padding: 0;\n  font-size: 14px;\n  line-height: 1.2;\n  color: #2f2e3f;\n}\n.app[data-v-9107a454]  * {\n  box-sizing: content-box;\n  margin: 0;\n  padding: 0;\n}\n.knowledge[data-v-9107a454] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  overflow: auto;\n  z-index: 9998;\n}\n\n/*# sourceMappingURL=App.vue.map */",map:{version:3,sources:["C:\\Users\\<USER>\\Desktop\\Knowledge\\src\\App.vue","App.vue"],names:[],mappings:"AAwKA;EACA,uBAAA;EACA,SAAA;EACA,UAAA;EACA,eAAA;EACA,gBAAA;EACA,cAAA;ACvKA;ADwKA;EACA,uBAAA;EACA,SAAA;EACA,UAAA;ACtKA;ADyKA;EACA,eAAA;EACA,MAAA;EACA,OAAA;EACA,QAAA;EACA,SAAA;EACA,oCAAA;EACA,cAAA;EACA,aAAA;ACtKA;;AAEA,kCAAkC",file:"App.vue",sourcesContent:['<template>\n     <div class="app">\n        <div class="knowledge" v-if="openDialog">\n            <addModule v-if="hasLvaiduo==\'knowledge\'" \n                :ai_url="ai_url"\n                :data="data"\n                :getAxios="getAxios" \n                :postAxios="postAxios"\n                @changeModule="changeModule" \n                @openMessage="openMessage" \n                @addLog="addLog" />\n            <alertModule v-else-if="hasLvaiduo==\'alert\'" :libraryName="name" @changeModule="changeModule" />\n            <isCompany v-else-if="hasLvaiduo==\'company\'" :base_url="base_url" :postAxios="postAxios" @changeModule="changeModule" />\n        </div>\n        <MessageModule ref="messageRef" :time="time" @close="close" />  \n    </div>\n</template>\n\n<script>\nimport addModule from \'./components/index\'\nimport alertModule from \'./components/alert\'\nimport isCompany from \'./components/IsCompany\'\nimport MessageModule from \'./module/message\'\nimport apis from \'./utils/api\'\nexport default {\n    name: \'index\',\n    components: {\n        addModule,\n        alertModule,\n        isCompany,\n        MessageModule\n    },\n    props: {\n        time: {\n            type: Number,\n            default: 3000\n        },\n        ai_url: {\n            type: String,\n            default: () => {\n                return {"VUE_AI_URL":"https://ai.pkulaw.com","VUE_BASE_URL":"https://www.pkulaw.com","VUE_GATEWAY_URL":"https://gateway.pkulaw.com","VUE_KC_BASE":"https://cas.pkulaw.com"}[\'VUE_AI_URL\']\n            }\n        },\n        base_url: {\n            type: String,\n            default: () => {\n                return {"VUE_AI_URL":"https://ai.pkulaw.com","VUE_BASE_URL":"https://www.pkulaw.com","VUE_GATEWAY_URL":"https://gateway.pkulaw.com","VUE_KC_BASE":"https://cas.pkulaw.com"}[\'VUE_BASE_URL\']\n            }\n        },\n        gateway_url: {\n            type: String,\n            default: () => {\n                return {"VUE_AI_URL":"https://ai.pkulaw.com","VUE_BASE_URL":"https://www.pkulaw.com","VUE_GATEWAY_URL":"https://gateway.pkulaw.com","VUE_KC_BASE":"https://cas.pkulaw.com"}[\'VUE_GATEWAY_URL\']\n            }\n        },\n\n        userType: {\n            type: Number,\n            default: 0\n        },\n        library: {\n            type: String,\n            default: \'\'\n        },\n        libraryName: {\n            type: String,\n            default: \'\'\n        },\n        getAxios: {\n            type: Function,\n            default: () => {\n                return Promise.resolve()\n            }\n        },\n        postAxios: {\n            type: Function,\n            default: () => {\n                return Promise.resolve()\n            }\n        }\n    },\n    data() {\n        return {\n            openDialog: false,\n            hasLvaiduo: \'\',\n\n            data: [],\n\n            haveAuth: false,\n            LawyerAI: null\n        }   \n    },\n    computed: {\n        name() {\n            if(this.haveAuth){\n                return \'lvAiduo\'\n            }else{\n                return this.libraryName\n            }\n        }\n    },\n    methods: {\n        start(arr) {\n            if(this.userType==1){\n                this.changeModule(true)\n                this.hasLvaiduo = \'company\'\n                return\n            }\n            this.data = arr\n            this.postAxios(this.gateway_url+apis.hasLibrary, {\n                product: this.library,\n                productType: \'expiration\'\n            }).then(res=>{\n                this.haveAuth = res.haveAuth\n                if (this.haveAuth) {\n                    this.getLvaiduo()\n                } else {\n                    this.changeModule(true)\n                    this.hasLvaiduo = \'alert\'\n                }\n            })\n        },\n        async getLvaiduo() {\n            try {\n                if (this.LawyerAI === null) {\n                    const res = await this.getAxios(this.ai_url+apis.hasLvaiduo)\n                    this.LawyerAI = res.LawyerAI\n                }\n\n                this.changeModule(true)\n                if (this.LawyerAI) {\n                    this.hasLvaiduo = \'knowledge\'\n                } else {\n                    this.hasLvaiduo = \'alert\'\n                }\n            } catch (error) {\n                console.error(\'获取律爱多状态失败:\', error)\n            }\n        },\n        changeModule(type,obj){\n            this.openDialog = type\n            if(!type){\n                document.body.classList.remove(\'dialog-sub-scroll\')\n                if(obj){\n                    this.timer = true\n                    this.openMessage(obj.type, obj.default)\n                }else{\n                    this.$emit(\'close\')\n                }\n            }\n        },\n        openMessage(type, msg){\n            this.$refs[\'messageRef\'].start(type, msg)\n        },\n        close(){\n            if(this.timer){\n                this.$emit(\'close\')\n                this.timer = false\n            }\n        },\n        addLog(){\n            this.$emit(\'addLog\')\n        }\n    }\n}\n<\/script>\n\n<style lang="scss" scoped>\n.app{   \n    box-sizing: content-box;\n    margin: 0;\n    padding: 0;\n    font-size: 14px;\n    line-height: 1.2;\n    color: #2f2e3f;\n    ::v-deep *{\n        box-sizing: content-box;\n        margin: 0;\n        padding: 0;\n    }\n}\n.knowledge{   \n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(0, 0, 0, 0.5);\n    overflow: auto;\n    z-index: 9998;\n}\n</style>',".app {\n  box-sizing: content-box;\n  margin: 0;\n  padding: 0;\n  font-size: 14px;\n  line-height: 1.2;\n  color: #2f2e3f;\n}\n.app ::v-deep * {\n  box-sizing: content-box;\n  margin: 0;\n  padding: 0;\n}\n\n.knowledge {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  overflow: auto;\n  z-index: 9998;\n}\n\n/*# sourceMappingURL=App.vue.map */"]},media:void 0})},S,"data-v-9107a454",0,0,0,t)});
