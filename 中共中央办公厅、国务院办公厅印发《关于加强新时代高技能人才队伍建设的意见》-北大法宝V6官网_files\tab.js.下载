/*
	页面结构 包含指定class即可
		选项卡固定class
		.tab-nav > .tab-nav-item
		内容固定class
		.tab-col > .tab-col-item
	示例结构
	<div class="tab-wrap" tab="1">
        <div class="tab-nav">
            <a href="javascript:;" class="tab-nav-item current">法规</a>
            <a href="javascript:;" class="tab-nav-item">案例</a>
            <a href="javascript:;" class="tab-nav-item">期刊</a>
        </div>
        <ul class="tab-col">
            <li class="tab-col-item">法规内容</li>
            <li class="tab-col-item">案例内容</li>
            <li class="tab-col-item">期刊内容</li>
        </ul>
    </div>
	使用方法
	$('[tab=1]').tab({
		mouse: 'click',   //切换方式：over，click, toggle(默认收起，点击展开，再次点击收起，当设置为toggle后以下设置不起作用)
		autoPlay: false,  //播放方式：false，true
		curDisplay: 1,     //当前第一个打开
		changeMethod: 'opacity'  //切换选项：默认default，horizontal，vertical，opacity这4种方式
	});
*/

(function($, window, document, undefined) {
	var Plugin = function(elem, options) {
		this.$wrapper = elem;
		this.$class = this.$wrapper.selector.slice(1);
		this.$tabNav = this.$wrapper.find('.' + this.$class + '-nav').find('.' + this.$class + '-nav-item');
		this.$tabCol = this.$wrapper.find('.' + this.$class + '-col');
		this.$tabColItem = this.$tabCol.find('.' + this.$class + '-col-item');
		this.timer = null;
		this.playTimer = null
		this.iNow = -1;
		this.defaults = {
			curDisplay: 1,
			mouse: 'click',
			changeMethod: 'default',
			autoPlay: false
		};
		this.opts = $.extend({}, this.defaults, options);
	};
	Plugin.prototype = {
		inital: function() {
			var self = this;
			this.setData();
			this.tabInital();
			if(this.opts.mouse === 'click') {
				this.$tabNav.click(function () {
					var index = $(this).index(); 
					var tabType = $(this).attr('tabType');
					if (tabType == "projectProposal") {//民法典立法分析报告只跳转地址，不参与
						return;
					}
					if (self.iNow != index) {
						self.changeTab(index);
						self.iNow = index;
					}
				});
			} else if(this.opts.mouse === 'toggle') {
				this.$tabNav.removeClass('current')
				this.$tabColItem.hide()
				this.$tabNav.click(function() {
					//$(this).siblings().removeClass('current');
					$(this).addClass('current').siblings().removeClass('current');
					self.$tabColItem.eq($(this).index()).toggle().siblings().hide();
				});
			} else if(this.opts.mouse === 'over') {
				this.$tabNav.hover(function() {
					var cur_obj = this;
					clearTimeout(self.timer);
					self.timer = setTimeout(function() {
						self.changeTab($(cur_obj).index());
					}, 30);
					self.iNow = $(this).index();
				}, function() {
					clearTimeout(self.timer);
				});
			} else {
				this.$tabNav.click(function() {
					var index = $(this).index();
					var tabType = $(this).attr('tabType');
					if (tabType == "projectProposal") {//民法典立法分析报告只跳转地址，不参与
						return;
					}
					if (self.iNow != index) {
						self.changeTab(index);
						self.iNow = index;
					}
				});
			}
			if(this.opts.autoPlay) {
				clearInterval(this.playTimer);
				this.playTimer = setInterval(function() {
					self.autoPlay();
				}, 1000);
				this.$wrapper.hover(function() {
					clearInterval(self.playTimer);
				}, function() {
					self.playTimer = setInterval(function() {
						self.autoPlay();
					}, 1000);
				});
			}
		},
		setData: function() {
			var tabCont_w = this.$tabColItem.width();
			var tabCont_h = this.$tabColItem.height();
			var tabCont_len = this.$tabColItem.length;
			switch(this.opts.changeMethod) {
				case 'default':
					this.$tabColItem.css({
						display: 'none'
					});
					break;
				case 'horizontal':
					this.$tabCol.css({
						width: tabCont_w * tabCont_len
					});
					this.$tabColItem.css({
						float: 'left'
					});
					break;
				case 'vertical':
					this.$tabCol.css({
						height: tabCont_h * tabCont_len
					});
					break;
				case 'opacity':
					this.$tabColItem.css({
						display: 'none'
					});
					break;
				default:
					this.$tabColItem.css({
						display: 'none'
					});
					break;
			}
		},
		tabInital: function() {
			var curNum = this.opts.curDisplay - 1;
			this.$tabNav.removeClass('current');
			this.$tabNav.eq(curNum).addClass('current');
			if(this.opts.changeMethod === 'default' || this.opts.changeMethod === 'opacity') {
				this.$tabColItem.eq(curNum).css({
					display: 'block'
				});
			} else if(this.opts.changeMethod === 'horizontal') {
				this.$tabCol.css({
					left: -curNum * this.$tabColItem.width()
				});
			} else if(this.opts.changeMethod === 'vertical') {
				this.$tabCol.css({
					top: -curNum * this.$tabColItem.height()
				});
			} else {
				this.$tabColItem.eq(curNum).css({
					display: 'block'
				});
			}
			this.iNow = this.opts.curDisplay - 1;
		},
		changeTab: function(index) {
			this.$tabNav.removeClass('current');
			this.$tabNav.eq(index).addClass('current');
			switch(this.opts.changeMethod) {
				case 'default':
					this.$tabColItem.css({
						display: 'none'
					});
					this.$tabColItem.eq(index).css({
						display: 'block'
					});
					break;
				case 'horizontal':
					this.$tabCol.stop().animate({
						left: this.$tabColItem.width() * -index
					});
					break;
				case 'vertical':
					this.$tabCol.stop().animate({
						top: this.$tabColItem.height() * -index
					});
					break;
				case 'opacity':
					this.$tabColItem.stop().fadeOut();
					this.$tabColItem.eq(index).stop().fadeIn()
					break;
				default:
					this.$tabColItem.css({
						display: 'none'
					});
					this.$tabColItem.eq(index).css({
						display: 'block'
					});
					break;
			}
		},
		autoPlay: function() {
			if(this.iNow === this.$tabNav.length - 1) {
				this.iNow = 0;
			} else {
				this.iNow++;
			}
			this.changeTab(this.iNow);
		},
		constructor: Plugin
	};
	$.fn.tab = function(options) {
		var plugin = new Plugin(this, options);
		return plugin.inital();
	};
})(window.jQuery, window, document);