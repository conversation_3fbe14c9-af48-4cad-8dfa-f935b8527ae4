// 弹窗组件
// 弹窗组件
async function createModal1(config) {
    const targetDiv = document.getElementById('modalContainer');
    const overlay = document.createElement('div');
    const modal = document.createElement('div');
    let header = null;
    let title = null;
    const content = document.createElement('div');
    let closeBtn = null;

    // 动态计算最大高度
    const maxHeight = window.innerHeight;
    const contentStyle = Object.assign({}, config.contentStyle);
    contentStyle.maxHeight = `${maxHeight}px`;
    // contentStyle.overflowY = 'auto'; // 允许垂直滚动

    // 创建样式标签并插入到文档头部
    const styleElement = document.createElement('style');
    styleElement.type = 'text/css';
    styleElement.textContent = `
        /* 定义滚动条的基本样式 */
        ${content.id}::-webkit-scrollbar {
            width: 6px; /* 滚动条宽度 */
        }

        /* 定义滚动条轨道的样式 */
        ${content.id}::-webkit-scrollbar-track {
            background: #f1f1f1; /* 轨道背景颜色 */
        }

        /* 定义滚动条滑块的样式 */
        ${content.id}::-webkit-scrollbar-thumb {
            background: #ccc; /* 滑块颜色 */
            border-radius: 6px;
        }

        /* 当鼠标悬停在滑块上时的样式 */
        ${content.id}::-webkit-scrollbar-thumb:hover {
            background: #999; /* 鼠标悬停时的滑块颜色 */
        }
    `;
    document.head.appendChild(styleElement);

    // 应用配置项
    Object.assign(overlay.style, config.overlayStyle);
    Object.assign(modal.style, config.modalStyle);
    Object.assign(content.style, contentStyle);

    // 创建自定义内容
    if (config.customContent) {
        const customContent = await config.customContent();

        // 将自定义内容添加到模态对话框中
        content.appendChild(customContent);

        // 绑定事件处理器
        if (config.customContentEventHandlers) {
            Object.entries(config.customContentEventHandlers).forEach(([eventType, handler]) => {
                const elements = customContent.querySelectorAll(config.customContentSelector);
                elements.forEach(element => {
                    element.addEventListener(eventType, handler);
                });
            });
        }
    }

    // 如果配置了标题，则创建并添加
    if (config.title) {
        header = document.createElement('header');
        title = document.createElement('h2');
        title.innerHTML = config.title;
        header.appendChild(title);
        modal.appendChild(header);
    }

    // 如果配置了关闭按钮，则创建并添加
    if (config.showCloseButton !== false) {
        closeBtn = document.createElement('span'); // 使用button元素
        if (config.closeIcon) {
            closeBtn.innerHTML = `<i class="${config.closeIcon}"></i>`; // 使用图标
        } else {
            closeBtn.textContent = config.closeText || '×';
        }
        closeBtn.classList.add('close'); // 添加class="close"
        closeBtn.addEventListener('click', () => {
            overlay.remove();
            if (typeof config.onClose === 'function') {
                config.onClose(); // 调用回调函数
            }
        });
        if (header) {
            header.appendChild(closeBtn);
        } else {
            modal.appendChild(closeBtn);
        }

        closeBtn.className = 'close'; // 添加class="close"
    }

    // 如果配置了iframe，则创建并添加
    if (config.iframeConfig) {
        const iframe = document.createElement('iframe');
        iframe.src = config.iframeConfig.src;
        iframe.width = config.iframeConfig.width || '100%';
        iframe.height = config.iframeConfig.height || '300';
        iframe.id = config.iframeConfig.id || 'iframe';
        iframe.frameBorder = '0';
        iframe.allowFullscreen = true;

        content.appendChild(iframe);
    } else if (config.vueComponent) {
        // 如果配置了 Vue 组件，则创建并挂载 Vue 组件
        const component = config.vueComponent;
        const app = Vue.createApp(component);
        app.mount(content);
        config.vueComponentApp = app; // 保存应用实例以便后续卸载
    } else if (config.contentUrl) {
        // 根据配置加载内容
        const response = await fetch(config.contentUrl);
        const htmlContent = await response.text();
        content.innerHTML = htmlContent;
    } else {
        content.innerHTML = config.content;
    }

    // 创建按钮并添加到内容区域
    if (config.buttonsConfig) {
        const buttonsWrapper = document.createElement('div'); // 创建按钮容器
        buttonsWrapper.className = 'buttons-wrapper'; // 添加类名以方便样式化

        config.buttonsConfig.forEach((buttonConfig) => {
            const button = document.createElement('span');
            button.textContent = buttonConfig.text || 'Button';
            button.className = buttonConfig.className || ''; // 添加按钮的class
            button.addEventListener('click', () => {
                if (typeof buttonConfig.onClick === 'function') {
                    buttonConfig.onClick(); // 调用按钮点击回调
                }
                if (buttonConfig.closeOnClick) {
                    overlay.remove();
                    if (typeof config.onClose === 'function') {
                        config.onClose(); // 弹窗关闭时调用回调
                    }
                }
            });
            buttonsWrapper.appendChild(button); // 将按钮添加到容器中
        });

        content.appendChild(buttonsWrapper); // 将按钮容器添加到内容区域
    }

    // 组装DOM结构
    overlay.className = config.overlayClass ? config.overlayClass + ' overlay' : 'overlay'; // 使用配置的类名或默认值
    modal.className = 'modal';
    content.className = 'content';
    modal.appendChild(content);
    overlay.appendChild(modal);
    targetDiv.appendChild(overlay); // 改为将 overlay 添加到目标 div 中

    // 显示遮罩层
    overlay.style.display = 'block';

    // 设置遮罩层点击事件
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay && config.closeOnOverlayClick) {
            overlay.remove();
            if (typeof config.onClose === 'function') {
                config.onClose(); // 调用回调函数
            }
        }
    });

    // 添加自动关闭逻辑
    if (config.autoCloseTime) {
        setTimeout(() => {
            overlay.remove();
            if (typeof config.onClose === 'function') {
                config.onClose(); // 调用回调函数
            }
        }, config.autoCloseTime);
    }

    // 添加加载完成的回调
    if (typeof config.onLoad === 'function') {
        config.onLoad(content); // 传递 content 元素作为参数
    }
}

// 弹窗组件
async function createModal(config) {
    const targetDiv = document.getElementById('modalContainer');
    const overlay = document.createElement('div');
    const modal = document.createElement('div');
    let header = null;
    let title = null;
    const content = document.createElement('div');
    let closeBtn = null;

    // 动态计算最大高度
    const maxHeight = window.innerHeight;
    const contentStyle = Object.assign({}, config.contentStyle);
    contentStyle.maxHeight = `${maxHeight}px`;
    // contentStyle.overflowY = 'auto'; // 允许垂直滚动

    // 创建样式标签并插入到文档头部
    const styleElement = document.createElement('style');
    styleElement.type = 'text/css';
    styleElement.textContent = `
        /* 定义滚动条的基本样式 */
        ${content.id}::-webkit-scrollbar {
            width: 6px; /* 滚动条宽度 */
        }

        /* 定义滚动条轨道的样式 */
        ${content.id}::-webkit-scrollbar-track {
            background: #f1f1f1; /* 轨道背景颜色 */
        }

        /* 定义滚动条滑块的样式 */
        ${content.id}::-webkit-scrollbar-thumb {
            background: #ccc; /* 滑块颜色 */
            border-radius: 6px;
        }

        /* 当鼠标悬停在滑块上时的样式 */
        ${content.id}::-webkit-scrollbar-thumb:hover {
            background: #999; /* 鼠标悬停时的滑块颜色 */
        }
    `;
    document.head.appendChild(styleElement);

    // 应用配置项
    Object.assign(overlay.style, config.overlayStyle);
    Object.assign(modal.style, config.modalStyle);
    Object.assign(content.style, contentStyle);

    // 创建自定义内容
    if (config.customContent) {
        const customContent = await config.customContent();

        // 将自定义内容添加到模态对话框中
        content.appendChild(customContent);

        // 绑定事件处理器
        if (config.customContentEventHandlers) {
            Object.entries(config.customContentEventHandlers).forEach(([eventType, handler]) => {
                const elements = customContent.querySelectorAll(config.customContentSelector);
                elements.forEach(element => {
                    element.addEventListener(eventType, handler);
                });
            });
        }
    }

    // 如果配置了标题，则创建并添加
    if (config.title) {
        header = document.createElement('header');
        title = document.createElement('h2');
        title.innerHTML = config.title;
        header.appendChild(title);
        modal.appendChild(header);
    }

    // 如果配置了关闭按钮，则创建并添加
    if (config.showCloseButton !== false) {
        closeBtn = document.createElement('span'); // 使用button元素
        if (config.closeIcon) {
            closeBtn.innerHTML = `<i class="${config.closeIcon}"></i>`; // 使用图标
        } else {
            closeBtn.textContent = config.closeText || '×';
        }
        closeBtn.classList.add('close'); // 添加class="close"
        closeBtn.addEventListener('click', () => {
            overlay.remove();
            if (typeof config.onClose === 'function') {
                config.onClose(); // 调用回调函数
            }
        });
        if (header) {
            header.appendChild(closeBtn);
        } else {
            modal.appendChild(closeBtn);
        }

        closeBtn.className = 'close'; // 添加class="close"
    }

    // 如果配置了iframe，则创建并添加
    if (config.iframeConfig) {
        const iframe = document.createElement('iframe');
        iframe.src = config.iframeConfig.src;
        iframe.width = config.iframeConfig.width || '100%';
        iframe.height = config.iframeConfig.height || '300';
        iframe.id = config.iframeConfig.id || 'iframe';
        iframe.frameBorder = '0';
        iframe.allowFullscreen = true;

        content.appendChild(iframe);
    } else if (config.vueComponent) {
        // 如果配置了 Vue 组件，则创建并挂载 Vue 组件
        const component = config.vueComponent;
        const app = Vue.createApp(component);
        app.mount(content);
        config.vueComponentApp = app; // 保存应用实例以便后续卸载
    } else if (config.contentUrl) {
        // 根据配置加载内容
        const response = await fetch(config.contentUrl);
        const htmlContent = await response.text();
        content.innerHTML = htmlContent;
    } else {
        content.innerHTML = config.content;
    }

    // 创建按钮并添加到内容区域
    if (config.buttonsConfig) {
        const buttonsWrapper = document.createElement('div'); // 创建按钮容器
        buttonsWrapper.className = 'buttons-wrapper'; // 添加类名以方便样式化

        config.buttonsConfig.forEach((buttonConfig) => {
            const button = document.createElement('span');
            button.textContent = buttonConfig.text || 'Button';
            button.className = buttonConfig.className || ''; // 添加按钮的class
            button.addEventListener('click', () => {
                if (typeof buttonConfig.onClick === 'function') {
                    buttonConfig.onClick(); // 调用按钮点击回调
                }
                if (buttonConfig.closeOnClick) {
                    overlay.remove();
                    if (typeof config.onClose === 'function') {
                        config.onClose(); // 弹窗关闭时调用回调
                    }
                }
            });
            buttonsWrapper.appendChild(button); // 将按钮添加到容器中
        });

        content.appendChild(buttonsWrapper); // 将按钮容器添加到内容区域
    }

    // 组装DOM结构
    overlay.className = config.overlayClass ? config.overlayClass + ' overlay' : 'overlay'; // 使用配置的类名或默认值
    modal.className = 'modal';
    content.className = 'content';
    modal.appendChild(content);
    overlay.appendChild(modal);
    targetDiv.appendChild(overlay); // 改为将 overlay 添加到目标 div 中

    // 显示遮罩层
    overlay.style.display = 'block';

    // 设置遮罩层点击事件
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay && config.closeOnOverlayClick) {
            overlay.remove();
            if (typeof config.onClose === 'function') {
                config.onClose(); // 调用回调函数
            }
        }
    });

    // 添加自动关闭逻辑
    if (config.autoCloseTime) {
        setTimeout(() => {
            overlay.remove();
            if (typeof config.onClose === 'function') {
                config.onClose(); // 调用回调函数
            }
        }, config.autoCloseTime);
    }

    // 添加加载完成的回调
    if (typeof config.onLoad === 'function') {
        config.onLoad(content); // 传递 content 元素作为参数
    }
}

// 自定义模块
// const customModule = async () => {
//     // 创建自定义模块的DOM结构
//     const moduleDiv = document.createElement('div');
//     moduleDiv.innerHTML = `
//         <h2>Welcome to our site!</h2>
//         <p>Please fill out the form below:</p>
//         <form>
//             <label for="name">Name:</label>
//             <input type="text" id="name" name="name">
//             <label for="email">Email:</label>
//             <input type="email" id="email" name="email">
//             <button type="submit">Submit</button>
//         </form>
//     `;
//     return moduleDiv;
// };

// 弹窗使用示例
// createModal({
//     customContent:customContent, // 允许插入自定义模块
//     overlayClass: 'custom-overlay', // 设置弹窗class名
//     title: 'Custom Modal Title', // 设置title
//     contentUrl: 'login-register.html', // 如果使用 contentUrl，则 content 属性无效
//     iframeConfig: { // 允许插入iframe
//         src: 'https://example.com', // iframe地址
//         width: '100%',
//         height: '690',
//     },
//     content:'<h2>标题</h2><div>内容</div>',
//     closeText: 'Close', // 关闭按钮文本
//     closeIcon: 'iconfont icon-guanbi-icon',
//     showCloseButton: true, // 是否显示关闭按钮
//     closeOnOverlayClick: true, // 遮罩层点击关闭
//     autoCloseTime: 500000, // 自动关闭时间，单位毫秒
//     overlayStyle: { backgroundColor: 'rgba(0, 0, 0, 0.7)' }, // 遮罩层样式
//     modalStyle: { width: '50%', maxWidth: '600px' }, // 弹窗大小
//     contentStyle: { fontSize: '16px', color: '#333' }, // 内容样式
//     onClose: function () { // 关闭回调函数
//         console.log('Modal closed');
//         // 这里可以执行任何清理工作或其他操作
//     },
//     // ... 其他配置 ...
//     buttonsConfig: [
//         {
//             text: 'Submit',
//             className: 'submit-button', // 添加class
//             onClick: function () {
//                 console.log('Submit button clicked!');
//                 // 处理提交按钮点击的逻辑
//             },
//             closeOnClick: true, // 点击后关闭弹窗
//         },
//         {
//             text: 'Cancel',
//             className: 'cancel-button', // 添加class
//             onClick: function () {
//                 console.log('Cancel button clicked!');
//                 // 处理取消按钮点击的逻辑
//             },
//             closeOnClick: true, // 点击后关闭弹窗
//         },
//     ],
// });