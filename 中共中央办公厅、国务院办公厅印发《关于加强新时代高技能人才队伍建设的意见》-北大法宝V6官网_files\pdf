(function(n,t){"use strict";typeof define=="function"&&define.amd?define("pdfjs-dist/build/pdf",["exports"],t):typeof exports!="undefined"?t(exports):t(n.pdfjsDistBuildPdf={})})(this,function(n){"use strict";var i="1.4.20",u="b15f335",r=typeof document!="undefined"&&document.currentScript?document.currentScript.src:null,t={};(function(){(function(n,t){t(n.pdfjsSharedGlobal={})})(this,function(n){var t=typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:this,r=typeof window=="undefined";t.PDFJS||(t.PDFJS={});typeof i!="undefined"&&(t.PDFJS.version=i);typeof i!="undefined"&&(t.PDFJS.build=u);t.PDFJS.pdfBug=!1;n.globalScope=t;n.isWorker=r;n.PDFJS=t.PDFJS}),function(n,t){t(n.pdfjsDisplayDOMUtils={},n.pdfjsSharedGlobal)}(this,function(n,t){var r=t.PDFJS,i=function(){function t(){}var i=["ms","Moz","Webkit","O"],n={};return t.getProp=function(t,r){var f,e,o,u,s;if(arguments.length===1&&typeof n[t]=="string")return n[t];if(r=r||document.documentElement,f=r.style,typeof f[t]=="string")return n[t]=t;for(o=t.charAt(0).toUpperCase()+t.slice(1),u=0,s=i.length;u<s;u++)if(e=i[u]+o,typeof f[e]=="string")return n[t]=e;return n[t]="undefined"},t.setProp=function(n,t,i){var r=this.getProp(n);r!=="undefined"&&(t.style[r]=i)},t}();r.CustomStyle=i;n.CustomStyle=i}),function(n,t){t(n.pdfjsSharedUtil={},n.pdfjsSharedGlobal)}(this,function(n,t){function st(n){i.verbosity>=i.VERBOSITY_LEVELS.infos&&console.log("Info: "+n)}function f(n){i.verbosity>=i.VERBOSITY_LEVELS.warnings&&console.log("Warning: "+n)}function g(n){f("Deprecated API usage: "+n)}function e(n){i.verbosity>=i.VERBOSITY_LEVELS.errors&&(console.log("Error: "+n),console.log(ht()));throw new Error(n);}function ht(){try{throw new Error;}catch(n){return n.stack?n.stack.split("\n").slice(2).join("\n"):""}}function s(n,t){n||e(t)}function ct(n,t){return t?new URL(t,n).href:n}function tt(n,t){if(!n)return!1;var i=/^[a-z][a-z0-9+\-.]*(?=:)/i.exec(n);if(!i)return t;i=i[0].toLowerCase();switch(i){case"http":case"https":case"ftp":case"mailto":case"tel":return!0;default:return!1}}function it(n,t){var r=t&&t.url;n.href=n.title=r?b(r):"";r&&(l()&&(n.target=c[i.externalLinkTarget]),n.rel=i.externalLinkRel)}function h(n,t,i){return Object.defineProperty(n,t,{value:i,enumerable:!0,configurable:!0,writable:!1}),i}function l(){i.openExternalLinksInNewWindow&&(g('PDFJS.openExternalLinksInNewWindow, please use "PDFJS.externalLinkTarget = PDFJS.LinkTarget.BLANK" instead.'),i.externalLinkTarget===u.NONE&&(i.externalLinkTarget=u.BLANK),i.openExternalLinksInNewWindow=!1);switch(i.externalLinkTarget){case u.NONE:return!1;case u.SELF:case u.BLANK:case u.PARENT:case u.TOP:return!0}return f("PDFJS.externalLinkTarget is invalid: "+i.externalLinkTarget),i.externalLinkTarget=u.NONE,!1}function b(n){return typeof n!="string"?(f("The argument for removeNullCharacters must be a string."),n):n.replace(yt,"")}function pt(n){var i,r,u,t,f,e;if(s(n!==null&&typeof n=="object"&&n.length!==undefined,"Invalid argument for bytesToString"),i=n.length,r=8192,i<r)return String.fromCharCode.apply(null,n);for(u=[],t=0;t<i;t+=r)f=Math.min(t+r,i),e=n.subarray(t,f),u.push(String.fromCharCode.apply(null,e));return u.join("")}function wt(n){var i,r,t;for(s(typeof n=="string","Invalid argument for stringToBytes"),i=n.length,r=new Uint8Array(i),t=0;t<i;++t)r[t]=n.charCodeAt(t)&255;return r}function bt(n){return String.fromCharCode(n>>24&255,n>>16&255,n>>8&255,n&255)}function kt(n){for(var t=1,i=0;n>t;)t<<=1,i++;return i}function dt(n,t){return n[t]<<24>>24}function gt(n,t){return n[t]<<8|n[t+1]}function ni(n,t){return(n[t]<<24|n[t+1]<<16|n[t+2]<<8|n[t+3])>>>0}function ti(){var n=new Uint8Array(2),t;return n[0]=1,t=new Uint16Array(n.buffer),t[0]===1}function ii(){var n=document.createElement("canvas"),t,i;return n.width=n.height=1,t=n.getContext("2d"),i=t.createImageData(1,1),typeof i.data.buffer!="undefined"}function ui(n){var t,u=n.length,i=[],r;if(n[0]==="þ"&&n[1]==="ÿ")for(t=2;t<u;t+=2)i.push(String.fromCharCode(n.charCodeAt(t)<<8|n.charCodeAt(t+1)));else for(t=0;t<u;++t)r=ri[n.charCodeAt(t)],i.push(r?String.fromCharCode(r):n.charAt(t));return i.join("")}function fi(n){return decodeURIComponent(escape(n))}function ei(n){return unescape(encodeURIComponent(n))}function oi(n){for(var t in n)return!1;return!0}function si(n){return typeof n=="boolean"}function ft(n){return typeof n=="number"&&(n|0)===n}function hi(n){return typeof n=="number"}function ci(n){return typeof n=="string"}function li(n){return n instanceof Array}function ai(n){return typeof n=="object"&&n!==null&&n.byteLength!==undefined}function k(){var n={};return n.promise=new Promise(function(t,i){n.resolve=t;n.reject=i}),n}function ot(n,t,i){this.sourceName=n;this.targetName=t;this.comObj=i;this.callbackIndex=1;this.postMessageTransfers=!0;var r=this.callbacksCapabilities={},u=this.actionHandler={};this._onComObjOnMessage=function(n){var t=n.data,o,s,f,h,c;t.targetName===this.sourceName&&(t.isReply?(o=t.callbackId,t.callbackId in r?(s=r[o],delete r[o],"error"in t?s.reject(t.error):s.resolve(t.data)):e("Cannot resolve callback "+o)):t.action in u?(f=u[t.action],t.callbackId?(h=this.sourceName,c=t.sourceName,Promise.resolve().then(function(){return f[0].call(f[1],t.data)}).then(function(n){i.postMessage({sourceName:h,targetName:c,isReply:!0,callbackId:t.callbackId,data:n})},function(n){n instanceof Error&&(n=n+"");i.postMessage({sourceName:h,targetName:c,isReply:!0,callbackId:t.callbackId,error:n})})):f[0].call(f[1],t.data)):e("Unknown action from worker: "+t.action))}.bind(this);i.addEventListener("message",this._onComObjOnMessage)}function vi(n,t,i){var r=new Image;r.onload=function(){i.resolve(n,r)};r.onerror=function(){i.resolve(n,null);f("Error during JPEG image loading")};r.src=t}var i=t.PDFJS,r=t.globalScope,d,nt,u,c,rt,a,v,y,p,w,ut,et;i.VERBOSITY_LEVELS={errors:0,warnings:1,infos:5};d=i.OPS={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotations:78,endAnnotations:79,beginAnnotation:80,endAnnotation:81,paintJpegXObject:82,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};nt=i.UNSUPPORTED_FEATURES={unknown:"unknown",forms:"forms",javaScript:"javaScript",smask:"smask",shadingPattern:"shadingPattern",font:"font"};i.isValidUrl=tt;i.addLinkAttributes=it;i.shadow=h;u=i.LinkTarget={NONE:0,SELF:1,BLANK:2,PARENT:3,TOP:4};c=["","_self","_blank","_parent","_top"];i.isExternalLinkTargetSet=l;rt=i.PasswordResponses={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};a=function(){function n(n,t){this.name="PasswordException";this.message=n;this.code=t}return n.prototype=new Error,n.constructor=n,n}();i.PasswordException=a;v=function(){function n(n,t){this.name="UnknownErrorException";this.message=n;this.details=t}return n.prototype=new Error,n.constructor=n,n}();i.UnknownErrorException=v;y=function(){function n(n){this.name="InvalidPDFException";this.message=n}return n.prototype=new Error,n.constructor=n,n}();i.InvalidPDFException=y;p=function(){function n(n){this.name="MissingPDFException";this.message=n}return n.prototype=new Error,n.constructor=n,n}();i.MissingPDFException=p;w=function(){function n(n,t){this.name="UnexpectedResponseException";this.message=n;this.status=t}return n.prototype=new Error,n.constructor=n,n}();i.UnexpectedResponseException=w;var lt=function(){function n(n){this.message=n}return n.prototype=new Error,n.prototype.name="NotImplementedException",n.constructor=n,n}(),at=function(){function n(n,t){this.begin=n;this.end=t;this.message="Missing data ["+n+", "+t+")"}return n.prototype=new Error,n.prototype.name="MissingDataException",n.constructor=n,n}(),vt=function(){function n(n){this.message=n}return n.prototype=new Error,n.prototype.name="XRefParseException",n.constructor=n,n}(),yt=/\x00/g;i.removeNullCharacters=b;Object.defineProperty(i,"isLittleEndian",{configurable:!0,get:function(){return h(i,"isLittleEndian",ti())}});Object.defineProperty(i,"hasCanvasTypedArrays",{configurable:!0,get:function(){return h(i,"hasCanvasTypedArrays",ii())}});ut=function(){function t(n,t){this.buffer=n;this.byteLength=n.length;this.length=t===undefined?this.byteLength>>2:t;r(this.length)}function i(n){return{get:function(){var t=this.buffer,i=n<<2;return(t[i]|t[i+1]<<8|t[i+2]<<16|t[i+3]<<24)>>>0},set:function(t){var i=this.buffer,r=n<<2;i[r]=t&255;i[r+1]=t>>8&255;i[r+2]=t>>16&255;i[r+3]=t>>>24&255}}}function r(r){while(n<r)Object.defineProperty(t.prototype,n,i(n)),n++}t.prototype=Object.create(null);var n=0;return t}();n.Uint32ArrayView=ut;var o=i.Util=function(){function n(){}var t=["rgb(",0,",",0,",",0,")"],i;return n.makeCssRgb=function(n,i,r){return t[1]=n,t[3]=i,t[5]=r,t.join("")},n.transform=function(n,t){return[n[0]*t[0]+n[2]*t[1],n[1]*t[0]+n[3]*t[1],n[0]*t[2]+n[2]*t[3],n[1]*t[2]+n[3]*t[3],n[0]*t[4]+n[2]*t[5]+n[4],n[1]*t[4]+n[3]*t[5]+n[5]]},n.applyTransform=function(n,t){var i=n[0]*t[0]+n[1]*t[2]+t[4],r=n[0]*t[1]+n[1]*t[3]+t[5];return[i,r]},n.applyInverseTransform=function(n,t){var i=t[0]*t[3]-t[1]*t[2],r=(n[0]*t[3]-n[1]*t[2]+t[2]*t[5]-t[4]*t[3])/i,u=(-n[0]*t[1]+n[1]*t[0]+t[4]*t[1]-t[5]*t[0])/i;return[r,u]},n.getAxialAlignedBoundingBox=function(t,i){var r=n.applyTransform(t,i),u=n.applyTransform(t.slice(2,4),i),f=n.applyTransform([t[0],t[3]],i),e=n.applyTransform([t[2],t[1]],i);return[Math.min(r[0],u[0],f[0],e[0]),Math.min(r[1],u[1],f[1],e[1]),Math.max(r[0],u[0],f[0],e[0]),Math.max(r[1],u[1],f[1],e[1])]},n.inverseTransform=function(n){var t=n[0]*n[3]-n[1]*n[2];return[n[3]/t,-n[1]/t,-n[2]/t,n[0]/t,(n[2]*n[5]-n[4]*n[3])/t,(n[4]*n[1]-n[5]*n[0])/t]},n.apply3dTransform=function(n,t){return[n[0]*t[0]+n[1]*t[1]+n[2]*t[2],n[3]*t[0]+n[4]*t[1]+n[5]*t[2],n[6]*t[0]+n[7]*t[1]+n[8]*t[2]]},n.singularValueDecompose2dScale=function(n){var t=[n[0],n[2],n[1],n[3]],i=n[0]*t[0]+n[1]*t[2],e=n[0]*t[1]+n[1]*t[3],o=n[2]*t[0]+n[3]*t[2],r=n[2]*t[1]+n[3]*t[3],u=(i+r)/2,f=Math.sqrt((i+r)*(i+r)-4*(i*r-o*e))/2,s=u+f||1,h=u-f||1;return[Math.sqrt(s),Math.sqrt(h)]},n.normalizeRect=function(n){var t=n.slice(0);return n[0]>n[2]&&(t[0]=n[2],t[2]=n[0]),n[1]>n[3]&&(t[1]=n[3],t[3]=n[1]),t},n.intersect=function(t,i){function e(n,t){return n-t}var r=[t[0],t[2],i[0],i[2]].sort(e),u=[t[1],t[3],i[1],i[3]].sort(e),f=[];if(t=n.normalizeRect(t),i=n.normalizeRect(i),r[0]===t[0]&&r[1]===i[0]||r[0]===i[0]&&r[1]===t[0])f[0]=r[1],f[2]=r[2];else return!1;if(u[0]===t[1]&&u[1]===i[1]||u[0]===i[1]&&u[1]===t[1])f[1]=u[1],f[3]=u[2];else return!1;return f},n.sign=function(n){return n<0?-1:1},i=["","C","CC","CCC","CD","D","DC","DCC","DCCC","CM","","X","XX","XXX","XL","L","LX","LXX","LXXX","XC","","I","II","III","IV","V","VI","VII","VIII","IX"],n.toRoman=function(n,t){var u,r,f;for(s(ft(n)&&n>0,"The number should be a positive integer."),r=[];n>=1e3;)n-=1e3,r.push("M");return u=n/100|0,n%=100,r.push(i[u]),u=n/10|0,n%=10,r.push(i[10+u]),r.push(i[20+n]),f=r.join(""),t?f.toLowerCase():f},n.appendToArray=function(n,t){Array.prototype.push.apply(n,t)},n.prependToArray=function(n,t){Array.prototype.unshift.apply(n,t)},n.extendObj=function(n,t){for(var i in t)n[i]=t[i]},n.getInheritableProperty=function(n,t){while(n&&!n.has(t))n=n.get("Parent");return n?n.get(t):null},n.inherit=function(n,t,i){n.prototype=Object.create(t.prototype);n.prototype.constructor=n;for(var r in i)n.prototype[r]=i[r]},n.loadScript=function(n,t){var i=document.createElement("script"),r=!1;i.setAttribute("src",n);t&&(i.onload=function(){r||t();r=!0});document.getElementsByTagName("head")[0].appendChild(i)},n}(),fr=i.PageViewport=function(){function n(n,t,i,r,u,f){var c,l,s,h,e,o,a,v,y,p;this.viewBox=n;this.scale=t;this.rotation=i;this.offsetX=r;this.offsetY=u;c=(n[2]+n[0])/2;l=(n[3]+n[1])/2;i=i%360;i=i<0?i+360:i;switch(i){case 180:s=-1;h=0;e=0;o=1;break;case 90:s=0;h=1;e=1;o=0;break;case 270:s=0;h=-1;e=-1;o=0;break;default:s=1;h=0;e=0;o=-1}f&&(e=-e,o=-o);s===0?(a=Math.abs(l-n[1])*t+r,v=Math.abs(c-n[0])*t+u,y=Math.abs(n[3]-n[1])*t,p=Math.abs(n[2]-n[0])*t):(a=Math.abs(c-n[0])*t+r,v=Math.abs(l-n[1])*t+u,y=Math.abs(n[2]-n[0])*t,p=Math.abs(n[3]-n[1])*t);this.transform=[s*t,h*t,e*t,o*t,a-s*t*c-e*t*l,v-h*t*c-o*t*l];this.width=y;this.height=p;this.fontScale=t}return n.prototype={clone:function(t){t=t||{};var i="scale"in t?t.scale:this.scale,r="rotation"in t?t.rotation:this.rotation;return new n(this.viewBox.slice(),i,r,this.offsetX,this.offsetY,t.dontFlip)},convertToViewportPoint:function(n,t){return o.applyTransform([n,t],this.transform)},convertToViewportRectangle:function(n){var t=o.applyTransform([n[0],n[1]],this.transform),i=o.applyTransform([n[2],n[3]],this.transform);return[t[0],t[1],i[0],i[1]]},convertToPdfPoint:function(n,t){return o.applyInverseTransform([n,t],this.transform)}},n}(),ri=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];i.createPromiseCapability=k,function(){function n(n){this._status=e;this._handlers=[];try{n.call(this,this._resolve.bind(this),this._reject.bind(this))}catch(t){this._reject(t)}}if(r.Promise){typeof r.Promise.all!="function"&&(r.Promise.all=function(n){var t=0,i=[],u,f,e=new r.Promise(function(n,t){u=n;f=t});return n.forEach(function(n,r){t++;n.then(function(n){i[r]=n;t--;t===0&&u(i)},f)}),t===0&&u(i),e});typeof r.Promise.resolve!="function"&&(r.Promise.resolve=function(n){return new r.Promise(function(t){t(n)})});typeof r.Promise.reject!="function"&&(r.Promise.reject=function(n){return new r.Promise(function(t,i){i(n)})});typeof r.Promise.prototype.catch!="function"&&(r.Promise.prototype.catch=function(n){return r.Promise.prototype.then(undefined,n)});return}var e=0,i=1,t=2,o=500,u={handlers:[],running:!1,unhandledRejections:[],pendingRejectionCheck:!1,scheduleHandlers:function(n){n._status!==e&&((this.handlers=this.handlers.concat(n._handlers),n._handlers=[],this.running)||(this.running=!0,setTimeout(this.runHandlers.bind(this),0)))},runHandlers:function(){for(var f=Date.now()+1;this.handlers.length>0;){var n=this.handlers.shift(),u=n.thisPromise._status,r=n.thisPromise._value;try{u===i?typeof n.onResolve=="function"&&(r=n.onResolve(r)):typeof n.onReject=="function"&&(r=n.onReject(r),u=i,n.thisPromise._unhandledRejection&&this.removeUnhandeledRejection(n.thisPromise))}catch(e){u=t;r=e}if(n.nextPromise._updateStatus(u,r),Date.now()>=f)break}if(this.handlers.length>0){setTimeout(this.runHandlers.bind(this),0);return}this.running=!1},addUnhandledRejection:function(n){this.unhandledRejections.push({promise:n,time:Date.now()});this.scheduleRejectionCheck()},removeUnhandeledRejection:function(n){n._unhandledRejection=!1;for(var t=0;t<this.unhandledRejections.length;t++)this.unhandledRejections[t].promise===n&&(this.unhandledRejections.splice(t),t--)},scheduleRejectionCheck:function(){this.pendingRejectionCheck||(this.pendingRejectionCheck=!0,setTimeout(function(){var r,n,t,i;for(this.pendingRejectionCheck=!1,r=Date.now(),n=0;n<this.unhandledRejections.length;n++)r-this.unhandledRejections[n].time>o&&(t=this.unhandledRejections[n].promise._value,i="Unhandled rejection: "+t,t.stack&&(i+="\n"+t.stack),f(i),this.unhandledRejections.splice(n),n--);this.unhandledRejections.length&&this.scheduleRejectionCheck()}.bind(this),o))}};n.all=function(i){function a(n){u._status!==t&&(f=[],c(n))}var o,c,u=new n(function(n,t){o=n;c=t}),s=i.length,f=[],r,l,e,h;if(s===0)return o(f),u;for(r=0,l=i.length;r<l;++r)e=i[r],h=function(n){return function(i){u._status!==t&&(f[n]=i,s--,s===0&&o(f))}}(r),n.isPromise(e)?e.then(h,a):h(e);return u};n.isPromise=function(n){return n&&typeof n.then=="function"};n.resolve=function(t){return new n(function(n){n(t)})};n.reject=function(t){return new n(function(n,i){i(t)})};n.prototype={_status:null,_value:null,_handlers:null,_unhandledRejection:null,_updateStatus:function(r,f){if(this._status!==i&&this._status!==t){if(r===i&&n.isPromise(f)){f.then(this._updateStatus.bind(this,i),this._updateStatus.bind(this,t));return}this._status=r;this._value=f;r===t&&this._handlers.length===0&&(this._unhandledRejection=!0,u.addUnhandledRejection(this));u.scheduleHandlers(this)}},_resolve:function(n){this._updateStatus(i,n)},_reject:function(n){this._updateStatus(t,n)},then:function(t,i){var r=new n(function(n,t){this.resolve=n;this.reject=t});return this._handlers.push({thisPromise:this,onResolve:t,onReject:i,nextPromise:r}),u.scheduleHandlers(this),r},"catch":function(n){return this.then(undefined,n)}};r.Promise=n}();et=function(){function t(n,t,i){while(n.length<i)n+=t;return n}function n(){this.started={};this.times=[];this.enabled=!0}return n.prototype={time:function(n){this.enabled&&(n in this.started&&f("Timer is already running for "+n),this.started[n]=Date.now())},timeEnd:function(n){this.enabled&&(n in this.started||f("Timer has not been started for "+n),this.times.push({name:n,start:this.started[n],end:Date.now()}),delete this.started[n])},toString:function(){for(var r=this.times,o="",f=0,e,u,s,n=0,i=r.length;n<i;++n)e=r[n].name,e.length>f&&(f=e.length);for(n=0,i=r.length;n<i;++n)u=r[n],s=u.end-u.start,o+=t(u.name," ",f)+" "+s+"ms\n";return o}},n}();i.createBlob=function(n,t){if(typeof Blob!="undefined")return new Blob([n],{type:t});var i=new MozBlobBuilder;return i.append(n),i.getBlob(t)};i.createObjectURL=function(){var n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";return function(t,r){var o,e,u,f;if(!i.disableCreateObjectURL&&typeof URL!="undefined"&&URL.createObjectURL)return o=i.createBlob(t,r),URL.createObjectURL(o);for(e="data:"+r+";base64,",u=0,f=t.length;u<f;u+=3){var s=t[u]&255,h=t[u+1]&255,c=t[u+2]&255,l=s>>2,a=(s&3)<<4|h>>4,v=u+1<f?(h&15)<<2|c>>6:64,y=u+2<f?c&63:64;e+=n[l]+n[a]+n[v]+n[y]}return e}}();ot.prototype={on:function(n,t,i){var r=this.actionHandler;r[n]&&e('There is already an actionName called "'+n+'"');r[n]=[t,i]},send:function(n,t,i){var r={sourceName:this.sourceName,targetName:this.targetName,action:n,data:t};this.postMessage(r,i)},sendWithPromise:function(n,t,i){var u=this.callbackIndex++,f={sourceName:this.sourceName,targetName:this.targetName,action:n,data:t,callbackId:u},r=k();this.callbacksCapabilities[u]=r;try{this.postMessage(f,i)}catch(e){r.reject(e)}return r.promise},postMessage:function(n,t){t&&this.postMessageTransfers?this.comObj.postMessage(n,t):this.comObj.postMessage(n)},destroy:function(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}},function(n){function y(n){return i[n]!==undefined}function h(){a.call(this);this._isInvalid=!0}function c(n){return""==n&&h.call(this),n.toLowerCase()}function l(n){var t=n.charCodeAt(0);return t>32&&t<127&&[34,35,60,62,63,96].indexOf(t)==-1?n:encodeURIComponent(n)}function p(n){var t=n.charCodeAt(0);return t>32&&t<127&&[34,35,60,62,96].indexOf(t)==-1?n:encodeURIComponent(n)}function r(n,r,f){function v(n){et.push(n)}var a=r||"scheme start",b=0,s="",ut=!1,nt=!1,et=[],e,tt,k,g,d,it,rt,ft;n:while((n[b-1]!=t||b==0)&&!this._isInvalid){e=n[b];switch(a){case"scheme start":if(e&&o.test(e))s+=e.toLowerCase(),a="scheme";else if(r){v("Invalid scheme.");break n}else{s="";a="no scheme";continue}break;case"scheme":if(e&&w.test(e))s+=e.toLowerCase();else if(":"==e){if(this._scheme=s,s="",r)break n;y(this._scheme)&&(this._isRelative=!0);a="file"==this._scheme?"relative":this._isRelative&&f&&f._scheme==this._scheme?"relative or authority":this._isRelative?"authority first slash":"scheme data"}else if(r)if(t==e)break n;else{v("Code point not allowed in scheme: "+e);break n}else{s="";b=0;a="no scheme";continue}break;case"scheme data":"?"==e?(this._query="?",a="query"):"#"==e?(this._fragment="#",a="fragment"):t!=e&&"\t"!=e&&"\n"!=e&&"\r"!=e&&(this._schemeData+=l(e));break;case"no scheme":if(f&&y(f._scheme)){a="relative";continue}else v("Missing scheme."),h.call(this);break;case"relative or authority":if("/"==e&&"/"==n[b+1])a="authority ignore slashes";else{v("Expected /, got: "+e);a="relative";continue}break;case"relative":if(this._isRelative=!0,"file"!=this._scheme&&(this._scheme=f._scheme),t==e){this._host=f._host;this._port=f._port;this._path=f._path.slice();this._query=f._query;this._username=f._username;this._password=f._password;break n}else if("/"==e||"\\"==e)"\\"==e&&v("\\ is an invalid code point."),a="relative slash";else if("?"==e)this._host=f._host,this._port=f._port,this._path=f._path.slice(),this._query="?",this._username=f._username,this._password=f._password,a="query";else if("#"==e)this._host=f._host,this._port=f._port,this._path=f._path.slice(),this._query=f._query,this._fragment="#",this._username=f._username,this._password=f._password,a="fragment";else{tt=n[b+1];k=n[b+2];"file"==this._scheme&&o.test(e)&&(tt==":"||tt=="|")&&(t==k||"/"==k||"\\"==k||"?"==k||"#"==k)||(this._host=f._host,this._port=f._port,this._username=f._username,this._password=f._password,this._path=f._path.slice(),this._path.pop());a="relative path";continue}break;case"relative slash":if("/"==e||"\\"==e)"\\"==e&&v("\\ is an invalid code point."),a="file"==this._scheme?"file host":"authority ignore slashes";else{"file"!=this._scheme&&(this._host=f._host,this._port=f._port,this._username=f._username,this._password=f._password);a="relative path";continue}break;case"authority first slash":if("/"==e)a="authority second slash";else{v("Expected '/', got: "+e);a="authority ignore slashes";continue}break;case"authority second slash":if(a="authority ignore slashes","/"!=e){v("Expected '/', got: "+e);continue}break;case"authority ignore slashes":if("/"!=e&&"\\"!=e){a="authority";continue}else v("Expected authority, got: "+e);break;case"authority":if("@"==e){for(ut&&(v("@ already seen."),s+="%40"),ut=!0,g=0;g<s.length;g++){if(d=s[g],"\t"==d||"\n"==d||"\r"==d){v("Invalid whitespace in authority.");continue}if(":"==d&&null===this._password){this._password="";continue}it=l(d);null!==this._password?this._password+=it:this._username+=it}s=""}else if(t==e||"/"==e||"\\"==e||"?"==e||"#"==e){b-=s.length;s="";a="host";continue}else s+=e;break;case"file host":if(t==e||"/"==e||"\\"==e||"?"==e||"#"==e){s.length==2&&o.test(s[0])&&(s[1]==":"||s[1]=="|")?a="relative path":s.length==0?a="relative path start":(this._host=c.call(this,s),s="",a="relative path start");continue}else"\t"==e||"\n"==e||"\r"==e?v("Invalid whitespace in file host."):s+=e;break;case"host":case"hostname":if(":"!=e||nt)if(t==e||"/"==e||"\\"==e||"?"==e||"#"==e){if(this._host=c.call(this,s),s="",a="relative path start",r)break n;continue}else"\t"!=e&&"\n"!=e&&"\r"!=e?("["==e?nt=!0:"]"==e&&(nt=!1),s+=e):v("Invalid code point in host/hostname: "+e);else if(this._host=c.call(this,s),s="",a="port","hostname"==r)break n;break;case"port":if(/[0-9]/.test(e))s+=e;else if(t==e||"/"==e||"\\"==e||"?"==e||"#"==e||r){if(""!=s&&(rt=parseInt(s,10),rt!=i[this._scheme]&&(this._port=rt+""),s=""),r)break n;a="relative path start";continue}else"\t"==e||"\n"==e||"\r"==e?v("Invalid code point in port: "+e):h.call(this);break;case"relative path start":if("\\"==e&&v("'\\' not allowed in path."),a="relative path","/"!=e&&"\\"!=e)continue;break;case"relative path":t!=e&&"/"!=e&&"\\"!=e&&(r||"?"!=e&&"#"!=e)?"\t"!=e&&"\n"!=e&&"\r"!=e&&(s+=l(e)):("\\"==e&&v("\\ not allowed in relative path."),(ft=u[s.toLowerCase()])&&(s=ft),".."==s?(this._path.pop(),"/"!=e&&"\\"!=e&&this._path.push("")):"."==s&&"/"!=e&&"\\"!=e?this._path.push(""):"."!=s&&("file"==this._scheme&&this._path.length==0&&s.length==2&&o.test(s[0])&&s[1]=="|"&&(s=s[0]+":"),this._path.push(s)),s="","?"==e?(this._query="?",a="query"):"#"==e&&(this._fragment="#",a="fragment"));break;case"query":r||"#"!=e?t!=e&&"\t"!=e&&"\n"!=e&&"\r"!=e&&(this._query+=p(e)):(this._fragment="#",a="fragment");break;case"fragment":t!=e&&"\t"!=e&&"\n"!=e&&"\r"!=e&&(this._fragment+=e)}b++}}function a(){this._scheme="";this._schemeData="";this._username="";this._password=null;this._host="";this._port="";this._path=[];this._query="";this._fragment="";this._isInvalid=!1;this._isRelative=!1}function f(n,t){t===undefined||t instanceof f||(t=new f(String(t)));this._url=n;a.call(this);var i=n.replace(/^[ \t\r\n\f]+|[ \t\r\n\f]+$/g,"");r.call(this,i,null,t)}var v=!1,s,i,u,e;try{typeof URL=="function"&&typeof URL.prototype=="object"&&"origin"in URL.prototype&&(s=new URL("b","http://a"),s.pathname="c%20d",v=s.href==="http://a/c%20d")}catch(b){}if(!v){i=Object.create(null);i.ftp=21;i.file=0;i.gopher=70;i.http=80;i.https=443;i.ws=80;i.wss=443;u=Object.create(null);u["%2e"]=".";u[".%2e"]="..";u["%2e."]="..";u["%2e%2e"]="..";var t=undefined,o=/[a-zA-Z]/,w=/[a-zA-Z0-9\+\-\.]/;f.prototype={toString:function(){return this.href},get href(){if(this._isInvalid)return this._url;var n="";return(""!=this._username||null!=this._password)&&(n=this._username+(null!=this._password?":"+this._password:"")+"@"),this.protocol+(this._isRelative?"//"+n+this.host:"")+this.pathname+this._query+this._fragment},set href(n){a.call(this);r.call(this,n)},get protocol(){return this._scheme+":"},set protocol(n){this._isInvalid||r.call(this,n+":","scheme start")},get host(){return this._isInvalid?"":this._port?this._host+":"+this._port:this._host},set host(n){!this._isInvalid&&this._isRelative&&r.call(this,n,"host")},get hostname(){return this._host},set hostname(n){!this._isInvalid&&this._isRelative&&r.call(this,n,"hostname")},get port(){return this._port},set port(n){!this._isInvalid&&this._isRelative&&r.call(this,n,"port")},get pathname(){return this._isInvalid?"":this._isRelative?"/"+this._path.join("/"):this._schemeData},set pathname(n){!this._isInvalid&&this._isRelative&&(this._path=[],r.call(this,n,"relative path start"))},get search(){return this._isInvalid||!this._query||"?"==this._query?"":this._query},set search(n){!this._isInvalid&&this._isRelative&&(this._query="?","?"==n[0]&&(n=n.slice(1)),r.call(this,n,"query"))},get hash(){return this._isInvalid||!this._fragment||"#"==this._fragment?"":this._fragment},set hash(n){this._isInvalid||(this._fragment="#","#"==n[0]&&(n=n.slice(1)),r.call(this,n,"fragment"))},get origin(){var n;if(this._isInvalid||!this._scheme)return"";switch(this._scheme){case"data":case"file":case"javascript":case"mailto":return"null"}return(n=this.host,!n)?"":this._scheme+"://"+n}};e=n.URL;e&&(f.createObjectURL=function(){return e.createObjectURL.apply(e,arguments)},f.revokeObjectURL=function(n){e.revokeObjectURL(n)});n.URL=f}}(r);n.FONT_IDENTITY_MATRIX=[.001,0,0,.001,0,0];n.IDENTITY_MATRIX=[1,0,0,1,0,0];n.OPS=d;n.UNSUPPORTED_FEATURES=nt;n.AnnotationBorderStyleType={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};n.AnnotationFlag={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};n.AnnotationType={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};n.FontType={UNKNOWN:0,TYPE1:1,TYPE1C:2,CIDFONTTYPE0:3,CIDFONTTYPE0C:4,TRUETYPE:5,CIDFONTTYPE2:6,TYPE3:7,OPENTYPE:8,TYPE0:9,MMTYPE1:10};n.ImageKind={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};n.InvalidPDFException=y;n.LinkTarget=u;n.LinkTargetStringMap=c;n.MessageHandler=ot;n.MissingDataException=at;n.MissingPDFException=p;n.NotImplementedException=lt;n.PasswordException=a;n.PasswordResponses=rt;n.StatTimer=et;n.StreamType={UNKNOWN:0,FLATE:1,LZW:2,DCT:3,JPX:4,JBIG:5,A85:6,AHX:7,CCF:8,RL:9};n.TextRenderingMode={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};n.UnexpectedResponseException=w;n.UnknownErrorException=v;n.Util=o;n.XRefParseException=vt;n.assert=s;n.bytesToString=pt;n.combineUrl=ct;n.createPromiseCapability=k;n.deprecated=g;n.error=e;n.info=st;n.isArray=li;n.isArrayBuffer=ai;n.isBool=si;n.isEmptyObj=oi;n.isExternalLinkTargetSet=l;n.isInt=ft;n.isNum=hi;n.isString=ci;n.isValidUrl=tt;n.addLinkAttributes=it;n.loadJpegStream=vi;n.log2=kt;n.readInt8=dt;n.readUint16=gt;n.readUint32=ni;n.removeNullCharacters=b;n.shadow=h;n.string32=bt;n.stringToBytes=wt;n.stringToPDFString=ui;n.stringToUTF8String=fi;n.utf8StringToString=ei;n.warn=f}),function(n,t){t(n.pdfjsDisplayAnnotationLayer={},n.pdfjsSharedUtil,n.pdfjsDisplayDOMUtils)}(this,function(n,t,i){function h(){}var e=t.AnnotationBorderStyleType,f=t.AnnotationType,u=t.Util,a=t.addLinkAttributes,s=t.warn,o=i.CustomStyle;h.prototype={create:function(n){var t=n.data.annotationType;switch(t){case f.LINK:return new v(n);case f.TEXT:return new y(n);case f.WIDGET:return new p(n);case f.POPUP:return new w(n);case f.HIGHLIGHT:return new b(n);case f.UNDERLINE:return new k(n);case f.SQUIGGLY:return new d(n);case f.STRIKEOUT:return new g(n);default:throw new Error('Unimplemented annotation type "'+t+'"');}}};var r=function(){function n(n){this.data=n.data;this.layer=n.layer;this.page=n.page;this.viewport=n.viewport;this.linkService=n.linkService;this.container=this._createContainer()}return n.prototype={_createContainer:function(){var n=this.data,r=this.page,v=this.viewport,t=document.createElement("section"),f=n.rect[2]-n.rect[0],h=n.rect[3]-n.rect[1],i,c,l,a;if(t.setAttribute("data-annotation-id",n.id),i=u.normalizeRect([n.rect[0],r.view[3]-n.rect[1]+r.view[1],n.rect[2],r.view[3]-n.rect[3]+r.view[1]]),o.setProp("transform",t,"matrix("+v.transform.join(",")+")"),o.setProp("transformOrigin",t,-i[0]+"px "+-i[1]+"px"),n.borderStyle.width>0){t.style.borderWidth=n.borderStyle.width+"px";n.borderStyle.style!==e.UNDERLINE&&(f=f-2*n.borderStyle.width,h=h-2*n.borderStyle.width);c=n.borderStyle.horizontalCornerRadius;l=n.borderStyle.verticalCornerRadius;(c>0||l>0)&&(a=c+"px / "+l+"px",o.setProp("borderRadius",t,a));switch(n.borderStyle.style){case e.SOLID:t.style.borderStyle="solid";break;case e.DASHED:t.style.borderStyle="dashed";break;case e.BEVELED:s("Unimplemented border style: beveled");break;case e.INSET:s("Unimplemented border style: inset");break;case e.UNDERLINE:t.style.borderBottomStyle="solid"}n.color?t.style.borderColor=u.makeCssRgb(n.color[0]|0,n.color[1]|0,n.color[2]|0):t.style.borderWidth=0}return t.style.left=i[0]+"px",t.style.top=i[1]+"px",t.style.width=f+"px",t.style.height=h+"px",t},render:function(){throw new Error("Abstract method AnnotationElement.render called");}},n}(),v=function(){function n(n){r.call(this,n)}return u.inherit(n,r,{render:function(){this.container.className="linkAnnotation";var n=document.createElement("a");return a(n,{url:this.data.url}),this.data.url||(this.data.action?this._bindNamedAction(n,this.data.action):this._bindLink(n,"dest"in this.data?this.data.dest:null)),this.container.appendChild(n),this.container},_bindLink:function(n,t){var i=this;n.href=this.linkService.getDestinationHash(t);n.onclick=function(){return t&&i.linkService.navigateTo(t),!1};t&&(n.className="internalLink")},_bindNamedAction:function(n,t){var i=this;n.href=this.linkService.getAnchorUrl("");n.onclick=function(){return i.linkService.executeNamedAction(t),!1};n.className="internalLink"}}),n}(),y=function(){function n(n){r.call(this,n)}return u.inherit(n,r,{render:function(){var n,i,t;return this.container.className="textAnnotation",n=document.createElement("img"),n.style.height=this.container.style.height,n.style.width=this.container.style.width,n.src=PDFJS.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",n.alt="[{{type}} Annotation]",n.dataset.l10nId="text_annotation_type",n.dataset.l10nArgs=JSON.stringify({type:this.data.name}),this.data.hasPopup||(i=new c({container:this.container,trigger:n,color:this.data.color,title:this.data.title,contents:this.data.contents,hideWrapper:!0}),t=i.render(),t.style.left=n.style.width,this.container.appendChild(t)),this.container.appendChild(n),this.container}}),n}(),p=function(){function n(n){r.call(this,n)}return u.inherit(n,r,{render:function(){var n=document.createElement("div"),t,i;return n.textContent=this.data.fieldValue,t=this.data.textAlignment,n.style.textAlign=["left","center","right"][t],n.style.verticalAlign="middle",n.style.display="table-cell",i=this.data.fontRefName?this.page.commonObjs.getData(this.data.fontRefName):null,this._setTextStyle(n,i),this.container.appendChild(n),this.container},_setTextStyle:function(n,t){var i=n.style,r,u;(i.fontSize=this.data.fontSize+"px",i.direction=this.data.fontDirection<0?"rtl":"ltr",t)&&(i.fontWeight=t.black?t.bold?"900":"bold":t.bold?"bold":"normal",i.fontStyle=t.italic?"italic":"normal",r=t.loadedName?'"'+t.loadedName+'", ':"",u=t.fallbackName||"Helvetica, sans-serif",i.fontFamily=r+u)}}),n}(),w=function(){function n(n){r.call(this,n)}return u.inherit(n,r,{render:function(){var t,n;if(this.container.className="popupAnnotation",t='[data-annotation-id="'+this.data.parentId+'"]',n=this.layer.querySelector(t),!n)return this.container;var u=new c({container:this.container,trigger:n,color:this.data.color,title:this.data.title,contents:this.data.contents}),i=parseFloat(n.style.left),r=parseFloat(n.style.width);return o.setProp("transformOrigin",this.container,-(i+r)+"px -"+n.style.top),this.container.style.left=i+r+"px",this.container.appendChild(u.render()),this.container}}),n}(),c=function(){function t(n){this.container=n.container;this.trigger=n.trigger;this.color=n.color;this.title=n.title;this.contents=n.contents;this.hideWrapper=n.hideWrapper||!1;this.pinned=!1}var n=.7;return t.prototype={render:function(){var r=document.createElement("div"),i,t,e,f;if(r.className="popupWrapper",this.hideElement=this.hideWrapper?r:this.container,this.hideElement.setAttribute("hidden",!0),i=document.createElement("div"),i.className="popup",t=this.color,t){var o=n*(255-t[0])+t[0],s=n*(255-t[1])+t[1],h=n*(255-t[2])+t[2];i.style.backgroundColor=u.makeCssRgb(o|0,s|0,h|0)}return e=this._formatContents(this.contents),f=document.createElement("h1"),f.textContent=this.title,this.trigger.addEventListener("click",this._toggle.bind(this)),this.trigger.addEventListener("mouseover",this._show.bind(this,!1)),this.trigger.addEventListener("mouseout",this._hide.bind(this,!1)),i.addEventListener("click",this._hide.bind(this,!0)),i.appendChild(f),i.appendChild(e),r.appendChild(i),r},_formatContents:function(n){for(var f,i=document.createElement("p"),r=n.split(/(?:\r\n?|\n)/),t=0,u=r.length;t<u;++t)f=r[t],i.appendChild(document.createTextNode(f)),t<u-1&&i.appendChild(document.createElement("br"));return i},_toggle:function(){this.pinned?this._hide(!0):this._show(!0)},_show:function(n){n&&(this.pinned=!0);this.hideElement.hasAttribute("hidden")&&(this.hideElement.removeAttribute("hidden"),this.container.style.zIndex+=1)},_hide:function(n){n&&(this.pinned=!1);this.hideElement.hasAttribute("hidden")||this.pinned||(this.hideElement.setAttribute("hidden",!0),this.container.style.zIndex-=1)}},t}(),b=function(){function n(n){r.call(this,n)}return u.inherit(n,r,{render:function(){return this.container.className="highlightAnnotation",this.container}}),n}(),k=function(){function n(n){r.call(this,n)}return u.inherit(n,r,{render:function(){return this.container.className="underlineAnnotation",this.container}}),n}(),d=function(){function n(n){r.call(this,n)}return u.inherit(n,r,{render:function(){return this.container.className="squigglyAnnotation",this.container}}),n}(),g=function(){function n(n){r.call(this,n)}return u.inherit(n,r,{render:function(){return this.container.className="strikeoutAnnotation",this.container}}),n}(),l=function(){return{render:function(n){for(var t,r,u,f=new h,i=0,e=n.annotations.length;i<e;i++)(t=n.annotations[i],t&&t.hasHtml)&&(r={data:t,layer:n.div,page:n.page,viewport:n.viewport,linkService:n.linkService},u=f.create(r),n.div.appendChild(u.render()))},update:function(n){for(var u,i,t=0,r=n.annotations.length;t<r;t++)u=n.annotations[t],i=n.div.querySelector('[data-annotation-id="'+u.id+'"]'),i&&o.setProp("transform",i,"matrix("+n.viewport.transform.join(",")+")");n.div.removeAttribute("hidden")}}}();PDFJS.AnnotationLayer=l;n.AnnotationLayer=l}),function(n,t){t(n.pdfjsDisplayFontLoader={},n.pdfjsSharedUtil,n.pdfjsSharedGlobal)}(this,function(n,t,i){function r(n){this.docId=n;this.styleElement=null;this.nativeFontFaces=[];this.loadTestFontId=0;this.loadingContext={requests:[],nextRequestId:0}}var o=t.assert,l=t.bytesToString,a=t.string32,e=t.shadow,s=t.warn,f=i.PDFJS,u=i.globalScope,h=i.isWorker,c;r.prototype={insertRule:function(n){var t=this.styleElement,i;t||(t=this.styleElement=document.createElement("style"),t.id="PDFJS_FONT_STYLE_TAG_"+this.docId,document.documentElement.getElementsByTagName("head")[0].appendChild(t));i=t.sheet;i.insertRule(n,i.cssRules.length)},clear:function(){var n=this.styleElement;n&&(n.parentNode.removeChild(n),n=this.styleElement=null);this.nativeFontFaces.forEach(function(n){document.fonts.delete(n)});this.nativeFontFaces.length=0},get loadTestFont(){return e(this,"loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))},addNativeFontFace:function(n){this.nativeFontFaces.push(n);document.fonts.add(n)},bind:function(n,t){var u,y,i,f,e,c;o(!h,"bind() shall be called from main thread");var l=[],a=[],v=[],p=function(n){return n.loaded.catch(function(t){s('Failed to load font "'+n.family+'": '+t)})};for(u=0,y=n.length;u<y;u++)(i=n[u],i.attached||i.loading===!1)||(i.attached=!0,r.isFontLoadingAPISupported?(f=i.createNativeFontFace(),f&&(this.addNativeFontFace(f),v.push(p(f)))):(e=i.createFontFaceRule(),e&&(this.insertRule(e),l.push(e),a.push(i))));c=this.queueLoadingCallback(t);r.isFontLoadingAPISupported?Promise.all(v).then(function(){c.complete()}):l.length>0&&!r.isSyncFontLoadingSupported?this.prepareFontLoadEvent(l,a,c):c.complete()},queueLoadingCallback:function(n){function r(){for(o(!i.end,"completeRequest() cannot be called twice"),i.end=Date.now();t.requests.length>0&&t.requests[0].end;){var n=t.requests.shift();setTimeout(n.callback,0)}}var t=this.loadingContext,u="pdfjs-font-loading-"+t.nextRequestId++,i={id:u,complete:r,callback:n,started:Date.now()};return t.requests.push(i),i},prepareFontLoadEvent:function(n,t,i){function y(n,t){return n.charCodeAt(t)<<24|n.charCodeAt(t+1)<<16|n.charCodeAt(t+2)<<8|n.charCodeAt(t+3)&255}function b(n,t,i,r){var u=n.substr(0,t),f=n.substr(t+i);return u+r+f}function k(n,t){if(w++,w>30){s("Load test font never loaded.");t();return}l.font="30px "+n;l.fillText(".",0,20);var i=l.getImageData(0,0,1,1);if(i.data[3]>0){t();return}setTimeout(k.bind(null,n,t))}var r,f,p=document.createElement("canvas"),l,w,nt,tt,h,c,v;p.width=1;p.height=1;l=p.getContext("2d");w=0;var u="lt"+Date.now()+this.loadTestFontId++,e=this.loadTestFont;e=b(e,976,u.length,u);var d=16,g=1482184792,o=y(e,d);for(r=0,f=u.length-3;r<f;r+=4)o=o-g+y(u,r)|0;for(r<u.length&&(o=o-g+y(u+"XXX",r)|0),e=b(e,d,4,a(o)),nt="url(data:font/opentype;base64,"+btoa(e)+");",tt='@font-face { font-family:"'+u+'";src:'+nt+"}",this.insertRule(tt),h=[],r=0,f=t.length;r<f;r++)h.push(t[r].loadedName);for(h.push(u),c=document.createElement("div"),c.setAttribute("style","visibility: hidden;width: 10px; height: 10px;position: absolute; top: 0px; left: 0px;"),r=0,f=h.length;r<f;++r)v=document.createElement("span"),v.textContent="Hi",v.style.fontFamily=h[r],c.appendChild(v);document.body.appendChild(c);k(u,function(){document.body.removeChild(c);i.complete()})}};r.isFontLoadingAPISupported=!h&&typeof document!="undefined"&&!!document.fonts;Object.defineProperty(r,"isSyncFontLoadingSupported",{get:function(){var n=!1,t=window.navigator.userAgent,i=/Mozilla\/5.0.*?rv:(\d+).*? Gecko/.exec(t);return i&&i[1]>=14&&(n=!0),t==="node"&&(n=!0),e(r,"isSyncFontLoadingSupported",n)},enumerable:!0,configurable:!0});c=function(){function n(n){this.compiledGlyphs={};for(var t in n)this[t]=n[t]}return Object.defineProperty(n,"isEvalSupported",{get:function(){var n=!1;if(f.isEvalSupported)try{new Function("");n=!0}catch(t){}return e(this,"isEvalSupported",n)},enumerable:!0,configurable:!0}),n.prototype={createNativeFontFace:function(){if(!this.data)return null;if(f.disableFontFace)return this.disableFontFace=!0,null;var n=new FontFace(this.loadedName,this.data,{});return f.pdfBug&&"FontInspector"in u&&u.FontInspector.enabled&&u.FontInspector.fontAdded(this),n},createFontFaceRule:function(){if(!this.data)return null;if(f.disableFontFace)return this.disableFontFace=!0,null;var t=l(new Uint8Array(this.data)),i=this.loadedName,n="url(data:"+this.mimetype+";base64,"+window.btoa(t)+");",r='@font-face { font-family:"'+i+'";src:'+n+"}";return f.pdfBug&&"FontInspector"in u&&u.FontInspector.enabled&&u.FontInspector.fontAdded(this,n),r},getPathGenerator:function(t,i){var f,r,u,e,s,o;if(!(i in this.compiledGlyphs))if(f=t.get(this.loadedName+"_path_"+i),n.isEvalSupported){for(o="",u=0,e=f.length;u<e;u++)r=f[u],s=r.args!==undefined?r.args.join(","):"",o+="c."+r.cmd+"("+s+");\n";this.compiledGlyphs[i]=new Function("c","size",o)}else this.compiledGlyphs[i]=function(n,t){for(u=0,e=f.length;u<e;u++)r=f[u],r.cmd==="scale"&&(r.args=[t,-t]),n[r.cmd].apply(n,r.args)};return this.compiledGlyphs[i]}},n}();n.FontFaceObject=c;n.FontLoader=r}),function(n,t){t(n.pdfjsDisplayMetadata={},n.pdfjsSharedUtil)}(this,function(n,t){var i=t.error,r=PDFJS.Metadata=function(){function t(n){return n.replace(/>\\376\\377([^<]+)/g,function(n,t){for(var i,u=t.replace(/\\([0-3])([0-7])([0-7])/g,function(n,t,i,r){return String.fromCharCode(t*64+i*8+r*1)}),f="",r=0;r<u.length;r+=2)i=u.charCodeAt(r)*256+u.charCodeAt(r+1),f+=i>=32&&i<127&&i!==60&&i!==62&&i!==38&&!1?String.fromCharCode(i):"&#x"+(65536+i).toString(16).substring(1)+";";return">"+f})}function n(n){if(typeof n=="string"){n=t(n);var r=new DOMParser;n=r.parseFromString(n,"application/xml")}else n instanceof Document||i("Metadata: Invalid metadata object");this.metaDocument=n;this.metadata={};this.parse()}return n.prototype={parse:function(){var c=this.metaDocument,n=c.documentElement,e,u,t,f,o,r,i,s,h;if(n.nodeName.toLowerCase()!=="rdf:rdf")for(n=n.firstChild;n&&n.nodeName.toLowerCase()!=="rdf:rdf";)n=n.nextSibling;if(e=n?n.nodeName.toLowerCase():null,n&&e==="rdf:rdf"&&n.hasChildNodes())for(u=n.childNodes,r=0,s=u.length;r<s;r++)if(t=u[r],t.nodeName.toLowerCase()==="rdf:description")for(i=0,h=t.childNodes.length;i<h;i++)t.childNodes[i].nodeName.toLowerCase()!=="#text"&&(f=t.childNodes[i],o=f.nodeName.toLowerCase(),this.metadata[o]=f.textContent.trim())},get:function(n){return this.metadata[n]||null},has:function(n){return typeof this.metadata[n]!="undefined"}},n}();n.Metadata=r}),function(n,t){t(n.pdfjsDisplaySVG={},n.pdfjsSharedUtil)}(this,function(n,t){var e=t.FONT_IDENTITY_MATRIX,r=t.IDENTITY_MATRIX,u=t.ImageKind,i=t.OPS,o=t.Util,l=t.isNum,s=t.isArray,h=t.warn,f={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"},a=function(){function s(n,t,i){for(var f,e,r=-1,u=t;u<i;u++)f=(r^n[u])&255,e=o[f],r=r>>>8^e;return r^-1}function e(n,t,i,r){var u=r,e=t.length,f;i[u]=e>>24&255;i[u+1]=e>>16&255;i[u+2]=e>>8&255;i[u+3]=e&255;u+=4;i[u]=n.charCodeAt(0)&255;i[u+1]=n.charCodeAt(1)&255;i[u+2]=n.charCodeAt(2)&255;i[u+3]=n.charCodeAt(3)&255;u+=4;i.set(t,u);u+=t.length;f=s(i,r+4,u);i[u]=f>>24&255;i[u+1]=f>>16&255;i[u+2]=f>>8&255;i[u+3]=f&255}function h(n,t,i){for(var r=1,u=0,f=t;f<i;++f)r=(r+(n[f]&255))%65521,u=(u+r)%65521;return u<<16|r}function c(n,t){var a=n.width,v=n.height,nt,tt,c,ft=n.data,k,d;switch(t){case u.GRAYSCALE_1BPP:tt=0;nt=1;c=a+7>>3;break;case u.RGB_24BPP:tt=2;nt=8;c=a*3;break;case u.RGBA_32BPP:tt=6;nt=8;c=a*4;break;default:throw new Error("invalid format");}for(var s=new Uint8Array((1+c)*v),p=0,it=0,rt,w=0;w<v;++w)s[p++]=0,s.set(ft.subarray(it,it+c),p),it+=c,p+=c;if(t===u.GRAYSCALE_1BPP)for(p=0,w=0;w<v;w++)for(p++,rt=0;rt<c;rt++)s[p++]^=255;var ut=new Uint8Array([a>>24&255,a>>16&255,a>>8&255,a&255,v>>24&255,v>>16&255,v>>8&255,v&255,nt,tt,0,0,0]),l=s.length,b=65535,et=Math.ceil(l/b),f=new Uint8Array(2+l+et*5+4),o=0;for(f[o++]=120,f[o++]=156,k=0;l>b;)f[o++]=0,f[o++]=255,f[o++]=255,f[o++]=0,f[o++]=0,f.set(s.subarray(k,k+b),o),o+=b,k+=b,l-=b;f[o++]=1;f[o++]=l&255;f[o++]=l>>8&255;f[o++]=~l&255;f[o++]=(~l&65535)>>8&255;f.set(s.subarray(k),o);o+=s.length-k;d=h(s,0,s.length);f[o++]=d>>24&255;f[o++]=d>>16&255;f[o++]=d>>8&255;f[o++]=d&255;var ot=i.length+r*3+ut.length+f.length,g=new Uint8Array(ot),y=0;return g.set(i,y),y+=i.length,e("IHDR",ut,g,y),y+=r+ut.length,e("IDATA",f,g,y),y+=r+f.length,e("IEND",new Uint8Array(0),g,y),PDFJS.createObjectURL(g,"image/png")}for(var n,f,i=new Uint8Array([137,80,78,71,13,10,26,10]),r=12,o=new Int32Array(256),t=0;t<256;t++){for(n=t,f=0;f<8;f++)n=n&1?3988292384^n>>1&2147483647:n>>1&2147483647;o[t]=n}return function(n){var t=n.kind===undefined?u.GRAYSCALE_1BPP:n.kind;return c(n,t)}}(),v=function(){function n(){this.fontSizeScale=1;this.fontWeight=f.fontWeight;this.fontSize=0;this.textMatrix=r;this.fontMatrix=e;this.leading=0;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRise=0;this.fillColor=f.fillColor;this.strokeColor="#000000";this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.lineJoin="";this.lineCap="";this.miterLimit=0;this.dashArray=[];this.dashPhase=0;this.dependencies=[];this.clipId="";this.pendingClip=!1;this.maskId=""}return n.prototype={clone:function(){return Object.create(this)},setCurrentPoint:function(n,t){this.x=n;this.y=t}},n}(),c=function(){function w(n,t){var i=document.createElementNS("http://www.w3.org/2000/svg","svg:svg");return i.setAttributeNS(null,"version","1.1"),i.setAttributeNS(null,"width",n+"px"),i.setAttributeNS(null,"height",t+"px"),i.setAttributeNS(null,"viewBox","0 0 "+n+" "+t),i}function b(n){for(var t=[],r=[],u=n.length,i=0;i<u;i++){if(n[i].fn==="save"){t.push({fnId:92,fn:"group",items:[]});r.push(t);t=t[t.length-1].items;continue}n[i].fn==="restore"?t=r.pop():t.push(n[i])}return t}function n(n){if(n===(n|0))return n.toString();var t=n.toFixed(10),i=t.length-1;if(t[i]!=="0")return t;do i--;while(t[i]==="0");return t.substr(0,t[i]==="."?i:i+1)}function u(t){if(t[4]===0&&t[5]===0){if(t[1]===0&&t[2]===0)return t[0]===1&&t[3]===1?"":"scale("+n(t[0])+" "+n(t[3])+")";if(t[0]===t[3]&&t[1]===-t[2]){var i=Math.acos(t[0])*180/Math.PI;return"rotate("+n(i)+")"}}else if(t[0]===1&&t[1]===0&&t[2]===0&&t[3]===1)return"translate("+n(t[4])+" "+n(t[5])+")";return"matrix("+n(t[0])+" "+n(t[1])+" "+n(t[2])+" "+n(t[3])+" "+n(t[4])+" "+n(t[5])+")"}function c(n,t){this.current=new v;this.transformMatrix=r;this.transformStack=[];this.extraStack=[];this.commonObjs=n;this.objs=t;this.pendingEOFill=!1;this.embedFonts=!1;this.embeddedFonts={};this.cssStyle=null}var t="http://www.w3.org/2000/svg",k="http://www.w3.org/XML/1998/namespace",y="http://www.w3.org/1999/xlink",d=["butt","round","square"],g=["miter","round","bevel"],p=0,nt=0;return c.prototype={save:function(){this.transformStack.push(this.transformMatrix);var n=this.current;this.extraStack.push(n);this.current=n.clone()},restore:function(){this.transformMatrix=this.transformStack.pop();this.current=this.extraStack.pop();this.tgrp=document.createElementNS(t,"svg:g");this.tgrp.setAttributeNS(null,"transform",u(this.transformMatrix));this.pgrp.appendChild(this.tgrp)},group:function(n){this.save();this.executeOpTree(n);this.restore()},loadDependencies:function(n){for(var f,r,s,u,h,c,e=n.fnArray,l=e.length,a=n.argsArray,o=this,t=0;t<l;t++)if(i.dependency===e[t])for(f=a[t],r=0,s=f.length;r<s;r++)u=f[r],h=u.substring(0,2)==="g_",c=h?new Promise(function(n){o.commonObjs.get(u,n)}):new Promise(function(n){o.objs.get(u,n)}),this.current.dependencies.push(c);return Promise.all(this.current.dependencies)},transform:function(n,i,r,f,e,o){var s=[n,i,r,f,e,o];this.transformMatrix=PDFJS.Util.transform(this.transformMatrix,s);this.tgrp=document.createElementNS(t,"svg:g");this.tgrp.setAttributeNS(null,"transform",u(this.transformMatrix))},getSVG:function(n,i){return this.svg=w(i.width,i.height),this.viewport=i,this.loadDependencies(n).then(function(){this.transformMatrix=r;this.pgrp=document.createElementNS(t,"svg:g");this.pgrp.setAttributeNS(null,"transform",u(i.transform));this.tgrp=document.createElementNS(t,"svg:g");this.tgrp.setAttributeNS(null,"transform",u(this.transformMatrix));this.defs=document.createElementNS(t,"svg:defs");this.pgrp.appendChild(this.defs);this.pgrp.appendChild(this.tgrp);this.svg.appendChild(this.pgrp);var f=this.convertOpList(n);return this.executeOpTree(f),this.svg}.bind(this))},convertOpList:function(n){var s=n.argsArray,f=n.fnArray,h=f.length,e=[],o=[],r,t,u;for(r in i)e[i[r]]=r;for(t=0;t<h;t++)u=f[t],o.push({fnId:u,fn:e[u],args:s[t]});return b(o)},executeOpTree:function(n){for(var u=n.length,r=0;r<u;r++){var f=n[r].fn,e=n[r].fnId,t=n[r].args;switch(e|0){case i.beginText:this.beginText();break;case i.setLeading:this.setLeading(t);break;case i.setLeadingMoveText:this.setLeadingMoveText(t[0],t[1]);break;case i.setFont:this.setFont(t);break;case i.showText:this.showText(t[0]);break;case i.showSpacedText:this.showText(t[0]);break;case i.endText:this.endText();break;case i.moveText:this.moveText(t[0],t[1]);break;case i.setCharSpacing:this.setCharSpacing(t[0]);break;case i.setWordSpacing:this.setWordSpacing(t[0]);break;case i.setHScale:this.setHScale(t[0]);break;case i.setTextMatrix:this.setTextMatrix(t[0],t[1],t[2],t[3],t[4],t[5]);break;case i.setLineWidth:this.setLineWidth(t[0]);break;case i.setLineJoin:this.setLineJoin(t[0]);break;case i.setLineCap:this.setLineCap(t[0]);break;case i.setMiterLimit:this.setMiterLimit(t[0]);break;case i.setFillRGBColor:this.setFillRGBColor(t[0],t[1],t[2]);break;case i.setStrokeRGBColor:this.setStrokeRGBColor(t[0],t[1],t[2]);break;case i.setDash:this.setDash(t[0],t[1]);break;case i.setGState:this.setGState(t[0]);break;case i.fill:this.fill();break;case i.eoFill:this.eoFill();break;case i.stroke:this.stroke();break;case i.fillStroke:this.fillStroke();break;case i.eoFillStroke:this.eoFillStroke();break;case i.clip:this.clip("nonzero");break;case i.eoClip:this.clip("evenodd");break;case i.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case i.paintJpegXObject:this.paintJpegXObject(t[0],t[1],t[2]);break;case i.paintImageXObject:this.paintImageXObject(t[0]);break;case i.paintInlineImageXObject:this.paintInlineImageXObject(t[0]);break;case i.paintImageMaskXObject:this.paintImageMaskXObject(t[0]);break;case i.paintFormXObjectBegin:this.paintFormXObjectBegin(t[0],t[1]);break;case i.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case i.closePath:this.closePath();break;case i.closeStroke:this.closeStroke();break;case i.closeFillStroke:this.closeFillStroke();break;case i.nextLine:this.nextLine();break;case i.transform:this.transform(t[0],t[1],t[2],t[3],t[4],t[5]);break;case i.constructPath:this.constructPath(t[0],t[1]);break;case i.endPath:this.endPath();break;case 92:this.group(n[r].items);break;default:h("Unimplemented method "+f)}}},setWordSpacing:function(n){this.current.wordSpacing=n},setCharSpacing:function(n){this.current.charSpacing=n},nextLine:function(){this.moveText(0,this.current.leading)},setTextMatrix:function(i,r,u,f,e,o){var s=this.current;this.current.textMatrix=this.current.lineMatrix=[i,r,u,f,e,o];this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0;s.xcoords=[];s.tspan=document.createElementNS(t,"svg:tspan");s.tspan.setAttributeNS(null,"font-family",s.fontFamily);s.tspan.setAttributeNS(null,"font-size",n(s.fontSize)+"px");s.tspan.setAttributeNS(null,"y",n(-s.y));s.txtElement=document.createElementNS(t,"svg:text");s.txtElement.appendChild(s.tspan)},beginText:function(){this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0;this.current.textMatrix=r;this.current.lineMatrix=r;this.current.tspan=document.createElementNS(t,"svg:tspan");this.current.txtElement=document.createElementNS(t,"svg:text");this.current.txtgrp=document.createElementNS(t,"svg:g");this.current.xcoords=[]},moveText:function(i,r){var u=this.current;this.current.x=this.current.lineX+=i;this.current.y=this.current.lineY+=r;u.xcoords=[];u.tspan=document.createElementNS(t,"svg:tspan");u.tspan.setAttributeNS(null,"font-family",u.fontFamily);u.tspan.setAttributeNS(null,"font-size",n(u.fontSize)+"px");u.tspan.setAttributeNS(null,"y",n(-u.y))},showText:function(t){var i=this.current,a=i.font,s=i.fontSize,e;if(s!==0){for(var v=i.charSpacing,y=i.wordSpacing,h=i.fontDirection,c=i.textHScale*h,p=t.length,w=a.vertical,b=s*i.fontMatrix[0],r=0,o=0;o<p;++o){if(e=t[o],e===null){r+=h*y;continue}else if(l(e)){r+=-e*s*.001;continue}i.xcoords.push(i.x+r*c);var d=e.width,g=e.fontChar,nt=d*b+v*h;r+=nt;i.tspan.textContent+=g}w?i.y-=r*c:i.x+=r*c;i.tspan.setAttributeNS(null,"x",i.xcoords.map(n).join(" "));i.tspan.setAttributeNS(null,"y",n(-i.y));i.tspan.setAttributeNS(null,"font-family",i.fontFamily);i.tspan.setAttributeNS(null,"font-size",n(i.fontSize)+"px");i.fontStyle!==f.fontStyle&&i.tspan.setAttributeNS(null,"font-style",i.fontStyle);i.fontWeight!==f.fontWeight&&i.tspan.setAttributeNS(null,"font-weight",i.fontWeight);i.fillColor!==f.fillColor&&i.tspan.setAttributeNS(null,"fill",i.fillColor);i.txtElement.setAttributeNS(null,"transform",u(i.textMatrix)+" scale(1, -1)");i.txtElement.setAttributeNS(k,"xml:space","preserve");i.txtElement.appendChild(i.tspan);i.txtgrp.appendChild(i.txtElement);this.tgrp.appendChild(i.txtElement)}},setLeadingMoveText:function(n,t){this.setLeading(-t);this.moveText(n,t)},addFontStyle:function(n){this.cssStyle||(this.cssStyle=document.createElementNS(t,"svg:style"),this.cssStyle.setAttributeNS(null,"type","text/css"),this.defs.appendChild(this.cssStyle));var i=PDFJS.createObjectURL(n.data,n.mimetype);this.cssStyle.textContent+='@font-face { font-family: "'+n.loadedName+'"; src: url('+i+"); }\n"},setFont:function(i){var u=this.current,r=this.commonObjs.get(i[0]),f=i[1],o,s;this.current.font=r;this.embedFonts&&r.data&&!this.embeddedFonts[r.loadedName]&&(this.addFontStyle(r),this.embeddedFonts[r.loadedName]=r);u.fontMatrix=r.fontMatrix?r.fontMatrix:e;o=r.black?r.bold?"bolder":"bold":r.bold?"bold":"normal";s=r.italic?"italic":"normal";f<0?(f=-f,u.fontDirection=-1):u.fontDirection=1;u.fontSize=f;u.fontFamily=r.loadedName;u.fontWeight=o;u.fontStyle=s;u.tspan=document.createElementNS(t,"svg:tspan");u.tspan.setAttributeNS(null,"y",n(-u.y));u.xcoords=[]},endText:function(){this.current.pendingClip?(this.cgrp.appendChild(this.tgrp),this.pgrp.appendChild(this.cgrp)):this.pgrp.appendChild(this.tgrp);this.tgrp=document.createElementNS(t,"svg:g");this.tgrp.setAttributeNS(null,"transform",u(this.transformMatrix))},setLineWidth:function(n){this.current.lineWidth=n},setLineCap:function(n){this.current.lineCap=d[n]},setLineJoin:function(n){this.current.lineJoin=g[n]},setMiterLimit:function(n){this.current.miterLimit=n},setStrokeRGBColor:function(n,t,i){var r=o.makeCssRgb(n,t,i);this.current.strokeColor=r},setFillRGBColor:function(n,i,r){var u=o.makeCssRgb(n,i,r);this.current.fillColor=u;this.current.tspan=document.createElementNS(t,"svg:tspan");this.current.xcoords=[]},setDash:function(n,t){this.current.dashArray=n;this.current.dashPhase=t},constructPath:function(r,u){var e=this.current,o=e.x,s=e.y,h,l,c,f;for(e.path=document.createElementNS(t,"svg:path"),h=[],l=r.length,c=0,f=0;c<l;c++)switch(r[c]|0){case i.rectangle:o=u[f++];s=u[f++];var y=u[f++],p=u[f++],a=o+y,v=s+p;h.push("M",n(o),n(s),"L",n(a),n(s),"L",n(a),n(v),"L",n(o),n(v),"Z");break;case i.moveTo:o=u[f++];s=u[f++];h.push("M",n(o),n(s));break;case i.lineTo:o=u[f++];s=u[f++];h.push("L",n(o),n(s));break;case i.curveTo:o=u[f+4];s=u[f+5];h.push("C",n(u[f]),n(u[f+1]),n(u[f+2]),n(u[f+3]),n(o),n(s));f+=6;break;case i.curveTo2:o=u[f+2];s=u[f+3];h.push("C",n(o),n(s),n(u[f]),n(u[f+1]),n(u[f+2]),n(u[f+3]));f+=4;break;case i.curveTo3:o=u[f+2];s=u[f+3];h.push("C",n(u[f]),n(u[f+1]),n(o),n(s),n(o),n(s));f+=4;break;case i.closePath:h.push("Z")}e.path.setAttributeNS(null,"d",h.join(" "));e.path.setAttributeNS(null,"stroke-miterlimit",n(e.miterLimit));e.path.setAttributeNS(null,"stroke-linecap",e.lineCap);e.path.setAttributeNS(null,"stroke-linejoin",e.lineJoin);e.path.setAttributeNS(null,"stroke-width",n(e.lineWidth)+"px");e.path.setAttributeNS(null,"stroke-dasharray",e.dashArray.map(n).join(" "));e.path.setAttributeNS(null,"stroke-dashoffset",n(e.dashPhase)+"px");e.path.setAttributeNS(null,"fill","none");this.tgrp.appendChild(e.path);e.pendingClip?(this.cgrp.appendChild(this.tgrp),this.pgrp.appendChild(this.cgrp)):this.pgrp.appendChild(this.tgrp);e.element=e.path;e.setCurrentPoint(o,s)},endPath:function(){var n=this.current;n.pendingClip?(this.cgrp.appendChild(this.tgrp),this.pgrp.appendChild(this.cgrp)):this.pgrp.appendChild(this.tgrp);this.tgrp=document.createElementNS(t,"svg:g");this.tgrp.setAttributeNS(null,"transform",u(this.transformMatrix))},clip:function(n){var i=this.current,r;i.clipId="clippath"+p;p++;this.clippath=document.createElementNS(t,"svg:clipPath");this.clippath.setAttributeNS(null,"id",i.clipId);r=i.element.cloneNode();n==="evenodd"?r.setAttributeNS(null,"clip-rule","evenodd"):r.setAttributeNS(null,"clip-rule","nonzero");this.clippath.setAttributeNS(null,"transform",u(this.transformMatrix));this.clippath.appendChild(r);this.defs.appendChild(this.clippath);i.pendingClip=!0;this.cgrp=document.createElementNS(t,"svg:g");this.cgrp.setAttributeNS(null,"clip-path","url(#"+i.clipId+")");this.pgrp.appendChild(this.cgrp)},closePath:function(){var n=this.current,t=n.path.getAttributeNS(null,"d");t+="Z";n.path.setAttributeNS(null,"d",t)},setLeading:function(n){this.current.leading=-n},setTextRise:function(n){this.current.textRise=n},setHScale:function(n){this.current.textHScale=n/100},setGState:function(n){for(var i=0,r=n.length;i<r;i++){var u=n[i],f=u[0],t=u[1];switch(f){case"LW":this.setLineWidth(t);break;case"LC":this.setLineCap(t);break;case"LJ":this.setLineJoin(t);break;case"ML":this.setMiterLimit(t);break;case"D":this.setDash(t[0],t[1]);break;case"Font":this.setFont(t)}}},fill:function(){var n=this.current;n.element.setAttributeNS(null,"fill",n.fillColor)},stroke:function(){var n=this.current;n.element.setAttributeNS(null,"stroke",n.strokeColor);n.element.setAttributeNS(null,"fill","none")},eoFill:function(){var n=this.current;n.element.setAttributeNS(null,"fill",n.fillColor);n.element.setAttributeNS(null,"fill-rule","evenodd")},fillStroke:function(){this.stroke();this.fill()},eoFillStroke:function(){this.current.element.setAttributeNS(null,"fill-rule","evenodd");this.fillStroke()},closeStroke:function(){this.closePath();this.stroke()},closeFillStroke:function(){this.closePath();this.fillStroke()},paintSolidColorImageMask:function(){var i=this.current,n=document.createElementNS(t,"svg:rect");n.setAttributeNS(null,"x","0");n.setAttributeNS(null,"y","0");n.setAttributeNS(null,"width","1px");n.setAttributeNS(null,"height","1px");n.setAttributeNS(null,"fill",i.fillColor);this.tgrp.appendChild(n)},paintJpegXObject:function(i,r,u){var o=this.current,e=this.objs.get(i),f=document.createElementNS(t,"svg:image");f.setAttributeNS(y,"xlink:href",e.src);f.setAttributeNS(null,"width",e.width+"px");f.setAttributeNS(null,"height",e.height+"px");f.setAttributeNS(null,"x","0");f.setAttributeNS(null,"y",n(-u));f.setAttributeNS(null,"transform","scale("+n(1/r)+" "+n(-1/u)+")");this.tgrp.appendChild(f);o.pendingClip?(this.cgrp.appendChild(this.tgrp),this.pgrp.appendChild(this.cgrp)):this.pgrp.appendChild(this.tgrp)},paintImageXObject:function(n){var t=this.objs.get(n);if(!t){h("Dependent image isn't ready yet");return}this.paintInlineImageXObject(t)},paintInlineImageXObject:function(i,r){var s=this.current,o=i.width,e=i.height,h=a(i),f=document.createElementNS(t,"svg:rect"),u;f.setAttributeNS(null,"x","0");f.setAttributeNS(null,"y","0");f.setAttributeNS(null,"width",n(o));f.setAttributeNS(null,"height",n(e));s.element=f;this.clip("nonzero");u=document.createElementNS(t,"svg:image");u.setAttributeNS(y,"xlink:href",h);u.setAttributeNS(null,"x","0");u.setAttributeNS(null,"y",n(-e));u.setAttributeNS(null,"width",n(o)+"px");u.setAttributeNS(null,"height",n(e)+"px");u.setAttributeNS(null,"transform","scale("+n(1/o)+" "+n(-1/e)+")");r?r.appendChild(u):this.tgrp.appendChild(u);s.pendingClip?(this.cgrp.appendChild(this.tgrp),this.pgrp.appendChild(this.cgrp)):this.pgrp.appendChild(this.tgrp)},paintImageMaskXObject:function(i){var u=this.current,e=i.width,o=i.height,s=u.fillColor,f,r;u.maskId="mask"+nt++;f=document.createElementNS(t,"svg:mask");f.setAttributeNS(null,"id",u.maskId);r=document.createElementNS(t,"svg:rect");r.setAttributeNS(null,"x","0");r.setAttributeNS(null,"y","0");r.setAttributeNS(null,"width",n(e));r.setAttributeNS(null,"height",n(o));r.setAttributeNS(null,"fill",s);r.setAttributeNS(null,"mask","url(#"+u.maskId+")");this.defs.appendChild(f);this.tgrp.appendChild(r);this.paintInlineImageXObject(i,f)},paintFormXObjectBegin:function(i,r){if(this.save(),s(i)&&i.length===6&&this.transform(i[0],i[1],i[2],i[3],i[4],i[5]),s(r)&&r.length===4){var f=r[2]-r[0],e=r[3]-r[1],u=document.createElementNS(t,"svg:rect");u.setAttributeNS(null,"x",r[0]);u.setAttributeNS(null,"y",r[1]);u.setAttributeNS(null,"width",n(f));u.setAttributeNS(null,"height",n(e));this.current.element=u;this.clip("nonzero");this.endPath()}},paintFormXObjectEnd:function(){this.restore()}},c}();PDFJS.SVGGraphics=c;n.SVGGraphics=c}),function(n,t){t(n.pdfjsDisplayTextLayer={},n.pdfjsSharedUtil,n.pdfjsDisplayDOMUtils,n.pdfjsSharedGlobal)}(this,function(n,t,i,r){var e=t.Util,o=t.createPromiseCapability,s=i.CustomStyle,u=r.PDFJS,f=function(){function f(n){return!r.test(n)}function h(n,t,i,r){var c=r[i.fontName],s=document.createElement("div"),o,l,a,h,v,y;if(n.push(s),f(i.str)){s.dataset.isWhitespace=!0;return}o=e.transform(t.transform,i.transform);l=Math.atan2(o[1],o[0]);c.vertical&&(l+=Math.PI/2);a=Math.sqrt(o[2]*o[2]+o[3]*o[3]);h=a;c.ascent?h=c.ascent*h:c.descent&&(h=(1+c.descent)*h);l===0?(v=o[4],y=o[5]-h):(v=o[4]+h*Math.sin(l),y=o[5]-h*Math.cos(l));s.style.left=v+"px";s.style.top=y+"px";s.style.fontSize=a+"px";s.style.fontFamily=c.fontFamily;s.textContent=i.str;u.pdfBug&&(s.dataset.fontName=i.fontName);l!==0&&(s.dataset.angle=l*(180/Math.PI));i.str.length>1&&(s.dataset.canvasWidth=c.vertical?i.height*t.scale:i.width*t.scale)}function n(n){var o,h,p,w,u,t,f,e,c,r,b,l;if(!n._canceled){var k=n._container,a=n._textDivs,v=n._capability,y=a.length;if(y>i){v.resolve();return}for(o=document.createElement("canvas"),o.mozOpaque=!0,h=o.getContext("2d",{alpha:!1}),u=0;u<y;u++)(t=a[u],t.dataset.isWhitespace===undefined)&&(f=t.style.fontSize,e=t.style.fontFamily,(f!==p||e!==w)&&(h.font=f+" "+e,p=f,w=e),c=h.measureText(t.textContent).width,c>0&&(k.appendChild(t),t.dataset.canvasWidth!==undefined?(b=t.dataset.canvasWidth/c,r="scaleX("+b+")"):r="",l=t.dataset.angle,l&&(r="rotate("+l+"deg) "+r),r&&s.setProp("transform",t,r)));v.resolve()}}function t(n,t,i,r){this._textContent=n;this._container=t;this._viewport=i;r=r||[];this._textDivs=r;this._canceled=!1;this._capability=o();this._renderTimer=null}function c(n){var i=new t(n.textContent,n.container,n.viewport,n.textDivs);return i._render(n.timeout),i}var i=1e5,r=/\S/;return t.prototype={get promise(){return this._capability.promise},cancel:function(){this._canceled=!0;this._renderTimer!==null&&(clearTimeout(this._renderTimer),this._renderTimer=null);this._capability.reject("canceled")},_render:function(t){for(var r,u=this._textContent.items,f=this._textContent.styles,e=this._textDivs,o=this._viewport,i=0,s=u.length;i<s;i++)h(e,o,u[i],f);t?(r=this,this._renderTimer=setTimeout(function(){n(r);r._renderTimer=null},t)):n(this)}},c}();u.renderTextLayer=f;n.renderTextLayer=f}),function(n,t){t(n.pdfjsDisplayWebGL={},n.pdfjsSharedUtil)}(this,function(n,t){var i=t.shadow,r=function(){function e(n,t,i){var r=n.createShader(i),u,f;if(n.shaderSource(r,t),n.compileShader(r),u=n.getShaderParameter(r,n.COMPILE_STATUS),!u){f=n.getShaderInfoLog(r);throw new Error("Error during shader compilation: "+f);}return r}function o(n,t){return e(n,t,n.VERTEX_SHADER)}function s(n,t){return e(n,t,n.FRAGMENT_SHADER)}function h(n,t){for(var u,f,i=n.createProgram(),r=0,e=t.length;r<e;++r)n.attachShader(i,t[r]);if(n.linkProgram(i),u=n.getProgramParameter(i,n.LINK_STATUS),!u){f=n.getProgramInfoLog(i);throw new Error("Error during program linking: "+f);}return i}function c(n,t,i){n.activeTexture(i);var r=n.createTexture();return n.bindTexture(n.TEXTURE_2D,r),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_S,n.CLAMP_TO_EDGE),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_T,n.CLAMP_TO_EDGE),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MIN_FILTER,n.NEAREST),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MAG_FILTER,n.NEAREST),n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,t),r}function f(){r||(u=document.createElement("canvas"),r=u.getContext("webgl",{premultipliedalpha:!1}))}function v(){var c,t,e;f();c=u;u=null;t=r;r=null;var y=o(t,l),p=s(t,a),i=h(t,[y,p]);t.useProgram(i);e={};e.gl=t;e.canvas=c;e.resolutionLocation=t.getUniformLocation(i,"u_resolution");e.positionLocation=t.getAttribLocation(i,"a_position");e.backdropLocation=t.getUniformLocation(i,"u_backdrop");e.subtypeLocation=t.getUniformLocation(i,"u_subtype");var v=t.getAttribLocation(i,"a_texCoord"),w=t.getUniformLocation(i,"u_image"),b=t.getUniformLocation(i,"u_mask"),k=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,k);t.bufferData(t.ARRAY_BUFFER,new Float32Array([0,0,1,0,0,1,0,1,1,0,1,1]),t.STATIC_DRAW);t.enableVertexAttribArray(v);t.vertexAttribPointer(v,2,t.FLOAT,!1,0,0);t.uniform1i(w,0);t.uniform1i(b,1);n=e}function y(t,i,r){var e=t.width,o=t.height;n||v();var f=n,s=f.canvas,u=f.gl;s.width=e;s.height=o;u.viewport(0,0,u.drawingBufferWidth,u.drawingBufferHeight);u.uniform2f(f.resolutionLocation,e,o);r.backdrop?u.uniform4f(f.resolutionLocation,r.backdrop[0],r.backdrop[1],r.backdrop[2],1):u.uniform4f(f.resolutionLocation,0,0,0,0);u.uniform1i(f.subtypeLocation,r.subtype==="Luminosity"?1:0);var l=c(u,t,u.TEXTURE0),a=c(u,i,u.TEXTURE1),h=u.createBuffer();return u.bindBuffer(u.ARRAY_BUFFER,h),u.bufferData(u.ARRAY_BUFFER,new Float32Array([0,0,e,0,0,o,0,o,e,0,e,o]),u.STATIC_DRAW),u.enableVertexAttribArray(f.positionLocation),u.vertexAttribPointer(f.positionLocation,2,u.FLOAT,!1,0,0),u.clearColor(0,0,0,0),u.enable(u.BLEND),u.blendFunc(u.ONE,u.ONE_MINUS_SRC_ALPHA),u.clear(u.COLOR_BUFFER_BIT),u.drawArrays(u.TRIANGLES,0,6),u.flush(),u.deleteTexture(l),u.deleteTexture(a),u.deleteBuffer(h),s}function b(){var c,n,i;f();c=u;u=null;n=r;r=null;var l=o(n,p),a=s(n,w),e=h(n,[l,a]);n.useProgram(e);i={};i.gl=n;i.canvas=c;i.resolutionLocation=n.getUniformLocation(e,"u_resolution");i.scaleLocation=n.getUniformLocation(e,"u_scale");i.offsetLocation=n.getUniformLocation(e,"u_offset");i.positionLocation=n.getAttribLocation(e,"a_position");i.colorLocation=n.getAttribLocation(e,"a_color");t=i}function k(n,i,r,u,f){var nt,p,tt,it,y,ut,h,et,g,ht,ot,st;t||b();var d=t,ft=d.canvas,e=d.gl;for(ft.width=n,ft.height=i,e.viewport(0,0,e.drawingBufferWidth,e.drawingBufferHeight),e.uniform2f(d.resolutionLocation,n,i),nt=0,p=0,tt=u.length;p<tt;p++)switch(u[p].type){case"lattice":it=u[p].coords.length/u[p].verticesPerRow|0;nt+=(it-1)*(u[p].verticesPerRow-1)*6;break;case"triangles":nt+=u[p].coords.length}var l=new Float32Array(nt*2),s=new Uint8Array(nt*3),k=f.coords,a=f.colors,c=0,o=0;for(p=0,tt=u.length;p<tt;p++){var rt=u[p],w=rt.coords,v=rt.colors;switch(rt.type){case"lattice":for(y=rt.verticesPerRow,it=w.length/y|0,ut=1;ut<it;ut++)for(h=ut*y+1,et=1;et<y;et++,h++)l[c]=k[w[h-y-1]],l[c+1]=k[w[h-y-1]+1],l[c+2]=k[w[h-y]],l[c+3]=k[w[h-y]+1],l[c+4]=k[w[h-1]],l[c+5]=k[w[h-1]+1],s[o]=a[v[h-y-1]],s[o+1]=a[v[h-y-1]+1],s[o+2]=a[v[h-y-1]+2],s[o+3]=a[v[h-y]],s[o+4]=a[v[h-y]+1],s[o+5]=a[v[h-y]+2],s[o+6]=a[v[h-1]],s[o+7]=a[v[h-1]+1],s[o+8]=a[v[h-1]+2],l[c+6]=l[c+2],l[c+7]=l[c+3],l[c+8]=l[c+4],l[c+9]=l[c+5],l[c+10]=k[w[h]],l[c+11]=k[w[h]+1],s[o+9]=s[o+3],s[o+10]=s[o+4],s[o+11]=s[o+5],s[o+12]=s[o+6],s[o+13]=s[o+7],s[o+14]=s[o+8],s[o+15]=a[v[h]],s[o+16]=a[v[h]+1],s[o+17]=a[v[h]+2],c+=12,o+=18;break;case"triangles":for(g=0,ht=w.length;g<ht;g++)l[c]=k[w[g]],l[c+1]=k[w[g]+1],s[o]=a[v[g]],s[o+1]=a[v[g]+1],s[o+2]=a[v[g]+2],c+=2,o+=3}}return r?e.clearColor(r[0]/255,r[1]/255,r[2]/255,1):e.clearColor(0,0,0,0),e.clear(e.COLOR_BUFFER_BIT),ot=e.createBuffer(),e.bindBuffer(e.ARRAY_BUFFER,ot),e.bufferData(e.ARRAY_BUFFER,l,e.STATIC_DRAW),e.enableVertexAttribArray(d.positionLocation),e.vertexAttribPointer(d.positionLocation,2,e.FLOAT,!1,0,0),st=e.createBuffer(),e.bindBuffer(e.ARRAY_BUFFER,st),e.bufferData(e.ARRAY_BUFFER,s,e.STATIC_DRAW),e.enableVertexAttribArray(d.colorLocation),e.vertexAttribPointer(d.colorLocation,3,e.UNSIGNED_BYTE,!1,0,0),e.uniform2f(d.scaleLocation,f.scaleX,f.scaleY),e.uniform2f(d.offsetLocation,f.offsetX,f.offsetY),e.drawArrays(e.TRIANGLES,0,nt),e.flush(),e.deleteBuffer(ot),e.deleteBuffer(st),ft}function d(){n&&n.canvas&&(n.canvas.width=0,n.canvas.height=0);t&&t.canvas&&(t.canvas.width=0,t.canvas.height=0);n=null;t=null}var r,u,l="  attribute vec2 a_position;                                      attribute vec2 a_texCoord;                                                                                                      uniform vec2 u_resolution;                                                                                                      varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec2 clipSpace = (a_position / u_resolution) * 2.0 - 1.0;       gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_texCoord = a_texCoord;                                      }                                                             ",a="  precision mediump float;                                                                                                        uniform vec4 u_backdrop;                                        uniform int u_subtype;                                          uniform sampler2D u_image;                                      uniform sampler2D u_mask;                                                                                                       varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec4 imageColor = texture2D(u_image, v_texCoord);               vec4 maskColor = texture2D(u_mask, v_texCoord);                 if (u_backdrop.a > 0.0) {                                         maskColor.rgb = maskColor.rgb * maskColor.a +                                   u_backdrop.rgb * (1.0 - maskColor.a);         }                                                               float lum;                                                      if (u_subtype == 0) {                                             lum = maskColor.a;                                            } else {                                                          lum = maskColor.r * 0.3 + maskColor.g * 0.59 +                        maskColor.b * 0.11;                                     }                                                               imageColor.a *= lum;                                            imageColor.rgb *= imageColor.a;                                 gl_FragColor = imageColor;                                    }                                                             ",n=null,p="  attribute vec2 a_position;                                      attribute vec3 a_color;                                                                                                         uniform vec2 u_resolution;                                      uniform vec2 u_scale;                                           uniform vec2 u_offset;                                                                                                          varying vec4 v_color;                                                                                                           void main() {                                                     vec2 position = (a_position + u_offset) * u_scale;              vec2 clipSpace = (position / u_resolution) * 2.0 - 1.0;         gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_color = vec4(a_color / 255.0, 1.0);                         }                                                             ",w="  precision mediump float;                                                                                                        varying vec4 v_color;                                                                                                           void main() {                                                     gl_FragColor = v_color;                                       }                                                             ",t=null;return{get isEnabled(){if(PDFJS.disableWebGL)return!1;var n=!1;try{f();n=!!r}catch(t){}return i(this,"isEnabled",n)},composeSMask:y,drawFigures:k,clear:d}}();n.WebGLUtils=r}),function(n,t){t(n.pdfjsDisplayPatternHelper={},n.pdfjsSharedUtil,n.pdfjsDisplayWebGL)}(this,function(n,t,i){function l(n){var t=u[n[0]];return t||f("Unknown IR type: "+n[0]),t.fromIR(n)}var r=t.Util,h=t.info,c=t.isArray,f=t.error,e=i.WebGLUtils,u={},o,s;u.RadialAxial={fromIR:function(n){var r=n[1],u=n[2],t=n[3],i=n[4],f=n[5],e=n[6];return{type:"Pattern",getPattern:function(n){var o,s,c,h;for(r==="axial"?o=n.createLinearGradient(t[0],t[1],i[0],i[1]):r==="radial"&&(o=n.createRadialGradient(t[0],t[1],f,i[0],i[1],e)),s=0,c=u.length;s<c;++s)h=u[s],o.addColorStop(h[0],h[1]);return o}}}};o=function(){function n(n,t,i,r,u,f,e,o){var h=t.coords,a=t.colors,b=n.data,dt=n.width*4,c,l,et;h[i+1]>h[r+1]&&(c=i,i=r,r=c,c=f,f=e,e=c);h[r+1]>h[u+1]&&(c=r,r=u,u=c,c=e,e=o,o=c);h[i+1]>h[r+1]&&(c=i,i=r,r=c,c=f,f=e,e=c);var k=(h[i]+t.offsetX)*t.scaleX,v=(h[i+1]+t.offsetY)*t.scaleY,ot=(h[r]+t.offsetX)*t.scaleX,p=(h[r+1]+t.offsetY)*t.scaleY,lt=(h[u]+t.offsetX)*t.scaleX,y=(h[u+1]+t.offsetY)*t.scaleY;if(!(v>=y)){var d=a[f],g=a[f+1],nt=a[f+2],st=a[e],ht=a[e+1],ct=a[e+2],at=a[o],vt=a[o+1],yt=a[o+2],gt=Math.round(v),ni=Math.round(y),w,tt,it,rt,ut,pt,wt,bt,s;for(l=gt;l<=ni;l++){l<p?(s=l<v?0:v===p?1:(v-l)/(v-p),w=k-(k-ot)*s,tt=d-(d-st)*s,it=g-(g-ht)*s,rt=nt-(nt-ct)*s):(s=l>y?1:p===y?0:(p-l)/(p-y),w=ot-(ot-lt)*s,tt=st-(st-at)*s,it=ht-(ht-vt)*s,rt=ct-(ct-yt)*s);s=l<v?0:l>y?1:(v-l)/(v-y);ut=k-(k-lt)*s;pt=d-(d-at)*s;wt=g-(g-vt)*s;bt=nt-(nt-yt)*s;var kt=Math.round(Math.min(w,ut)),ti=Math.round(Math.max(w,ut)),ft=dt*l+kt*4;for(et=kt;et<=ti;et++)s=(w-et)/(w-ut),s=s<0?0:s>1?1:s,b[ft++]=tt-(tt-pt)*s|0,b[ft++]=it-(it-wt)*s|0,b[ft++]=rt-(rt-bt)*s|0,b[ft++]=255}}}function t(t,i,r){var o=i.coords,s=i.colors,e,l,u,c;switch(i.type){case"lattice":var h=i.verticesPerRow,a=Math.floor(o.length/h)-1,v=h-1;for(e=0;e<a;e++)for(u=e*h,c=0;c<v;c++,u++)n(t,r,o[u],o[u+1],o[u+h],s[u],s[u+1],s[u+h]),n(t,r,o[u+h+1],o[u+1],o[u+h],s[u+h+1],s[u+1],s[u+h]);break;case"triangles":for(e=0,l=o.length;e<l;e+=3)n(t,r,o[e],o[e+1],o[e+2],s[e],s[e+1],s[e+2]);break;default:f("illigal figure")}}function i(n,i,r,u,f,o,s){var d=1.1,g=3e3,w=Math.floor(n[0]),b=Math.floor(n[1]),nt=Math.ceil(n[2])-w,tt=Math.ceil(n[3])-b,a=Math.min(Math.ceil(Math.abs(nt*i[0]*d)),g),v=Math.min(Math.ceil(Math.abs(tt*i[1]*d)),g),it=nt/a,rt=tt/v,ut={coords:r,colors:u,offsetX:-w,offsetY:-b,scaleX:1/it,scaleY:1/rt},y,c,h,ft,k,p,l;if(e.isEnabled)y=e.drawFigures(a,v,o,f,ut),c=s.getCanvas("mesh",a,v,!1),c.context.drawImage(y,0,0),y=c.canvas;else{if(c=s.getCanvas("mesh",a,v,!1),k=c.context,p=k.createImageData(a,v),o)for(l=p.data,h=0,ft=l.length;h<ft;h+=4)l[h]=o[0],l[h+1]=o[1],l[h+2]=o[2],l[h+3]=255;for(h=0;h<f.length;h++)t(p,f[h],ut);k.putImageData(p,0,0);y=c.canvas}return{canvas:y,offsetX:w,offsetY:b,scaleX:it,scaleY:rt}}return i}();u.Mesh={fromIR:function(n){var i=n[2],u=n[3],f=n[4],e=n[5],t=n[6],s=n[8];return{type:"Pattern",getPattern:function(n,h,c){var l,v,a;return c?l=r.singularValueDecompose2dScale(n.mozCurrentTransform):(l=r.singularValueDecompose2dScale(h.baseTransform),t&&(v=r.singularValueDecompose2dScale(t),l=[l[0]*v[0],l[1]*v[1]])),a=o(e,l,i,u,f,c?null:s,h.cachedCanvases),c||(n.setTransform.apply(n,h.baseTransform),t&&n.transform.apply(n,t)),n.translate(a.offsetX,a.offsetY),n.scale(a.scaleX,a.scaleY),n.createPattern(a.canvas,"no-repeat")}}}};u.Dummy={fromIR:function(){return{type:"Pattern",getPattern:function(){return"hotpink"}}}};s=function(){function i(n,t,i,r,u){this.operatorList=n[2];this.matrix=n[3]||[1,0,0,1,0,0];this.bbox=n[4];this.xstep=n[5];this.ystep=n[6];this.paintType=n[7];this.tilingType=n[8];this.color=t;this.canvasGraphicsFactory=r;this.baseTransform=u;this.type="Pattern";this.ctx=i}var n={COLORED:1,UNCOLORED:2},t=3e3;return i.prototype={createPatternCanvas:function(n){var g=this.operatorList,u=this.bbox,l=this.xstep,a=this.ystep,nt=this.paintType,tt=this.tilingType,it=this.color,rt=this.canvasGraphicsFactory,d;h("TilingType: "+tt);var s=u[0],c=u[1],ut=u[2],ft=u[3],f=[s,c],v=[s+l,c+a],e=v[0]-f[0],o=v[1]-f[1],y=r.singularValueDecompose2dScale(this.matrix),p=r.singularValueDecompose2dScale(this.baseTransform),w=[y[0]*p[0],y[1]*p[1]];e=Math.min(Math.ceil(Math.abs(e*w[0])),t);o=Math.min(Math.ceil(Math.abs(o*w[1])),t);var b=n.cachedCanvases.getCanvas("pattern",e,o,!0),k=b.context,i=rt.createCanvasGraphics(k);return i.groupLevel=n.groupLevel,this.setFillAndStrokeStyleToContext(k,nt,it),this.setScale(e,o,l,a),this.transformToScale(i),d=[1,0,0,1,-f[0],-f[1]],i.transform.apply(i,d),this.clipBbox(i,u,s,c,ut,ft),i.executeOperatorList(g),b.canvas},setScale:function(n,t,i,r){this.scale=[n/i,t/r]},transformToScale:function(n){var t=this.scale,i=[t[0],0,0,t[1],0,0];n.transform.apply(n,i)},scaleToContext:function(){var n=this.scale;this.ctx.scale(1/n[0],1/n[1])},clipBbox:function(n,t,i,r,u,f){if(t&&c(t)&&t.length===4){var e=u-i,o=f-r;n.ctx.rect(i,r,e,o);n.clip();n.endPath()}},setFillAndStrokeStyleToContext:function(t,i,u){var e,o;switch(i){case n.COLORED:e=this.ctx;t.fillStyle=e.fillStyle;t.strokeStyle=e.strokeStyle;break;case n.UNCOLORED:o=r.makeCssRgb(u[0],u[1],u[2]);t.fillStyle=o;t.strokeStyle=o;break;default:f("Unsupported paint type: "+i)}},getPattern:function(n,t){var i=this.createPatternCanvas(t);return n=this.ctx,n.setTransform.apply(n,this.baseTransform),n.transform.apply(n,this.matrix),this.scaleToContext(),n.createPattern(i,"repeat")}},i}();n.getShadingPatternFromIR=l;n.TilingPattern=s}),function(n,t){t(n.pdfjsDisplayCanvas={},n.pdfjsSharedUtil,n.pdfjsDisplayPatternHelper,n.pdfjsDisplayWebGL)}(this,function(n,t,i,r){function tt(n,t){var i=document.createElement("canvas");return i.width=n,i.height=t,i}function it(n){n.mozCurrentTransform||(n._originalSave=n.save,n._originalRestore=n.restore,n._originalRotate=n.rotate,n._originalScale=n.scale,n._originalTranslate=n.translate,n._originalTransform=n.transform,n._originalSetTransform=n.setTransform,n._transformMatrix=n._transformMatrix||[1,0,0,1,0,0],n._transformStack=[],Object.defineProperty(n,"mozCurrentTransform",{get:function(){return this._transformMatrix}}),Object.defineProperty(n,"mozCurrentTransformInverse",{get:function(){var n=this._transformMatrix,t=n[0],i=n[1],r=n[2],u=n[3],o=n[4],s=n[5],f=t*u-i*r,e=i*r-t*u;return[u/f,i/e,r/e,t/f,(u*o-r*s)/e,(i*o-t*s)/f]}}),n.save=function(){var n=this._transformMatrix;this._transformStack.push(n);this._transformMatrix=n.slice(0,6);this._originalSave()},n.restore=function(){var n=this._transformStack.pop();n&&(this._transformMatrix=n,this._originalRestore())},n.translate=function(n,t){var i=this._transformMatrix;i[4]=i[0]*n+i[2]*t+i[4];i[5]=i[1]*n+i[3]*t+i[5];this._originalTranslate(n,t)},n.scale=function(n,t){var i=this._transformMatrix;i[0]=i[0]*n;i[1]=i[1]*n;i[2]=i[2]*t;i[3]=i[3]*t;this._originalScale(n,t)},n.transform=function(t,i,r,u,f,e){var o=this._transformMatrix;this._transformMatrix=[o[0]*t+o[2]*i,o[1]*t+o[3]*i,o[0]*r+o[2]*u,o[1]*r+o[3]*u,o[0]*f+o[2]*e+o[4],o[1]*f+o[3]*e+o[5]];n._originalTransform(t,i,r,u,f,e)},n.setTransform=function(t,i,r,u,f,e){this._transformMatrix=[t,i,r,u,f,e];n._originalSetTransform(t,i,r,u,f,e)},n.rotate=function(n){var i=Math.cos(n),r=Math.sin(n),t=this._transformMatrix;this._transformMatrix=[t[0]*i+t[2]*r,t[1]*i+t[3]*r,t[0]*-r+t[2]*i,t[1]*-r+t[3]*i,t[4],t[5]];this._originalRotate(n)})}function lt(n){for(var g=1e3,c=n.width,l=n.height,f,h,s=c+1,e=new Uint8Array(s*(l+1)),nt=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),a=c+7&-8,tt=n.data,i=new Uint8Array(a*l),t=0,w,rt,o,y,ut,b,r,k,ft,u=0,it=tt.length;u<it;u++)for(w=128,rt=tt[u];w>0;)i[t++]=rt&w?0:255,w>>=1;for(o=0,t=0,i[t]!==0&&(e[0]=1,++o),f=1;f<c;f++)i[t]!==i[t+1]&&(e[f]=i[t]?2:1,++o),t++;for(i[t]!==0&&(e[f]=2,++o),u=1;u<l;u++){for(t=u*a,h=u*s,i[t-a]!==i[t]&&(e[h]=i[t]?1:8,++o),y=(i[t]?4:0)+(i[t-a]?8:0),f=1;f<c;f++)y=(y>>2)+(i[t+1]?4:0)+(i[t-a+1]?8:0),nt[y]&&(e[h+f]=nt[y],++o),t++;if(i[t-a]!==i[t]&&(e[h+f]=i[t]?2:4,++o),o>g)return null}for(t=a*(l-1),h=u*s,i[t]!==0&&(e[h]=8,++o),f=1;f<c;f++)i[t]!==i[t+1]&&(e[h+f]=i[t]?4:8,++o),t++;if(i[t]!==0&&(e[h+f]=4,++o),o>g)return null;for(ut=new Int32Array([0,s,-1,0,-s,0,0,0,1]),b=[],u=0;o&&u<=l;u++){for(r=u*s,k=r+c;r<k&&!e[r];)r++;if(r!==k){var d=[r%s,u],v=e[r],et=r,p;do{ft=ut[v];do r+=ft;while(!e[r]);p=e[r];p!==5&&p!==10?(v=p,e[r]=0):(v=p&51*v>>4,e[r]&=v>>2|v<<2);d.push(r%s);d.push(r/s|0);--o}while(et!==r);b.push(d);--u}}return function(n){var r,u,t,i,f;for(n.save(),n.scale(1/c,-1/l),n.translate(0,-l),n.beginPath(),r=0,u=b.length;r<u;r++)for(t=b[r],n.moveTo(t[0],t[1]),i=2,f=t.length;i<f;i+=2)n.lineTo(t[i],t[i+1]);n.fill();n.beginPath();n.restore()}}var l=t.FONT_IDENTITY_MATRIX,p=t.IDENTITY_MATRIX,a=t.ImageKind,e=t.OPS,u=t.TextRenderingMode,ut=t.Uint32ArrayView,o=t.Util,ft=t.assert,et=t.info,w=t.isNum,v=t.isArray,c=t.error,ot=t.shadow,s=t.warn,st=i.TilingPattern,b=i.getShadingPatternFromIR,y=r.WebGLUtils,k=16,d=100,h=4096,g=.65,ht=!0,nt=1e3,f=16,ct=function(){function n(){this.cache=Object.create(null)}return n.prototype={getCanvas:function(n,t,i,r){var u,f,e;return this.cache[n]!==undefined?(u=this.cache[n],u.canvas.width=t,u.canvas.height=i,u.context.setTransform(1,0,0,1,0,0)):(f=tt(t,i),e=f.getContext("2d"),r&&it(e),this.cache[n]=u={canvas:f,context:e}),u},clear:function(){var n,t;for(n in this.cache)t=this.cache[n],t.canvas.width=0,t.canvas.height=0,delete this.cache[n]}},n}(),rt=function(){function n(n){this.alphaIsShape=!1;this.fontSize=0;this.fontSizeScale=1;this.textMatrix=p;this.textMatrixScale=1;this.fontMatrix=l;this.leading=0;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRenderingMode=u.FILL;this.textRise=0;this.fillColor="#000000";this.strokeColor="#000000";this.patternFill=!1;this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.activeSMask=null;this.old=n}return n.prototype={clone:function(){return Object.create(this)},setCurrentPoint:function(n,t){this.x=n;this.y=t}},n}(),at=function(){function n(n,t,i,r){this.ctx=n;this.current=new rt;this.stateStack=[];this.pendingClip=null;this.pendingEOFill=!1;this.res=null;this.xobjs=null;this.commonObjs=t;this.objs=i;this.imageLayer=r;this.groupStack=[];this.processingType3=null;this.baseTransform=null;this.baseTransformStack=[];this.groupLevel=0;this.smaskStack=[];this.smaskCounter=0;this.tempSMask=null;this.cachedCanvases=new ct;n&&it(n);this.cachedGetSinglePixelWidth=null}function at(n,t){if(typeof ImageData!="undefined"&&t instanceof ImageData){n.putImageData(t,0,0);return}var rt=t.height,w=t.width,k=rt%f,d=(rt-k)/f,it=k===0?d:d+1,g=n.createImageData(w,f),u=0,i,v=t.data,y=g.data,r,s,b,p;if(t.kind===a.GRAYSCALE_1BPP){var ot=v.byteLength,e=PDFJS.hasCanvasTypedArrays?new Uint32Array(y.buffer):new ut(y),st=e.length,ht=w+7>>3,h=4294967295,l=PDFJS.isLittleEndian||!PDFJS.hasCanvasTypedArrays?4278190080:255;for(r=0;r<it;r++){for(b=r<d?f:k,i=0,s=0;s<b;s++){for(var ft=ot-u,nt=0,et=ft>ht?w:ft*8-7,ct=et&-8,tt=0,o=0;nt<ct;nt+=8)o=v[u++],e[i++]=o&128?h:l,e[i++]=o&64?h:l,e[i++]=o&32?h:l,e[i++]=o&16?h:l,e[i++]=o&8?h:l,e[i++]=o&4?h:l,e[i++]=o&2?h:l,e[i++]=o&1?h:l;for(;nt<et;nt++)tt===0&&(o=v[u++],tt=128),e[i++]=o&tt?h:l,tt>>=1}while(i<st)e[i++]=0;n.putImageData(g,0,r*f)}}else if(t.kind===a.RGBA_32BPP){for(s=0,p=w*f*4,r=0;r<d;r++)y.set(v.subarray(u,u+p)),u+=p,n.putImageData(g,0,s),s+=f;r<it&&(p=w*k*4,y.set(v.subarray(u,u+p)),n.putImageData(g,0,s))}else if(t.kind===a.RGB_24BPP)for(b=f,p=w*b,r=0;r<it;r++){for(r>=d&&(b=k,p=w*b),i=0,s=p;s--;)y[i++]=v[u++],y[i++]=v[u++],y[i++]=v[u++],y[i++]=255;n.putImageData(g,0,r*f)}else c("bad image kind: "+t.kind)}function t(n,t){for(var v,o,s,i,h,y,c=t.height,l=t.width,u=c%f,e=(c-u)/f,p=u===0?e:e+1,a=n.createImageData(l,f),w=0,b=t.data,k=a.data,r=0;r<p;r++){for(v=r<e?f:u,o=3,s=0;s<v;s++)for(i=0,h=0;h<l;h++)i||(y=b[w++],i=128),k[o]=y&i?0:255,o+=4,i>>=1;n.putImageData(a,0,r*f)}}function i(n,t){for(var i,u=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font"],r=0,f=u.length;r<f;r++)i=u[r],n[i]!==undefined&&(t[i]=n[i]);n.setLineDash!==undefined?(t.setLineDash(n.getLineDash()),t.lineDashOffset=n.lineDashOffset):n.mozDashOffset!==undefined&&(t.mozDash=n.mozDash,t.mozDashOffset=n.mozDashOffset)}function pt(n,t,i,r){for(var f,e,o=n.length,u=3;u<o;u+=4)f=n[u],f===0?(n[u-3]=t,n[u-2]=i,n[u-1]=r):f<255&&(e=255-f,n[u-3]=n[u-3]*f+t*e>>8,n[u-2]=n[u-2]*f+i*e>>8,n[u-1]=n[u-1]*f+r*e>>8)}function wt(n,t,i){for(var u,f=n.length,r=3;r<f;r+=4)u=i?i[n[r]]:n[r],t[r]=t[r]*u*(1/255)|0}function bt(n,t,i){for(var u,f=n.length,r=3;r<f;r+=4)u=n[r-3]*77+n[r-2]*152+n[r-1]*28,t[r]=i?t[r]*i[u>>8]>>8:t[r]*u>>16}function kt(n,t,i,r,u,f,e){var s=!!f,p=s?f[0]:0,w=s?f[1]:0,b=s?f[2]:0,c,l,h,o;for(c=u==="Luminosity"?bt:wt,l=1048576,h=Math.min(r,Math.ceil(l/i)),o=0;o<r;o+=h){var a=Math.min(h,r-o),v=n.getImageData(0,o,i,a),y=t.getImageData(0,o,i,a);s&&pt(v.data,p,w,b);c(v.data,y.data,e);n.putImageData(y,0,o)}}function dt(n,t,i){var r=t.canvas,e=t.context,u,f;if(n.setTransform(t.scaleX,0,0,t.scaleY,t.offsetX,t.offsetY),u=t.backdrop||null,!t.transferMap&&y.isEnabled){f=y.composeSMask(i.canvas,r,{subtype:t.subtype,backdrop:u});n.setTransform(1,0,0,1,0,0);n.drawImage(f,t.offsetX,t.offsetY);return}kt(e,i,r.width,r.height,t.subtype,u,t.transferMap);n.drawImage(r,0,0)}var yt=15,tt=10,gt=["butt","round","square"],ni=["miter","round","bevel"],ti={},vt={},r;n.prototype={beginDrawing:function(n,t,i){var u=this.ctx.canvas.width,f=this.ctx.canvas.height,r;this.ctx.save();this.ctx.fillStyle="rgb(255, 255, 255)";this.ctx.fillRect(0,0,u,f);this.ctx.restore();i&&(r=this.cachedCanvases.getCanvas("transparent",u,f,!0),this.compositeCtx=this.ctx,this.transparentCanvas=r.canvas,this.ctx=r.context,this.ctx.save(),this.ctx.transform.apply(this.ctx,this.compositeCtx.mozCurrentTransform));this.ctx.save();n&&this.ctx.transform.apply(this.ctx,n);this.ctx.transform.apply(this.ctx,t.transform);this.baseTransform=this.ctx.mozCurrentTransform.slice();this.imageLayer&&this.imageLayer.beginLayout()},executeOperatorList:function(n,t,i,r){var s=n.argsArray,w=n.fnArray,u=t||0,h=s.length,l,f,y;if(h===u)return u;for(var a=h-u>tt&&typeof i=="function",b=a?Date.now()+yt:0,v=0,k=this.commonObjs,d=this.objs,c;;){if(r!==undefined&&u===r.nextBreakPoint)return r.breakIt(u,i),u;if(c=w[u],c!==e.dependency)this[c].apply(this,s[u]);else for(l=s[u],f=0,y=l.length;f<y;f++){var o=l[f],g=o[0]==="g"&&o[1]==="_",p=g?k:d;if(!p.isResolved(o))return p.get(o,i),u}if(u++,u===h)return u;if(a&&++v>tt){if(Date.now()>b)return i(),u;v=0}}},endDrawing:function(){this.ctx.restore();this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.drawImage(this.transparentCanvas,0,0),this.transparentCanvas=null);this.cachedCanvases.clear();y.clear();this.imageLayer&&this.imageLayer.endLayout()},setLineWidth:function(n){this.current.lineWidth=n;this.ctx.lineWidth=n},setLineCap:function(n){this.ctx.lineCap=gt[n]},setLineJoin:function(n){this.ctx.lineJoin=ni[n]},setMiterLimit:function(n){this.ctx.miterLimit=n},setDash:function(n,t){var i=this.ctx;i.setLineDash!==undefined?(i.setLineDash(n),i.lineDashOffset=t):(i.mozDash=n,i.mozDashOffset=t)},setRenderingIntent:function(){},setFlatness:function(){},setGState:function(n){for(var u,r=0,f=n.length;r<f;r++){var i=n[r],e=i[0],t=i[1];switch(e){case"LW":this.setLineWidth(t);break;case"LC":this.setLineCap(t);break;case"LJ":this.setLineJoin(t);break;case"ML":this.setMiterLimit(t);break;case"D":this.setDash(t[0],t[1]);break;case"RI":this.setRenderingIntent(t);break;case"FL":this.setFlatness(t);break;case"Font":this.setFont(t[0],t[1]);break;case"CA":this.current.strokeAlpha=i[1];break;case"ca":this.current.fillAlpha=i[1];this.ctx.globalAlpha=i[1];break;case"BM":t&&t.name&&t.name!=="Normal"?(u=t.name.replace(/([A-Z])/g,function(n){return"-"+n.toLowerCase()}).substring(1),this.ctx.globalCompositeOperation=u,this.ctx.globalCompositeOperation!==u&&s('globalCompositeOperation "'+u+'" is not supported')):this.ctx.globalCompositeOperation="source-over";break;case"SMask":this.current.activeSMask&&this.endSMaskGroup();this.current.activeSMask=t?this.tempSMask:null;this.current.activeSMask&&this.beginSMaskGroup();this.tempSMask=null}}},beginSMaskGroup:function(){var t=this.current.activeSMask,u=t.canvas.width,f=t.canvas.height,e="smaskGroupAt"+this.groupLevel,o=this.cachedCanvases.getCanvas(e,u,f,!0),r=this.ctx,s=r.mozCurrentTransform,n;this.ctx.save();n=o.context;n.scale(1/t.scaleX,1/t.scaleY);n.translate(-t.offsetX,-t.offsetY);n.transform.apply(n,s);i(r,n);this.ctx=n;this.setGState([["BM","Normal"],["ca",1],["CA",1]]);this.groupStack.push(r);this.groupLevel++},endSMaskGroup:function(){var n=this.ctx;this.groupLevel--;this.ctx=this.groupStack.pop();dt(this.ctx,this.current.activeSMask,n);this.ctx.restore();i(n,this.ctx)},save:function(){this.ctx.save();var n=this.current;this.stateStack.push(n);this.current=n.clone();this.current.activeSMask=null},restore:function(){this.stateStack.length!==0&&(this.current.activeSMask!==null&&this.endSMaskGroup(),this.current=this.stateStack.pop(),this.ctx.restore(),this.pendingClip=null,this.cachedGetSinglePixelWidth=null)},transform:function(n,t,i,r,u,f){this.ctx.transform(n,t,i,r,u,f);this.cachedGetSinglePixelWidth=null},constructPath:function(n,t){for(var o,s,l,a,f=this.ctx,h=this.current,r=h.x,u=h.y,c=0,i=0,v=n.length;c<v;c++)switch(n[c]|0){case e.rectangle:r=t[i++];u=t[i++];o=t[i++];s=t[i++];o===0&&(o=this.getSinglePixelWidth());s===0&&(s=this.getSinglePixelWidth());l=r+o;a=u+s;this.ctx.moveTo(r,u);this.ctx.lineTo(l,u);this.ctx.lineTo(l,a);this.ctx.lineTo(r,a);this.ctx.lineTo(r,u);this.ctx.closePath();break;case e.moveTo:r=t[i++];u=t[i++];f.moveTo(r,u);break;case e.lineTo:r=t[i++];u=t[i++];f.lineTo(r,u);break;case e.curveTo:r=t[i+4];u=t[i+5];f.bezierCurveTo(t[i],t[i+1],t[i+2],t[i+3],r,u);i+=6;break;case e.curveTo2:f.bezierCurveTo(r,u,t[i],t[i+1],t[i+2],t[i+3]);r=t[i+2];u=t[i+3];i+=4;break;case e.curveTo3:r=t[i+2];u=t[i+3];f.bezierCurveTo(t[i],t[i+1],r,u,r,u);i+=4;break;case e.closePath:f.closePath()}h.setCurrentPoint(r,u)},closePath:function(){this.ctx.closePath()},stroke:function(n){n=typeof n!="undefined"?n:!0;var t=this.ctx,i=this.current.strokeColor;t.lineWidth=Math.max(this.getSinglePixelWidth()*g,this.current.lineWidth);t.globalAlpha=this.current.strokeAlpha;i&&i.hasOwnProperty("type")&&i.type==="Pattern"?(t.save(),t.strokeStyle=i.getPattern(t,this),t.stroke(),t.restore()):t.stroke();n&&this.consumePath();t.globalAlpha=this.current.fillAlpha},closeStroke:function(){this.closePath();this.stroke()},fill:function(n){n=typeof n!="undefined"?n:!0;var t=this.ctx,r=this.current.fillColor,u=this.current.patternFill,i=!1;u&&(t.save(),this.baseTransform&&t.setTransform.apply(t,this.baseTransform),t.fillStyle=r.getPattern(t,this),i=!0);this.pendingEOFill?(t.mozFillRule!==undefined?(t.mozFillRule="evenodd",t.fill(),t.mozFillRule="nonzero"):t.fill("evenodd"),this.pendingEOFill=!1):t.fill();i&&t.restore();n&&this.consumePath()},eoFill:function(){this.pendingEOFill=!0;this.fill()},fillStroke:function(){this.fill(!1);this.stroke(!1);this.consumePath()},eoFillStroke:function(){this.pendingEOFill=!0;this.fillStroke()},closeFillStroke:function(){this.closePath();this.fillStroke()},closeEOFillStroke:function(){this.pendingEOFill=!0;this.closePath();this.fillStroke()},endPath:function(){this.consumePath()},clip:function(){this.pendingClip=ti},eoClip:function(){this.pendingClip=vt},beginText:function(){this.current.textMatrix=p;this.current.textMatrixScale=1;this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0},endText:function(){var r=this.pendingTextPaths,n=this.ctx,i,t;if(r===undefined){n.beginPath();return}for(n.save(),n.beginPath(),i=0;i<r.length;i++)t=r[i],n.setTransform.apply(n,t.transform),n.translate(t.x,t.y),t.addToPath(n,t.fontSize);n.restore();n.clip();n.beginPath();delete this.pendingTextPaths},setCharSpacing:function(n){this.current.charSpacing=n},setWordSpacing:function(n){this.current.wordSpacing=n},setHScale:function(n){this.current.textHScale=n/100},setLeading:function(n){this.current.leading=-n},setFont:function(n,t){var i=this.commonObjs.get(n),r=this.current,f;if(i||c("Can't find font for "+n),r.fontMatrix=i.fontMatrix?i.fontMatrix:l,(r.fontMatrix[0]===0||r.fontMatrix[3]===0)&&s("Invalid font matrix for font "+n),t<0?(t=-t,r.fontDirection=-1):r.fontDirection=1,this.current.font=i,this.current.fontSize=t,!i.isType3Font){var e=i.loadedName||"sans-serif",o=i.black?i.bold?"900":"bold":i.bold?"bold":"normal",h=i.italic?"italic":"normal",a='"'+e+'", '+i.fallbackName,u=t<k?k:t>d?d:t;this.current.fontSizeScale=t/u;f=h+" "+o+" "+u+"px "+a;this.ctx.font=f}},setTextRenderingMode:function(n){this.current.textRenderingMode=n},setTextRise:function(n){this.current.textRise=n},moveText:function(n,t){this.current.x=this.current.lineX+=n;this.current.y=this.current.lineY+=t},setLeadingMoveText:function(n,t){this.setLeading(-t);this.moveText(n,t)},setTextMatrix:function(n,t,i,r,u,f){this.current.textMatrix=[n,t,i,r,u,f];this.current.textMatrixScale=Math.sqrt(n*n+t*t);this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0},nextLine:function(){this.moveText(0,this.current.leading)},paintChar:function(n,t,i){var r=this.ctx,e=this.current,o=e.font,h=e.textRenderingMode,c=e.fontSize/e.fontSizeScale,f=h&u.FILL_STROKE_MASK,l=!!(h&u.ADD_TO_PATH_FLAG),s,a;(o.disableFontFace||l)&&(s=o.getPathGenerator(this.commonObjs,n));o.disableFontFace?(r.save(),r.translate(t,i),r.beginPath(),s(r,c),(f===u.FILL||f===u.FILL_STROKE)&&r.fill(),(f===u.STROKE||f===u.FILL_STROKE)&&r.stroke(),r.restore()):((f===u.FILL||f===u.FILL_STROKE)&&r.fillText(n,t,i),(f===u.STROKE||f===u.FILL_STROKE)&&r.strokeText(n,t,i));l&&(a=this.pendingTextPaths||(this.pendingTextPaths=[]),a.push({transform:r.mozCurrentTransform,x:t,y:i,fontSize:c,addToPath:s}))},get isFontSubpixelAAEnabled(){var i=document.createElement("canvas").getContext("2d"),t,r,n;for(i.scale(1.5,1),i.fillText("I",0,10),t=i.getImageData(0,0,10,10).data,r=!1,n=3;n<t.length;n+=4)if(t[n]>0&&t[n]<255){r=!0;break}return ot(this,"isFontSubpixelAAEnabled",r)},showText:function(n){var t=this.current,l=t.font,h,c,rt,ut,o,k,f,y,d,ct,p,et,lt;if(l.isType3Font)return this.showType3Text(n);if(h=t.fontSize,h!==0){var i=this.ctx,r=t.fontSizeScale,at=t.charSpacing,vt=t.wordSpacing,nt=t.fontDirection,b=t.textHScale*nt,yt=n.length,tt=l.vertical,pt=tt?1:-1,wt=l.defaultVMetrics,it=h*t.fontMatrix[0],bt=t.textRenderingMode===u.FILL&&!l.disableFontFace;for(i.save(),i.transform.apply(i,t.textMatrix),i.translate(t.x,t.y+t.textRise),t.patternFill&&(i.fillStyle=t.fillColor.getPattern(i,this)),nt>0?i.scale(b,-1):i.scale(b,1),c=t.lineWidth,rt=t.textMatrixScale,rt===0||c===0?(ut=t.textRenderingMode&u.FILL_STROKE_MASK,(ut===u.STROKE||ut===u.FILL_STROKE)&&(this.cachedGetSinglePixelWidth=null,c=this.getSinglePixelWidth()*g)):c/=rt,r!==1&&(i.scale(r,r),c/=r),i.lineWidth=c,o=0,k=0;k<yt;++k){if(f=n[k],w(f)){o+=pt*f*h/1e3;continue}var ot=!1,kt=(f.isSpace?vt:0)+at,ft=f.fontChar,a=f.accent,s,v,st,ht,e=f.width;tt?(y=f.vmetric||wt,d=f.vmetric?y[1]:e*.5,d=-d*it,ct=y[2]*it,e=y?-y[0]:e,s=d/r,v=(o+ct)/r):(s=o/r,v=0);l.remeasure&&e>0&&(p=i.measureText(ft).width*1e3/h*r,e<p&&this.isFontSubpixelAAEnabled?(et=e/p,ot=!0,i.save(),i.scale(et,1),s/=et):e!==p&&(s+=(e-p)/2e3*h/r));bt&&!a?i.fillText(ft,s,v):(this.paintChar(ft,s,v),a&&(st=s+a.offset.x/r,ht=v-a.offset.y/r,this.paintChar(a.fontChar,st,ht)));lt=e*it+kt*nt;o+=lt;ot&&i.restore()}tt?t.y-=o*b:t.x+=o*b;i.restore()}},showType3Text:function(n){var i=this.ctx,t=this.current,y=t.font,f=t.fontSize,p=t.fontDirection,g=y.vertical?1:-1,nt=t.charSpacing,tt=t.wordSpacing,h=t.textHScale*p,b=t.fontMatrix||l,it=n.length,rt=t.textRenderingMode===u.INVISIBLE,e,r,c,a,k,v,d;if(!rt&&f!==0){for(this.cachedGetSinglePixelWidth=null,i.save(),i.transform.apply(i,t.textMatrix),i.translate(t.x,t.y),i.scale(h,p),e=0;e<it;++e){if(r=n[e],w(r)){a=g*r*f/1e3;this.ctx.translate(a,0);t.x+=a*h;continue}if(k=(r.isSpace?tt:0)+nt,v=y.charProcOperatorList[r.operatorListId],!v){s('Type3 character "'+r.operatorListId+'" is not available');continue}this.processingType3=r;this.save();i.scale(f,f);i.transform.apply(i,b);this.executeOperatorList(v);this.restore();d=o.applyTransform([r.width,0],b);c=d[0]*f+k;i.translate(c,0);t.x+=c*h}i.restore();this.processingType3=null}},setCharWidth:function(){},setCharWidthAndBounds:function(n,t,i,r,u,f){this.ctx.rect(i,r,u-i,f-r);this.clip();this.endPath()},getColorN_Pattern:function(t){var i;if(t[0]==="TilingPattern"){var u=t[1],f=this.baseTransform||this.ctx.mozCurrentTransform.slice(),r=this,e={createCanvasGraphics:function(t){return new n(t,r.commonObjs,r.objs)}};i=new st(t,u,this.ctx,e,f)}else i=b(t);return i},setStrokeColorN:function(){this.current.strokeColor=this.getColorN_Pattern(arguments)},setFillColorN:function(){this.current.fillColor=this.getColorN_Pattern(arguments);this.current.patternFill=!0},setStrokeRGBColor:function(n,t,i){var r=o.makeCssRgb(n,t,i);this.ctx.strokeStyle=r;this.current.strokeColor=r},setFillRGBColor:function(n,t,i){var r=o.makeCssRgb(n,t,i);this.ctx.fillStyle=r;this.current.fillColor=r;this.current.patternFill=!1},shadingFill:function(n){var i=this.ctx,s,t;if(this.save(),s=b(n),i.fillStyle=s.getPattern(i,this,!0),t=i.mozCurrentTransformInverse,t){var h=i.canvas,c=h.width,l=h.height,r=o.applyTransform([0,0],t),u=o.applyTransform([0,l],t),f=o.applyTransform([c,0],t),e=o.applyTransform([c,l],t),a=Math.min(r[0],u[0],f[0],e[0]),v=Math.min(r[1],u[1],f[1],e[1]),y=Math.max(r[0],u[0],f[0],e[0]),p=Math.max(r[1],u[1],f[1],e[1]);this.ctx.fillRect(a,v,y-a,p-v)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.restore()},beginInlineImage:function(){c("Should not call beginInlineImage")},beginImageData:function(){c("Should not call beginImageData")},paintFormXObjectBegin:function(n,t){if(this.save(),this.baseTransformStack.push(this.baseTransform),v(n)&&6===n.length&&this.transform.apply(this,n),this.baseTransform=this.ctx.mozCurrentTransform,v(t)&&4===t.length){var i=t[2]-t[0],r=t[3]-t[1];this.ctx.rect(t[0],t[1],i,r);this.clip();this.endPath()}},paintFormXObjectEnd:function(){this.restore();this.baseTransform=this.baseTransformStack.pop()},beginGroup:function(n){var t,w,u,b,y,p,r;this.save();t=this.ctx;n.isolated||et("TODO: Support non-isolated groups.");n.knockout&&s("Knockout groups not supported.");w=t.mozCurrentTransform;n.matrix&&t.transform.apply(t,n.matrix);ft(n.bbox,"Bounding box is required.");u=o.getAxialAlignedBoundingBox(n.bbox,t.mozCurrentTransform);b=[0,0,t.canvas.width,t.canvas.height];u=o.intersect(u,b)||[0,0,0,0];var f=Math.floor(u[0]),e=Math.floor(u[1]),c=Math.max(Math.ceil(u[2])-f,1),l=Math.max(Math.ceil(u[3])-e,1),a=1,v=1;c>h&&(a=c/h,c=h);l>h&&(v=l/h,l=h);y="groupAt"+this.groupLevel;n.smask&&(y+="_smask_"+this.smaskCounter++%2);p=this.cachedCanvases.getCanvas(y,c,l,!0);r=p.context;r.scale(1/a,1/v);r.translate(-f,-e);r.transform.apply(r,w);n.smask?this.smaskStack.push({canvas:p.canvas,context:r,offsetX:f,offsetY:e,scaleX:a,scaleY:v,subtype:n.smask.subtype,backdrop:n.smask.backdrop,transferMap:n.smask.transferMap||null}):(t.setTransform(1,0,0,1,0,0),t.translate(f,e),t.scale(a,v));i(t,r);this.ctx=r;this.setGState([["BM","Normal"],["ca",1],["CA",1]]);this.groupStack.push(t);this.groupLevel++},endGroup:function(n){this.groupLevel--;var t=this.ctx;this.ctx=this.groupStack.pop();this.ctx.imageSmoothingEnabled!==undefined?this.ctx.imageSmoothingEnabled=!1:this.ctx.mozImageSmoothingEnabled=!1;n.smask?this.tempSMask=this.smaskStack.pop():this.ctx.drawImage(t.canvas,0,0);this.restore()},beginAnnotations:function(){this.save();this.current=new rt;this.baseTransform&&this.ctx.setTransform.apply(this.ctx,this.baseTransform)},endAnnotations:function(){this.restore()},beginAnnotation:function(n,t,i){if(this.save(),v(n)&&4===n.length){var r=n[2]-n[0],u=n[3]-n[1];this.ctx.rect(n[0],n[1],r,u);this.clip();this.endPath()}this.transform.apply(this,t);this.transform.apply(this,i)},endAnnotation:function(){this.restore()},paintJpegXObject:function(n,t,i){var r=this.objs.get(n),u,f,e;if(!r){s("Dependent image isn't ready yet");return}this.save();u=this.ctx;u.scale(1/t,-1/i);u.drawImage(r,0,0,r.width,r.height,0,-i,t,i);this.imageLayer&&(f=u.mozCurrentTransformInverse,e=this.getCanvasPosition(0,0),this.imageLayer.appendImage({objId:n,left:e[0],top:e[1],width:t/f[0],height:i/f[3]}));this.restore()},paintImageMaskXObject:function(n){var s=this.ctx,u=n.width,f=n.height,o=this.current.fillColor,h=this.current.patternFill,r=this.processingType3,e,i;if(ht&&r&&r.compiled===undefined&&(r.compiled=u<=nt&&f<=nt?lt({data:n.data,width:u,height:f}):null),r&&r.compiled){r.compiled(s);return}e=this.cachedCanvases.getCanvas("maskCanvas",u,f);i=e.context;i.save();t(i,n);i.globalCompositeOperation="source-in";i.fillStyle=h?o.getPattern(i,this):o;i.fillRect(0,0,u,f);i.restore();this.paintInlineImageXObject(e.canvas)},paintImageMaskXObjectRepeat:function(n,i,r,u){var s=n.width,h=n.height,c=this.current.fillColor,v=this.current.patternFill,l=this.cachedCanvases.getCanvas("maskCanvas",s,h),f=l.context,e,o,a;for(f.save(),t(f,n),f.globalCompositeOperation="source-in",f.fillStyle=v?c.getPattern(f,this):c,f.fillRect(0,0,s,h),f.restore(),e=this.ctx,o=0,a=u.length;o<a;o+=2)e.save(),e.transform(i,0,0,r,u[o],u[o+1]),e.scale(1,-1),e.drawImage(l.canvas,0,0,s,h,0,-1,1,1),e.restore()},paintImageMaskXObjectGroup:function(n){for(var r=this.ctx,s=this.current.fillColor,c=this.current.patternFill,f=0,l=n.length;f<l;f++){var u=n[f],e=u.width,o=u.height,h=this.cachedCanvases.getCanvas("maskCanvas",e,o),i=h.context;i.save();t(i,u);i.globalCompositeOperation="source-in";i.fillStyle=c?s.getPattern(i,this):s;i.fillRect(0,0,e,o);i.restore();r.save();r.transform.apply(r,u.transform);r.scale(1,-1);r.drawImage(h.canvas,0,0,e,o,0,-1,1,1);r.restore()}},paintImageXObject:function(n){var t=this.objs.get(n);if(!t){s("Dependent image isn't ready yet");return}this.paintInlineImageXObject(t)},paintImageXObjectRepeat:function(n,t,i,r){var f=this.objs.get(n),u,o;if(!f){s("Dependent image isn't ready yet");return}var h=f.width,c=f.height,e=[];for(u=0,o=r.length;u<o;u+=2)e.push({transform:[t,0,0,i,r[u],r[u+1]],x:0,y:0,w:h,h:c});this.paintInlineImageXObjectGroup(f,e)},paintInlineImageXObject:function(n){var s=n.width,r=n.height,l=this.ctx,c,u,f,p;this.save();l.scale(1/s,-1/r);var e=l.mozCurrentTransformInverse,w=e[0],b=e[1],a=Math.max(Math.sqrt(w*w+b*b),1),k=e[2],d=e[3],v=Math.max(Math.sqrt(k*k+d*d),1),h,o;n instanceof HTMLElement||!n.data?h=n:(o=this.cachedCanvases.getCanvas("inlineImage",s,r),c=o.context,at(c,n),h=o.canvas);for(var t=s,i=r,y="prescale1";a>2&&t>1||v>2&&i>1;)u=t,f=i,a>2&&t>1&&(u=Math.ceil(t/2),a/=t/u),v>2&&i>1&&(f=Math.ceil(i/2),v/=i/f),o=this.cachedCanvases.getCanvas(y,u,f),c=o.context,c.clearRect(0,0,u,f),c.drawImage(h,0,0,t,i,0,0,u,f),h=o.canvas,t=u,i=f,y=y==="prescale1"?"prescale2":"prescale1";l.drawImage(h,0,0,t,i,0,-r,s,r);this.imageLayer&&(p=this.getCanvasPosition(0,-r),this.imageLayer.appendImage({imgData:n,left:p[0],top:p[1],width:s/e[0],height:r/e[3]}));this.restore()},paintInlineImageXObjectGroup:function(n,t){var r=this.ctx,e=n.width,o=n.height,s=this.cachedCanvases.getCanvas("inlineImage",e,o),c=s.context,u,h,i,f;for(at(c,n),u=0,h=t.length;u<h;u++)i=t[u],r.save(),r.transform.apply(r,i.transform),r.scale(1,-1),r.drawImage(s.canvas,i.x,i.y,i.w,i.h,0,-1,1,1),this.imageLayer&&(f=this.getCanvasPosition(i.x,i.y),this.imageLayer.appendImage({imgData:n,left:f[0],top:f[1],width:e,height:o})),r.restore()},paintSolidColorImageMask:function(){this.ctx.fillRect(0,0,1,1)},paintXObject:function(){s("Unsupported 'paintXObject' command.")},markPoint:function(){},markPointProps:function(){},beginMarkedContent:function(){},beginMarkedContentProps:function(){},endMarkedContent:function(){},beginCompat:function(){},endCompat:function(){},consumePath:function(){var n=this.ctx;this.pendingClip&&(this.pendingClip===vt?n.mozFillRule!==undefined?(n.mozFillRule="evenodd",n.clip(),n.mozFillRule="nonzero"):n.clip("evenodd"):n.clip(),this.pendingClip=null);n.beginPath()},getSinglePixelWidth:function(){if(this.cachedGetSinglePixelWidth===null){var n=this.ctx.mozCurrentTransformInverse;this.cachedGetSinglePixelWidth=Math.sqrt(Math.max(n[0]*n[0]+n[1]*n[1],n[2]*n[2]+n[3]*n[3]))}return this.cachedGetSinglePixelWidth},getCanvasPosition:function(n,t){var i=this.ctx.mozCurrentTransform;return[i[0]*n+i[2]*t+i[4],i[1]*n+i[3]*t+i[5]]}};for(r in e)n.prototype[e[r]]=n.prototype[r];return n}();n.CanvasGraphics=at;n.createScratchCanvas=tt}),function(n,t){t(n.pdfjsDisplayAPI={},n.pdfjsSharedUtil,n.pdfjsDisplayFontLoader,n.pdfjsDisplayCanvas,n.pdfjsDisplayMetadata,n.pdfjsSharedGlobal)}(this,function(n,t,i,u,f,e){function gt(n,t,i,r){return n.destroyed?Promise.reject(new Error("Worker was destroyed")):(t.disableAutoFetch=o.disableAutoFetch,t.disableStream=o.disableStream,t.chunkedViewerLoading=!!i,i&&(t.length=i.length,t.initialData=i.initialData),n.messageHandler.sendWithPromise("GetDocRequest",{docId:r,source:t,disableRange:o.disableRange,maxImageSize:o.maxImageSize,cMapUrl:o.cMapUrl,cMapPacked:o.cMapPacked,disableFontFace:o.disableFontFace,disableCreateObjectURL:o.disableCreateObjectURL,verbosity:o.verbosity}).then(function(t){if(n.destroyed)throw new Error("Worker was destroyed");return t}))}var ft=t.InvalidPDFException,a=t.MessageHandler,et=t.MissingPDFException,p=t.PasswordResponses,w=t.PasswordException,ot=t.StatTimer,st=t.UnexpectedResponseException,ht=t.UnknownErrorException,ct=t.Util,s=t.createPromiseCapability,lt=t.combineUrl,h=t.error,v=t.deprecated,b=t.info,k=t.isArrayBuffer,at=t.loadJpegStream,vt=t.stringToBytes,d=t.warn,yt=i.FontFaceObject,pt=i.FontLoader,wt=u.CanvasGraphics,bt=u.createScratchCanvas,kt=f.Metadata,o=e.PDFJS,c=e.globalScope,dt=65536,y=!1,g,nt,l;typeof module!="undefined"&&module.require&&(o.disableWorker=!0,typeof require.ensure=="undefined"&&(require.ensure=require("node-ensure")),y=!0);typeof __webpack_require__!="undefined"&&(o.workerSrc=require("entry?name=[hash]-worker.js!./pdf.worker.js"),y=!0);typeof requirejs!="undefined"&&requirejs.toUrl&&(o.workerSrc=requirejs.toUrl("pdfjs-dist/build/pdf.worker.js"));g=y?function(n){require.ensure([],function(){require("./pdf.worker.js");n()})}:typeof requirejs!="undefined"?function(n){requirejs(["pdfjs-dist/build/pdf.worker"],function(){n()})}:null;o.maxImageSize=o.maxImageSize===undefined?-1:o.maxImageSize;o.cMapUrl=o.cMapUrl===undefined?null:o.cMapUrl;o.cMapPacked=o.cMapPacked===undefined?!1:o.cMapPacked;o.disableFontFace=o.disableFontFace===undefined?!1:o.disableFontFace;o.imageResourcesPath=o.imageResourcesPath===undefined?"":o.imageResourcesPath;o.disableWorker=o.disableWorker===undefined?!1:o.disableWorker;o.workerSrc=o.workerSrc===undefined?null:o.workerSrc;o.disableRange=o.disableRange===undefined?!1:o.disableRange;o.disableStream=o.disableStream===undefined?!1:o.disableStream;o.disableAutoFetch=o.disableAutoFetch===undefined?!1:o.disableAutoFetch;o.pdfBug=o.pdfBug===undefined?!1:o.pdfBug;o.postMessageTransfers=o.postMessageTransfers===undefined?!0:o.postMessageTransfers;o.disableCreateObjectURL=o.disableCreateObjectURL===undefined?!1:o.disableCreateObjectURL;o.disableWebGL=o.disableWebGL===undefined?!0:o.disableWebGL;o.disableFullscreen=o.disableFullscreen===undefined?!1:o.disableFullscreen;o.useOnlyCssZoom=o.useOnlyCssZoom===undefined?!1:o.useOnlyCssZoom;o.verbosity=o.verbosity===undefined?o.VERBOSITY_LEVELS.warnings:o.verbosity;o.maxCanvasPixels=o.maxCanvasPixels===undefined?16777216:o.maxCanvasPixels;o.openExternalLinksInNewWindow=o.openExternalLinksInNewWindow===undefined?!1:o.openExternalLinksInNewWindow;o.externalLinkTarget=o.externalLinkTarget===undefined?o.LinkTarget.NONE:o.externalLinkTarget;o.externalLinkRel=o.externalLinkRel===undefined?"noreferrer":o.externalLinkRel;o.isEvalSupported=o.isEvalSupported===undefined?!0:o.isEvalSupported;o.getDocument=function(n,t,i,r){var e=new nt,f,u,o,p;arguments.length>1&&v("getDocument is called with pdfDataRangeTransport, passwordCallback or progressCallback argument");t&&(t instanceof l||(t=Object.create(t),t.length=n.length,t.initialData=n.initialData,t.abort||(t.abort=function(){})),n=Object.create(n),n.range=t);e.onPassword=i||null;e.onProgress=r||null;typeof n=="string"?f={url:n}:k(n)?f={data:n}:n instanceof l?f={range:n}:(typeof n!="object"&&h("Invalid parameter in getDocument, need either Uint8Array, string or a parameter object"),n.url||n.data||n.range||h("Invalid parameter object: need either .data, .range or .url"),f=n);var s={},y=null,c=null;for(u in f){if(u==="url"&&typeof window!="undefined"){s[u]=lt(window.location.href,f[u]);continue}else if(u==="range"){y=f[u];continue}else if(u==="worker"){c=f[u];continue}else if(u==="data"&&!(f[u]instanceof Uint8Array)){o=f[u];typeof o=="string"?s[u]=vt(o):typeof o!="object"||o===null||isNaN(o.length)?k(o)?s[u]=new Uint8Array(o):h("Invalid PDF binary data: either typed array, string or array-like object is expected in the data property."):s[u]=new Uint8Array(o);continue}s[u]=f[u]}return s.rangeChunkSize=s.rangeChunkSize||dt,c||(c=new rt,e._worker=c),p=e.docId,c.promise.then(function(){if(e.destroyed)throw new Error("Loading aborted");return gt(c,s,y,p).then(function(n){var t,i;if(e.destroyed)throw new Error("Loading aborted");t=new a(p,n,c.port);t.send("Ready",null);i=new ni(t,e,y);e._transport=i})}).catch(e._capability.reject),e};nt=function(){function n(){this._capability=s();this._transport=null;this._worker=null;this.docId="d"+t++;this.destroyed=!1;this.onPassword=null;this.onProgress=null;this.onUnsupportedFeature=null}var t=0;return n.prototype={get promise(){return this._capability.promise},destroy:function(){this.destroyed=!0;var n=this._transport?this._transport.destroy():Promise.resolve();return n.then(function(){this._transport=null;this._worker&&(this._worker.destroy(),this._worker=null)}.bind(this))},then:function(){return this.promise.then.apply(this.promise,arguments)}},n}();l=function(){function n(n,t){this.length=n;this.initialData=t;this._rangeListeners=[];this._progressListeners=[];this._progressiveReadListeners=[];this._readyCapability=s()}return n.prototype={addRangeListener:function(n){this._rangeListeners.push(n)},addProgressListener:function(n){this._progressListeners.push(n)},addProgressiveReadListener:function(n){this._progressiveReadListeners.push(n)},onDataRange:function(n,t){for(var r=this._rangeListeners,i=0,u=r.length;i<u;++i)r[i](n,t)},onDataProgress:function(n){this._readyCapability.promise.then(function(){for(var i=this._progressListeners,t=0,r=i.length;t<r;++t)i[t](n)}.bind(this))},onDataProgressiveRead:function(n){this._readyCapability.promise.then(function(){for(var i=this._progressiveReadListeners,t=0,r=i.length;t<r;++t)i[t](n)}.bind(this))},transportReady:function(){this._readyCapability.resolve()},requestDataRange:function(){throw new Error("Abstract method PDFDataRangeTransport.requestDataRange");},abort:function(){}},n}();o.PDFDataRangeTransport=l;var tt=function(){function n(n,t,i){this.pdfInfo=n;this.transport=t;this.loadingTask=i}return n.prototype={get numPages(){return this.pdfInfo.numPages},get fingerprint(){return this.pdfInfo.fingerprint},getPage:function(n){return this.transport.getPage(n)},getPageIndex:function(n){return this.transport.getPageIndex(n)},getDestinations:function(){return this.transport.getDestinations()},getDestination:function(n){return this.transport.getDestination(n)},getPageLabels:function(){return this.transport.getPageLabels()},getAttachments:function(){return this.transport.getAttachments()},getJavaScript:function(){return this.transport.getJavaScript()},getOutline:function(){return this.transport.getOutline()},getMetadata:function(){return this.transport.getMetadata()},getData:function(){return this.transport.getData()},getDownloadInfo:function(){return this.transport.downloadInfoCapability.promise},getStats:function(){return this.transport.getStats()},cleanup:function(){this.transport.startCleanup()},destroy:function(){return this.loadingTask.destroy()}},n}(),it=function(){function n(n,t,i){this.pageIndex=n;this.pageInfo=t;this.transport=i;this.stats=new ot;this.stats.enabled=!!c.PDFJS.enableStats;this.commonObjs=i.commonObjs;this.objs=new ut;this.cleanupAfterRender=!1;this.pendingCleanup=!1;this.intentStates={};this.destroyed=!1}return n.prototype={get pageNumber(){return this.pageIndex+1},get rotate(){return this.pageInfo.rotate},get ref(){return this.pageInfo.ref},get view(){return this.pageInfo.view},getViewport:function(n,t){return arguments.length<2&&(t=this.rotate),new o.PageViewport(this.view,n,t,0,0)},getAnnotations:function(n){var t=n&&n.intent||null;return this.annotationsPromise&&this.annotationsIntent===t||(this.annotationsPromise=this.transport.getAnnotations(this.pageIndex,t),this.annotationsIntent=t),this.annotationsPromise},render:function(n){function o(n){var r=t.renderTasks.indexOf(i);r>=0&&t.renderTasks.splice(r,1);u.cleanupAfterRender&&(u.pendingCleanup=!0);u._tryCleanup();n?i.capability.reject(n):i.capability.resolve();f.timeEnd("Rendering");f.timeEnd("Overall")}var f=this.stats,r,t,i,e,u;return f.time("Overall"),this.pendingCleanup=!1,r=n.intent==="print"?"print":"display",this.intentStates[r]||(this.intentStates[r]={}),t=this.intentStates[r],t.displayReadyCapability||(t.receivingOperatorList=!0,t.displayReadyCapability=s(),t.operatorList={fnArray:[],argsArray:[],lastChunk:!1},this.stats.time("Page Request"),this.transport.messageHandler.send("RenderPageRequest",{pageIndex:this.pageNumber-1,intent:r})),i=new ii(o,n,this.objs,this.commonObjs,t.operatorList,this.pageNumber),i.useRequestAnimationFrame=r!=="print",t.renderTasks||(t.renderTasks=[]),t.renderTasks.push(i),e=i.task,n.continueCallback&&(v("render is used with continueCallback parameter"),e.onContinue=n.continueCallback),u=this,t.displayReadyCapability.promise.then(function(n){if(u.pendingCleanup){o();return}f.time("Rendering");i.initalizeGraphics(n);i.operatorListChanged()},function(n){o(n)}),e},getOperatorList:function(){function r(){n.operatorList.lastChunk&&n.opListReadCapability.resolve(n.operatorList)}var t="oplist",n,i;return this.intentStates[t]||(this.intentStates[t]={}),n=this.intentStates[t],n.opListReadCapability||(i={},i.operatorListChanged=r,n.receivingOperatorList=!0,n.opListReadCapability=s(),n.renderTasks=[],n.renderTasks.push(i),n.operatorList={fnArray:[],argsArray:[],lastChunk:!1},this.transport.messageHandler.send("RenderPageRequest",{pageIndex:this.pageIndex,intent:t})),n.opListReadCapability.promise},getTextContent:function(n){var t=n&&n.normalizeWhitespace||!1;return this.transport.messageHandler.sendWithPromise("GetTextContent",{pageIndex:this.pageNumber-1,normalizeWhitespace:t})},_destroy:function(){this.destroyed=!0;this.transport.pageCache[this.pageIndex]=null;var n=[];return Object.keys(this.intentStates).forEach(function(t){var i=this.intentStates[t];i.renderTasks.forEach(function(t){var i=t.capability.promise.catch(function(){});n.push(i);t.cancel()})},this),this.objs.clear(),this.annotationsPromise=null,this.pendingCleanup=!1,Promise.all(n)},destroy:function(){v("page destroy method, use cleanup() instead");this.cleanup()},cleanup:function(){this.pendingCleanup=!0;this._tryCleanup()},_tryCleanup:function(){this.pendingCleanup&&!Object.keys(this.intentStates).some(function(n){var t=this.intentStates[n];return t.renderTasks.length!==0||t.receivingOperatorList},this)&&(Object.keys(this.intentStates).forEach(function(n){delete this.intentStates[n]},this),this.objs.clear(),this.annotationsPromise=null,this.pendingCleanup=!1)},_startRenderPage:function(n,t){var i=this.intentStates[t];i.displayReadyCapability&&i.displayReadyCapability.resolve(n)},_renderPageChunk:function(n,t){for(var r=this.intentStates[t],i=0,u=n.length;i<u;i++)r.operatorList.fnArray.push(n.fnArray[i]),r.operatorList.argsArray.push(n.argsArray[i]);for(r.operatorList.lastChunk=n.lastChunk,i=0;i<r.renderTasks.length;i++)r.renderTasks[i].operatorListChanged();n.lastChunk&&(r.receivingOperatorList=!1,this._tryCleanup())}},n}(),rt=function(){function n(){if(o.workerSrc)return o.workerSrc;if(r)return r.replace(/\.js$/i,".worker.js");h("No PDFJS.workerSrc specified")}function u(){if(!o.fakeWorkerFilesLoadedCapability){o.fakeWorkerFilesLoadedCapability=s();var t=g||function(t){ct.loadScript(n(),t)};t(function(){o.fakeWorkerFilesLoadedCapability.resolve()})}return o.fakeWorkerFilesLoadedCapability.promise}function t(n){this.name=n;this.destroyed=!1;this._readyCapability=s();this._port=null;this._webWorker=null;this._messageHandler=null;this._initialize()}var i=0;return t.prototype={get promise(){return this._readyCapability.promise},get port(){return this._port},get messageHandler(){return this._messageHandler},_initialize:function(){var u,i,t,r;if(!c.PDFJS.disableWorker&&typeof Worker!="undefined"){u=n();try{i=new Worker(u);t=new a("main","worker",i);t.on("test",function(n){if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));t.destroy();i.terminate();return}var r=n&&n.supportTypedArray;r?(this._messageHandler=t,this._port=i,this._webWorker=i,n.supportTransfers||(o.postMessageTransfers=!1),this._readyCapability.resolve()):(this._setupFakeWorker(),t.destroy(),i.terminate())}.bind(this));t.on("console_log",function(n){console.log.apply(console,n)});t.on("console_error",function(n){console.error.apply(console,n)});t.on("ready",function(){if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));t.destroy();i.terminate();return}try{r()}catch(n){this._setupFakeWorker()}}.bind(this));r=function(){var n=new Uint8Array([o.postMessageTransfers?255:0]);try{t.send("test",n,[n.buffer])}catch(i){b("Cannot use postMessage transfers");n[0]=0;t.send("test",n)}};r();return}catch(e){b("The worker has been disabled.")}}this._setupFakeWorker()},_setupFakeWorker:function(){c.PDFJS.disableWorker||(d("Setting up fake worker."),c.PDFJS.disableWorker=!0);u().then(function(){var n,t,r,u;if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}n={_listeners:[],postMessage:function(n){var t={data:n};this._listeners.forEach(function(n){n.call(this,t)},this)},addEventListener:function(n,t){this._listeners.push(t)},removeEventListener:function(n,t){var i=this._listeners.indexOf(t);this._listeners.splice(i,1)},terminate:function(){}};this._port=n;t="fake"+i++;r=new a(t+"_worker",t,n);o.WorkerMessageHandler.setup(r,n);u=new a(t,t+"_worker",n);this._messageHandler=u;this._readyCapability.resolve()}.bind(this))},destroy:function(){this.destroyed=!0;this._webWorker&&(this._webWorker.terminate(),this._webWorker=null);this._port=null;this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}},t}();o.PDFWorker=rt;var ni=function(){function n(n,t,i){this.messageHandler=n;this.loadingTask=t;this.pdfDataRangeTransport=i;this.commonObjs=new ut;this.fontLoader=new pt(t.docId);this.destroyed=!1;this.destroyCapability=null;this.pageCache=[];this.pagePromises=[];this.downloadInfoCapability=s();this.setupMessageHandler()}return n.prototype={destroy:function(){var t,n,i;return this.destroyCapability?this.destroyCapability.promise:(this.destroyed=!0,this.destroyCapability=s(),t=[],this.pageCache.forEach(function(n){n&&t.push(n._destroy())}),this.pageCache=[],this.pagePromises=[],n=this,i=this.messageHandler.sendWithPromise("Terminate",null),t.push(i),Promise.all(t).then(function(){n.fontLoader.clear();n.pdfDataRangeTransport&&(n.pdfDataRangeTransport.abort(),n.pdfDataRangeTransport=null);n.messageHandler&&(n.messageHandler.destroy(),n.messageHandler=null);n.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise)},setupMessageHandler:function(){function i(t){n.send("UpdatePassword",t)}var n=this.messageHandler,t=this.pdfDataRangeTransport;if(t){t.addRangeListener(function(t,i){n.send("OnDataRange",{begin:t,chunk:i})});t.addProgressListener(function(t){n.send("OnDataProgress",{loaded:t})});t.addProgressiveReadListener(function(t){n.send("OnDataRange",{chunk:t})});n.on("RequestDataRange",function(n){t.requestDataRange(n.begin,n.end)},this)}n.on("GetDoc",function(n){var r=n.pdfInfo,t,i;this.numPages=n.pdfInfo.numPages;t=this.loadingTask;i=new tt(r,this,t);this.pdfDocument=i;t._capability.resolve(i)},this);n.on("NeedPassword",function(n){var t=this.loadingTask;if(t.onPassword)return t.onPassword(i,p.NEED_PASSWORD);t._capability.reject(new w(n.message,n.code))},this);n.on("IncorrectPassword",function(n){var t=this.loadingTask;if(t.onPassword)return t.onPassword(i,p.INCORRECT_PASSWORD);t._capability.reject(new w(n.message,n.code))},this);n.on("InvalidPDF",function(n){this.loadingTask._capability.reject(new ft(n.message))},this);n.on("MissingPDF",function(n){this.loadingTask._capability.reject(new et(n.message))},this);n.on("UnexpectedResponse",function(n){this.loadingTask._capability.reject(new st(n.message,n.status))},this);n.on("UnknownError",function(n){this.loadingTask._capability.reject(new ht(n.message,n.details))},this);n.on("DataLoaded",function(n){this.downloadInfoCapability.resolve(n)},this);n.on("PDFManagerReady",function(){this.pdfDataRangeTransport&&this.pdfDataRangeTransport.transportReady()},this);n.on("StartRenderPage",function(n){if(!this.destroyed){var t=this.pageCache[n.pageIndex];t.stats.timeEnd("Page Request");t._startRenderPage(n.transparency,n.intent)}},this);n.on("RenderPageChunk",function(n){if(!this.destroyed){var t=this.pageCache[n.pageIndex];t._renderPageChunk(n.operatorList,n.intent)}},this);n.on("commonobj",function(n){var t,u,i,f,r;if(!this.destroyed&&(t=n[0],u=n[1],!this.commonObjs.hasData(t)))switch(u){case"Font":if(i=n[2],"error"in i){r=i.error;d("Error during font loading: "+r);this.commonObjs.resolve(t,r);break}else f=new yt(i);this.fontLoader.bind([f],function(){this.commonObjs.resolve(t,f)}.bind(this));break;case"FontPath":this.commonObjs.resolve(t,n[2]);break;default:r("Got unknown common object type "+u)}},this);n.on("obj",function(n){var f;if(!this.destroyed){var r=n[0],e=n[1],u=n[2],i=this.pageCache[e],t;if(!i.objs.hasData(r))switch(u){case"JpegStream":t=n[3];at(r,t,i.objs);break;case"Image":t=n[3];i.objs.resolve(r,t);f=8e6;t&&"data"in t&&t.data.length>f&&(i.cleanupAfterRender=!0);break;default:h("Got unknown object type "+u)}}},this);n.on("DocProgress",function(n){if(!this.destroyed){var t=this.loadingTask;if(t.onProgress)t.onProgress({loaded:n.loaded,total:n.total})}},this);n.on("PageError",function(n){if(!this.destroyed){var i=this.pageCache[n.pageNum-1],t=i.intentStates[n.intent];t.displayReadyCapability?t.displayReadyCapability.reject(n.error):h(n.error)}},this);n.on("UnsupportedFeature",function(n){if(!this.destroyed){var t=n.featureId,i=this.loadingTask;if(i.onUnsupportedFeature)i.onUnsupportedFeature(t);o.UnsupportedManager.notify(t)}},this);n.on("JpegDecode",function(n){if(this.destroyed)return Promise.reject("Worker was terminated");var i=n[0],t=n[1];return t!==3&&t!==1?Promise.reject(new Error("Only 3 components or 1 component can be returned")):new Promise(function(n,r){var u=new Image;u.onload=function(){var o=u.width,s=u.height,h=o*s,c=h*4,f=new Uint8Array(h*t),a=bt(o,s),l=a.getContext("2d"),e,i,r;if(l.drawImage(u,0,0),e=l.getImageData(0,0,o,s).data,t===3)for(i=0,r=0;i<c;i+=4,r+=3)f[r]=e[i],f[r+1]=e[i+1],f[r+2]=e[i+2];else if(t===1)for(i=0,r=0;i<c;i+=4,r++)f[r]=e[i];n({data:f,width:o,height:s})};u.onerror=function(){r(new Error("JpegDecode failed to load image"))};u.src=i})},this)},getData:function(){return this.messageHandler.sendWithPromise("GetData",null)},getPage:function(n){var t,i;return n<=0||n>this.numPages||(n|0)!==n?Promise.reject(new Error("Invalid page request")):(t=n-1,t in this.pagePromises)?this.pagePromises[t]:(i=this.messageHandler.sendWithPromise("GetPage",{pageIndex:t}).then(function(n){if(this.destroyed)throw new Error("Transport destroyed");var i=new it(t,n,this);return this.pageCache[t]=i,i}.bind(this)),this.pagePromises[t]=i,i)},getPageIndex:function(n){return this.messageHandler.sendWithPromise("GetPageIndex",{ref:n})},getAnnotations:function(n,t){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:n,intent:t})},getDestinations:function(){return this.messageHandler.sendWithPromise("GetDestinations",null)},getDestination:function(n){return this.messageHandler.sendWithPromise("GetDestination",{id:n})},getPageLabels:function(){return this.messageHandler.sendWithPromise("GetPageLabels",null)},getAttachments:function(){return this.messageHandler.sendWithPromise("GetAttachments",null)},getJavaScript:function(){return this.messageHandler.sendWithPromise("GetJavaScript",null)},getOutline:function(){return this.messageHandler.sendWithPromise("GetOutline",null)},getMetadata:function(){return this.messageHandler.sendWithPromise("GetMetadata",null).then(function(n){return{info:n[0],metadata:n[1]?new kt(n[1]):null}})},getStats:function(){return this.messageHandler.sendWithPromise("GetStats",null)},startCleanup:function(){this.messageHandler.sendWithPromise("Cleanup",null).then(function(){for(var t,n=0,i=this.pageCache.length;n<i;n++)t=this.pageCache[n],t&&t.cleanup();this.commonObjs.clear();this.fontLoader.clear()}.bind(this))}},n}(),ut=function(){function n(){this.objs={}}return n.prototype={ensureObj:function(n){if(this.objs[n])return this.objs[n];var t={capability:s(),data:null,resolved:!1};return this.objs[n]=t,t},get:function(n,t){if(t)return this.ensureObj(n).capability.promise.then(t),null;var i=this.objs[n];return i&&i.resolved||h("Requesting object that isn't resolved yet "+n),i.data},resolve:function(n,t){var i=this.ensureObj(n);i.resolved=!0;i.data=t;i.capability.resolve(t)},isResolved:function(n){var t=this.objs;return t[n]?t[n].resolved:!1},hasData:function(n){return this.isResolved(n)},getData:function(n){var t=this.objs;return t[n]&&t[n].resolved?t[n].data:null},clear:function(){this.objs={}}},n}(),ti=function(){function n(n){this._internalRenderTask=n;this.onContinue=null}return n.prototype={get promise(){return this._internalRenderTask.capability.promise},cancel:function(){this._internalRenderTask.cancel()},then:function(){return this.promise.then.apply(this.promise,arguments)}},n}(),ii=function(){function n(n,t,i,r,u,f){this.callback=n;this.params=t;this.objs=i;this.commonObjs=r;this.operatorListIdx=null;this.operatorList=u;this.pageNumber=f;this.running=!1;this.graphicsReadyCallback=null;this.graphicsReady=!1;this.useRequestAnimationFrame=!1;this.cancelled=!1;this.capability=s();this.task=new ti(this);this._continueBound=this._continue.bind(this);this._scheduleNextBound=this._scheduleNext.bind(this);this._nextBound=this._next.bind(this)}return n.prototype={initalizeGraphics:function(n){if(!this.cancelled){o.pdfBug&&"StepperManager"in c&&c.StepperManager.enabled&&(this.stepper=c.StepperManager.create(this.pageNumber-1),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());var t=this.params;this.gfx=new wt(t.canvasContext,this.commonObjs,this.objs,t.imageLayer);this.gfx.beginDrawing(t.transform,t.viewport,n);this.operatorListIdx=0;this.graphicsReady=!0;this.graphicsReadyCallback&&this.graphicsReadyCallback()}},cancel:function(){this.running=!1;this.cancelled=!0;this.callback("cancelled")},operatorListChanged:function(){if(!this.graphicsReady){this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound);return}(this.stepper&&this.stepper.updateOperatorList(this.operatorList),this.running)||this._continue()},_continue:function(){(this.running=!0,this.cancelled)||(this.task.onContinue?this.task.onContinue.call(this.task,this._scheduleNextBound):this._scheduleNext())},_scheduleNext:function(){this.useRequestAnimationFrame?window.requestAnimationFrame(this._nextBound):Promise.resolve(undefined).then(this._nextBound)},_next:function(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),this.callback())))}},n}();o.UnsupportedManager=function(){var n=[];return{listen:function(t){v("Global UnsupportedManager.listen is used:  use PDFDocumentLoadingTask.onUnsupportedFeature instead");n.push(t)},notify:function(t){for(var i=0,r=n.length;i<r;i++)n[i](t)}}}();n.getDocument=o.getDocument;n.PDFDataRangeTransport=l;n.PDFDocumentProxy=tt;n.PDFPageProxy=it})}).call(t);n.PDFJS=t.pdfjsSharedGlobal.PDFJS;n.getDocument=t.pdfjsDisplayAPI.getDocument;n.PDFDataRangeTransport=t.pdfjsDisplayAPI.PDFDataRangeTransport;n.renderTextLayer=t.pdfjsDisplayTextLayer.renderTextLayer;n.AnnotationLayer=t.pdfjsDisplayAnnotationLayer.AnnotationLayer;n.CustomStyle=t.pdfjsDisplayDOMUtils.CustomStyle;n.PasswordResponses=t.pdfjsSharedUtil.PasswordResponses;n.InvalidPDFException=t.pdfjsSharedUtil.InvalidPDFException;n.MissingPDFException=t.pdfjsSharedUtil.MissingPDFException;n.UnexpectedResponseException=t.pdfjsSharedUtil.UnexpectedResponseException})