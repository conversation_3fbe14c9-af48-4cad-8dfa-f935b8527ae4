('C:\\Users\\<USER>\\Desktop\\ee\\pkulaw\\pkulaw\\build\\北大法宝爬虫\\北大法宝爬虫.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\ee\\pkulaw\\pkulaw\\build\\北大法宝爬虫\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\ee\\pkulaw\\pkulaw\\build\\北大法宝爬虫\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\ee\\pkulaw\\pkulaw\\build\\北大法宝爬虫\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\ee\\pkulaw\\pkulaw\\build\\北大法宝爬虫\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\ee\\pkulaw\\pkulaw\\build\\北大法宝爬虫\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\ee\\pkulaw\\pkulaw\\build\\北大法宝爬虫\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('北大法典爬虫GUI',
   'C:\\Users\\<USER>\\Desktop\\ee\\pkulaw\\pkulaw\\北大法典爬虫GUI.py',
   'PYSOURCE'),
  ('python310.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\python310.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'BINARY'),
  ('select.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\charset_normalizer\\md.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\etree.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\_elementpath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\sax.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\objectify.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\html\\diff.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\clean.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\html\\clean.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\builder.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\_cffi_backend.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\sip.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp310-win_amd64.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('win32\\win32process.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\win32\\win32process.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'D:\\ProgramData\\miniconda3\\envs\\spider\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('liblzma.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('libbz2.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Library\\bin\\libbz2.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('libexpat.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Library\\bin\\libexpat.dll',
   'BINARY'),
  ('ffi-8.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Library\\bin\\ffi-8.dll',
   'BINARY'),
  ('python3.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\python3.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes310.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\pywin32_system32\\pywintypes310.dll',
   'BINARY'),
  ('sqlite3.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\Library\\bin\\sqlite3.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\ucrtbase.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\INSTALLER',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography-45.0.4.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\RECORD',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography-45.0.4.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\METADATA',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography-45.0.4.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\WHEEL',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography-45.0.4.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('h2-3.2.0.dist-info\\LICENSE',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2-3.2.0.dist-info\\LICENSE',
   'DATA'),
  ('h2-3.2.0.dist-info\\top_level.txt',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2-3.2.0.dist-info\\top_level.txt',
   'DATA'),
  ('h2-3.2.0.dist-info\\METADATA',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2-3.2.0.dist-info\\METADATA',
   'DATA'),
  ('h2-3.2.0.dist-info\\WHEEL',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2-3.2.0.dist-info\\WHEEL',
   'DATA'),
  ('h2-3.2.0.dist-info\\INSTALLER',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2-3.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('h2-3.2.0.dist-info\\RECORD',
   'D:\\ProgramData\\miniconda3\\envs\\spider\\lib\\site-packages\\h2-3.2.0.dist-info\\RECORD',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\ee\\pkulaw\\pkulaw\\build\\北大法宝爬虫\\base_library.zip',
   'DATA')],
 'python310.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
