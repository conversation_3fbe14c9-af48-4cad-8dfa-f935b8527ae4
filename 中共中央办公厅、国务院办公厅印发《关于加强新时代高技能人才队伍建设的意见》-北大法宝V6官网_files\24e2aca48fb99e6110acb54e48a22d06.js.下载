window.__YSFWINTYPE__ = Number('1');
window.__YSFMODILEWINTYPE__ = Number('1');
window.__YSFTHEMELAYEROUT__ = 4;
window.__YSFBGCOLOR__ = "#9cb9ec";
window.__YSFBGTONE__ = "notone";
window.__YSFBGIMG__ = "";
window.__YSFSDKADR__ = "https://qiyukf.com";
window.__YSFDASWITCH__ = 0;
window.__YSFDAROOT__ = "https://da.qiyukf.com/webda/da.gif";
window.__YSFISGA__ = 0;
window.__YSFVISITORRECORD__ = 0;
window.__YSFISUPINFOTOSERVER__ = 0;
window.__YSFAPPPATH__ = "/sdk/";
window.MIN_LBS = 'https://lbs.chatnos.com/lbs/webconf.jsp?devflag=qyonline';
!function(){function PromisePolyfill(e){this.state="pending";this.value=void 0;this.handlers=[];var t=function(e){if("pending"===this.state){this.state="fulfilled";this.value=e;this.handlers.forEach(function(t){t.onFulfilled(e)})}}.bind(this);var o=function(e){if("pending"===this.state){this.state="rejected";this.value=e;this.handlers.forEach(function(t){t.onRejected(e)})}}.bind(this);try{e(t,o)}catch(i){o(i)}}function execEvent(e,t){var o=ysf[e];if(util.isFunction(o))if(inited)return o.apply(null,t);else switch(e){case"onready":case"onLayerload":case"onunread":case"config":case"product":case"logoff":case"setAuthToken":case"pollAuthToken":case"onConversation":case"onSessionMessage":case"getConversation":case"cardMessage":case"customMessage":callbacksBeforeLoad.push({func:o,arg:t});break;case"open":case"url":case"track":case"getUnreadMsg":util.consoleWarn("can not exec"+e+"before sdk ready")}}function sendData2box(){function e(e){var t,o;var i="";for(o=0;o<e.length;o++){t=e.charCodeAt(o).toString(16);i+="-"+t}return i}try{var t=document.createElement("iframe");t.src="https://ipservice.163.com/if/box";t.id="YSF-IFRAME-DATA";t.name=e(device()+"::"+util.getcookie("__kaola_usertrack"));t.style.width=0;t.style.height=0;t.style.border=0;t.style.display="none";t.style.outline="none";document.body.appendChild(t);setTimeout(function(){document.body.removeChild(t)},1e4)}catch(o){}}if(!window.localStorage||!window.postMessage)return"not support service";var local=window.setting?window.setting.langJson||{}:{};window.__YSF_EMOJI__={pmap:{emoticon_emoji_01:{tag:"[可爱]",file:"emoji_01.png"},emoticon_emoji_00:{tag:"[大笑]",file:"emoji_00.png"},emoticon_emoji_02:{tag:"[色]",file:"emoji_02.png"},emoticon_emoji_03:{tag:"[嘘]",file:"emoji_03.png"},emoticon_emoji_04:{tag:"[亲]",file:"emoji_04.png"},emoticon_emoji_05:{tag:"[呆]",file:"emoji_05.png"},emoticon_emoji_06:{tag:"[口水]",file:"emoji_06.png"},emoticon_emoji_145:{tag:"[汗]",file:"emoji_145.png"},emoticon_emoji_07:{tag:"[呲牙]",file:"emoji_07.png"},emoticon_emoji_08:{tag:"[鬼脸]",file:"emoji_08.png"},emoticon_emoji_09:{tag:"[害羞]",file:"emoji_09.png"},emoticon_emoji_10:{tag:"[偷笑]",file:"emoji_10.png"},emoticon_emoji_11:{tag:"[调皮]",file:"emoji_11.png"},emoticon_emoji_12:{tag:"[可怜]",file:"emoji_12.png"},emoticon_emoji_13:{tag:"[敲]",file:"emoji_13.png"},emoticon_emoji_14:{tag:"[惊讶]",file:"emoji_14.png"},emoticon_emoji_15:{tag:"[流感]",file:"emoji_15.png"},emoticon_emoji_16:{tag:"[委屈]",file:"emoji_16.png"},emoticon_emoji_17:{tag:"[流泪]",file:"emoji_17.png"},emoticon_emoji_18:{tag:"[嚎哭]",file:"emoji_18.png"},emoticon_emoji_19:{tag:"[惊恐]",file:"emoji_19.png"},emoticon_emoji_20:{tag:"[怒]",file:"emoji_20.png"},emoticon_emoji_21:{tag:"[酷]",file:"emoji_21.png"},emoticon_emoji_22:{tag:"[不说]",file:"emoji_22.png"},emoticon_emoji_23:{tag:"[鄙视]",file:"emoji_23.png"},emoticon_emoji_24:{tag:"[阿弥陀佛]",file:"emoji_24.png"},emoticon_emoji_25:{tag:"[奸笑]",file:"emoji_25.png"},emoticon_emoji_26:{tag:"[睡着]",file:"emoji_26.png"},emoticon_emoji_27:{tag:"[口罩]",file:"emoji_27.png"},emoticon_emoji_28:{tag:"[生气]",file:"emoji_28.png"},emoticon_emoji_29:{tag:"[抠鼻孔]",file:"emoji_29.png"},emoticon_emoji_30:{tag:"[疑问]",file:"emoji_30.png"},emoticon_emoji_31:{tag:"[怒骂]",file:"emoji_31.png"},emoticon_emoji_32:{tag:"[晕]",file:"emoji_32.png"},emoticon_emoji_33:{tag:"[呕吐]",file:"emoji_33.png"},emoticon_emoji_160:{tag:"[拜一拜]",file:"emoji_160.png"},emoticon_emoji_161:{tag:"[惊喜]",file:"emoji_161.png"},emoticon_emoji_162:{tag:"[流汗]",file:"emoji_162.png"},emoticon_emoji_163:{tag:"[卖萌]",file:"emoji_163.png"},emoticon_emoji_164:{tag:"[默契眨眼]",file:"emoji_164.png"},emoticon_emoji_165:{tag:"[烧香拜佛]",file:"emoji_165.png"},emoticon_emoji_166:{tag:"[晚安]",file:"emoji_166.png"},emoticon_emoji_34:{tag:"[强]",file:"emoji_34.png"},emoticon_emoji_35:{tag:"[弱]",file:"emoji_35.png"},emoticon_emoji_36:{tag:"[OK]",file:"emoji_36.png"},emoticon_emoji_37:{tag:"[拳头]",file:"emoji_37.png"},emoticon_emoji_38:{tag:"[胜利]",file:"emoji_38.png"},emoticon_emoji_39:{tag:"[鼓掌]",file:"emoji_39.png"},emoticon_emoji_200:{tag:"[握手]",file:"emoji_200.png"},emoticon_emoji_40:{tag:"[发怒]",file:"emoji_40.png"},emoticon_emoji_41:{tag:"[骷髅]",file:"emoji_41.png"},emoticon_emoji_42:{tag:"[便便]",file:"emoji_42.png"},emoticon_emoji_43:{tag:"[火]",file:"emoji_43.png"},emoticon_emoji_44:{tag:"[溜]",file:"emoji_44.png"},emoticon_emoji_45:{tag:"[爱心]",file:"emoji_45.png"},emoticon_emoji_46:{tag:"[心碎]",file:"emoji_46.png"},emoticon_emoji_47:{tag:"[钟情]",file:"emoji_47.png"},emoticon_emoji_48:{tag:"[唇]",file:"emoji_48.png"},emoticon_emoji_49:{tag:"[戒指]",file:"emoji_49.png"},emoticon_emoji_50:{tag:"[钻石]",file:"emoji_50.png"},emoticon_emoji_51:{tag:"[太阳]",file:"emoji_51.png"},emoticon_emoji_52:{tag:"[有时晴]",file:"emoji_52.png"},emoticon_emoji_53:{tag:"[多云]",file:"emoji_53.png"},emoticon_emoji_54:{tag:"[雷]",file:"emoji_54.png"},emoticon_emoji_55:{tag:"[雨]",file:"emoji_55.png"},emoticon_emoji_56:{tag:"[雪花]",file:"emoji_56.png"},emoticon_emoji_57:{tag:"[爱人]",file:"emoji_57.png"},emoticon_emoji_58:{tag:"[帽子]",file:"emoji_58.png"},emoticon_emoji_59:{tag:"[皇冠]",file:"emoji_59.png"},emoticon_emoji_60:{tag:"[篮球]",file:"emoji_60.png"},emoticon_emoji_61:{tag:"[足球]",file:"emoji_61.png"},emoticon_emoji_62:{tag:"[垒球]",file:"emoji_62.png"},emoticon_emoji_63:{tag:"[网球]",file:"emoji_63.png"},emoticon_emoji_64:{tag:"[台球]",file:"emoji_64.png"},emoticon_emoji_65:{tag:"[咖啡]",file:"emoji_65.png"},emoticon_emoji_66:{tag:"[啤酒]",file:"emoji_66.png"},emoticon_emoji_67:{tag:"[干杯]",file:"emoji_67.png"},emoticon_emoji_68:{tag:"[柠檬汁]",file:"emoji_68.png"},emoticon_emoji_69:{tag:"[餐具]",file:"emoji_69.png"},emoticon_emoji_70:{tag:"[汉堡]",file:"emoji_70.png"},emoticon_emoji_71:{tag:"[鸡腿]",file:"emoji_71.png"},emoticon_emoji_72:{tag:"[面条]",file:"emoji_72.png"},emoticon_emoji_73:{tag:"[冰淇淋]",file:"emoji_73.png"},emoticon_emoji_74:{tag:"[沙冰]",file:"emoji_74.png"},emoticon_emoji_75:{tag:"[生日蛋糕]",file:"emoji_75.png"},emoticon_emoji_76:{tag:"[蛋糕]",file:"emoji_76.png"},emoticon_emoji_77:{tag:"[糖果]",file:"emoji_77.png"},emoticon_emoji_78:{tag:"[葡萄]",file:"emoji_78.png"},emoticon_emoji_79:{tag:"[西瓜]",file:"emoji_79.png"},emoticon_emoji_80:{tag:"[光碟]",file:"emoji_80.png"},emoticon_emoji_81:{tag:"[手机]",file:"emoji_81.png"},emoticon_emoji_82:{tag:"[电话]",file:"emoji_82.png"},emoticon_emoji_83:{tag:"[电视]",file:"emoji_83.png"},emoticon_emoji_84:{tag:"[声音开启]",file:"emoji_84.png"},emoticon_emoji_85:{tag:"[声音关闭]",file:"emoji_85.png"},emoticon_emoji_86:{tag:"[铃铛]",file:"emoji_86.png"},emoticon_emoji_87:{tag:"[锁头]",file:"emoji_87.png"},emoticon_emoji_88:{tag:"[放大镜]",file:"emoji_88.png"},emoticon_emoji_89:{tag:"[灯泡]",file:"emoji_89.png"},emoticon_emoji_90:{tag:"[锤头]",file:"emoji_90.png"},emoticon_emoji_91:{tag:"[烟]",file:"emoji_91.png"},emoticon_emoji_92:{tag:"[炸弹]",file:"emoji_92.png"},emoticon_emoji_93:{tag:"[枪]",file:"emoji_93.png"},emoticon_emoji_94:{tag:"[刀]",file:"emoji_94.png"},emoticon_emoji_95:{tag:"[药]",file:"emoji_95.png"},emoticon_emoji_96:{tag:"[打针]",file:"emoji_96.png"},emoticon_emoji_97:{tag:"[钱袋]",file:"emoji_97.png"},emoticon_emoji_98:{tag:"[钞票]",file:"emoji_98.png"},emoticon_emoji_99:{tag:"[银行卡]",file:"emoji_99.png"},emoticon_emoji_100:{tag:"[手柄]",file:"emoji_100.png"},emoticon_emoji_101:{tag:"[麻将]",file:"emoji_101.png"},emoticon_emoji_102:{tag:"[调色板]",file:"emoji_102.png"},emoticon_emoji_103:{tag:"[电影]",file:"emoji_103.png"},emoticon_emoji_104:{tag:"[麦克风]",file:"emoji_104.png"},emoticon_emoji_105:{tag:"[耳机]",file:"emoji_105.png"},emoticon_emoji_106:{tag:"[音乐]",file:"emoji_106.png"},emoticon_emoji_107:{tag:"[吉他]",file:"emoji_107.png"},emoticon_emoji_108:{tag:"[火箭]",file:"emoji_108.png"},emoticon_emoji_109:{tag:"[飞机]",file:"emoji_109.png"},emoticon_emoji_110:{tag:"[火车]",file:"emoji_110.png"},emoticon_emoji_111:{tag:"[公交]",file:"emoji_111.png"},emoticon_emoji_112:{tag:"[轿车]",file:"emoji_112.png"},emoticon_emoji_113:{tag:"[出租车]",file:"emoji_113.png"},emoticon_emoji_114:{tag:"[警车]",file:"emoji_114.png"},emoticon_emoji_115:{tag:"[自行车]",file:"emoji_115.png"},emoticon_emoji_116:{tag:"[撇嘴]",file:"new_emoji_01.png"},emoticon_emoji_117:{tag:"[难过]",file:"1f641.png"},emoticon_emoji_118:{tag:"[冷汗]",file:"1f628.png"},emoticon_emoji_119:{tag:"[抓狂]",file:"1f629.png"},emoticon_emoji_120:{tag:"[傲慢]",file:"1f615.png"},emoticon_emoji_121:{tag:"[困]",file:"1f62a.png"},emoticon_emoji_122:{tag:"[疯了]",file:"1f616.png"},emoticon_emoji_123:{tag:"[奋斗]",file:"1f4aa-1f3fc.png"},emoticon_emoji_124:{tag:"[白眼]",file:"new_emoji_02.png"},emoticon_emoji_125:{tag:"[衰]",file:"new_emoji_05.png"},emoticon_emoji_126:{tag:"[再见]",file:"new_emoji_06.png"},emoticon_emoji_127:{tag:"[哼哼]",file:"1f63e.png"},emoticon_emoji_128:{tag:"[阴险]",file:"1f608.png"},emoticon_emoji_129:{tag:"[饥饿]",file:"new_emoji_03.png"},emoticon_emoji_130:{tag:"[乒乓]",file:"new_emoji_23.png"},emoticon_emoji_131:{tag:"[猪头]",file:"1f437.png"},emoticon_emoji_132:{tag:"[玫瑰]",file:"new_emoji_25.png"},emoticon_emoji_133:{tag:"[凋谢]",file:"new_emoji_24.png"},emoticon_emoji_134:{tag:"[瓢虫]",file:"1f41e.png"},emoticon_emoji_135:{tag:"[月亮]",file:"new_emoji_30.png"},emoticon_emoji_136:{tag:"[礼物]",file:"new_emoji_28.png"},emoticon_emoji_137:{tag:"[拥抱]",file:"new_emoji_08.png"},emoticon_emoji_138:{tag:"[抱拳]",file:"new_emoji_18.png"},emoticon_emoji_139:{tag:"[勾引]",file:"new_emoji_19.png"},emoticon_emoji_140:{tag:"[差劲]",file:"new_emoji_20.png"},emoticon_emoji_141:{tag:"[爱你]",file:"new_emoji_21.png"},emoticon_emoji_142:{tag:"[NO]",file:"new_emoji_22.png"},emoticon_emoji_143:{tag:"[飞吻]",file:"new_emoji_10.png"},emoticon_emoji_144:{tag:"[微笑]",file:"1f642.png"},emoticon_emoji_146:{tag:"[吐舌头]",file:"1f61d.png"},emoticon_emoji_147:{tag:"[忧郁]",file:"new_emoji_11.png"},emoticon_emoji_148:{tag:"[尴尬]",file:"1f630.png"},emoticon_emoji_149:{tag:"[舒适]",file:"1f60c.png"},emoticon_emoji_150:{tag:"[不悦]",file:"1f612.png"},emoticon_emoji_151:{tag:"[幽灵]",file:"1f47b.png"},emoticon_emoji_152:{tag:"[礼盒]",file:"1f49d.png"},emoticon_emoji_153:{tag:"[拜托]",file:"1f64f-1f3fc.png"},emoticon_emoji_154:{tag:"[气球]",file:"1f388.png"},emoticon_emoji_155:{tag:"[拍掌]", file:"new_emoji_09.png"},emoticon_emoji_156:{tag:"[泪中带笑]",file:"1f602.png"},emoticon_emoji_157:{tag:"[糗大了]",file:"new_emoji_07.png"}},pmap2:{"[大笑]":"emoticon_emoji_00","[可爱]":"emoticon_emoji_01","[色]":"emoticon_emoji_02","[嘘]":"emoticon_emoji_03","[亲]":"emoticon_emoji_04","[呆]":"emoticon_emoji_05","[口水]":"emoticon_emoji_06","[汗]":"emoticon_emoji_145","[呲牙]":"emoticon_emoji_07","[鬼脸]":"emoticon_emoji_08","[害羞]":"emoticon_emoji_09","[偷笑]":"emoticon_emoji_10","[调皮]":"emoticon_emoji_11","[可怜]":"emoticon_emoji_12","[敲]":"emoticon_emoji_13","[惊讶]":"emoticon_emoji_14","[流感]":"emoticon_emoji_15","[委屈]":"emoticon_emoji_16","[流泪]":"emoticon_emoji_17","[嚎哭]":"emoticon_emoji_18","[惊恐]":"emoticon_emoji_19","[怒]":"emoticon_emoji_20","[酷]":"emoticon_emoji_21","[不说]":"emoticon_emoji_22","[鄙视]":"emoticon_emoji_23","[阿弥陀佛]":"emoticon_emoji_24","[奸笑]":"emoticon_emoji_25","[睡着]":"emoticon_emoji_26","[口罩]":"emoticon_emoji_27","[生气]":"emoticon_emoji_28","[抠鼻孔]":"emoticon_emoji_29","[疑问]":"emoticon_emoji_30","[怒骂]":"emoticon_emoji_31","[晕]":"emoticon_emoji_32","[呕吐]":"emoticon_emoji_33","[拜一拜]":"emoticon_emoji_160","[惊喜]":"emoticon_emoji_161","[流汗]":"emoticon_emoji_162","[卖萌]":"emoticon_emoji_163","[默契眨眼]":"emoticon_emoji_164","[烧香拜佛]":"emoticon_emoji_165","[晚安]":"emoticon_emoji_166","[强]":"emoticon_emoji_34","[弱]":"emoticon_emoji_35","[OK]":"emoticon_emoji_36","[拳头]":"emoticon_emoji_37","[胜利]":"emoticon_emoji_38","[鼓掌]":"emoticon_emoji_39","[握手]":"emoticon_emoji_200","[发怒]":"emoticon_emoji_40","[骷髅]":"emoticon_emoji_41","[便便]":"emoticon_emoji_42","[火]":"emoticon_emoji_43","[溜]":"emoticon_emoji_44","[爱心]":"emoticon_emoji_45","[心碎]":"emoticon_emoji_46","[钟情]":"emoticon_emoji_47","[唇]":"emoticon_emoji_48","[戒指]":"emoticon_emoji_49","[钻石]":"emoticon_emoji_50","[太阳]":"emoticon_emoji_51","[有时晴]":"emoticon_emoji_52","[多云]":"emoticon_emoji_53","[雷]":"emoticon_emoji_54","[雨]":"emoticon_emoji_55","[雪花]":"emoticon_emoji_56","[爱人]":"emoticon_emoji_57","[帽子]":"emoticon_emoji_58","[皇冠]":"emoticon_emoji_59","[篮球]":"emoticon_emoji_60","[足球]":"emoticon_emoji_61","[垒球]":"emoticon_emoji_62","[网球]":"emoticon_emoji_63","[台球]":"emoticon_emoji_64","[咖啡]":"emoticon_emoji_65","[啤酒]":"emoticon_emoji_66","[干杯]":"emoticon_emoji_67","[柠檬汁]":"emoticon_emoji_68","[餐具]":"emoticon_emoji_69","[汉堡]":"emoticon_emoji_70","[鸡腿]":"emoticon_emoji_71","[面条]":"emoticon_emoji_72","[冰淇淋]":"emoticon_emoji_73","[沙冰]":"emoticon_emoji_74","[生日蛋糕]":"emoticon_emoji_75","[蛋糕]":"emoticon_emoji_76","[糖果]":"emoticon_emoji_77","[葡萄]":"emoticon_emoji_78","[西瓜]":"emoticon_emoji_79","[光碟]":"emoticon_emoji_80","[手机]":"emoticon_emoji_81","[电话]":"emoticon_emoji_82","[电视]":"emoticon_emoji_83","[声音开启]":"emoticon_emoji_84","[声音关闭]":"emoticon_emoji_85","[铃铛]":"emoticon_emoji_86","[锁头]":"emoticon_emoji_87","[放大镜]":"emoticon_emoji_88","[灯泡]":"emoticon_emoji_89","[锤头]":"emoticon_emoji_90","[烟]":"emoticon_emoji_91","[炸弹]":"emoticon_emoji_92","[枪]":"emoticon_emoji_93","[刀]":"emoticon_emoji_94","[药]":"emoticon_emoji_95","[打针]":"emoticon_emoji_96","[钱袋]":"emoticon_emoji_97","[钞票]":"emoticon_emoji_98","[银行卡]":"emoticon_emoji_99","[手柄]":"emoticon_emoji_100","[麻将]":"emoticon_emoji_101","[调色板]":"emoticon_emoji_102","[电影]":"emoticon_emoji_103","[麦克风]":"emoticon_emoji_104","[耳机]":"emoticon_emoji_105","[音乐]":"emoticon_emoji_106","[吉他]":"emoticon_emoji_107","[火箭]":"emoticon_emoji_108","[飞机]":"emoticon_emoji_109","[火车]":"emoticon_emoji_110","[公交]":"emoticon_emoji_111","[轿车]":"emoticon_emoji_112","[出租车]":"emoticon_emoji_113","[警车]":"emoticon_emoji_114","[自行车]":"emoticon_emoji_115","[撇嘴]":"emoticon_emoji_116","[难过]":"emoticon_emoji_117","[冷汗]":"emoticon_emoji_118","[抓狂]":"emoticon_emoji_119","[傲慢]":"emoticon_emoji_120","[困]":"emoticon_emoji_121","[疯了]":"emoticon_emoji_122","[奋斗]":"emoticon_emoji_123","[白眼]":"emoticon_emoji_124","[衰]":"emoticon_emoji_125","[再见]":"emoticon_emoji_126","[哼哼]":"emoticon_emoji_127","[阴险]":"emoticon_emoji_128","[饥饿]":"emoticon_emoji_129","[乒乓]":"emoticon_emoji_130","[猪头]":"emoticon_emoji_131","[玫瑰]":"emoticon_emoji_132","[凋谢]":"emoticon_emoji_133","[瓢虫]":"emoticon_emoji_134","[月亮]":"emoticon_emoji_135","[礼物]":"emoticon_emoji_136","[拥抱]":"emoticon_emoji_137","[抱拳]":"emoticon_emoji_138","[勾引]":"emoticon_emoji_139","[差劲]":"emoticon_emoji_140","[爱你]":"emoticon_emoji_141","[NO]":"emoticon_emoji_142","[飞吻]":"emoticon_emoji_143","[微笑]":"emoticon_emoji_144","[吐舌头]":"emoticon_emoji_146","[忧郁]":"emoticon_emoji_147","[尴尬]":"emoticon_emoji_148","[舒适]":"emoticon_emoji_149","[不悦]":"emoticon_emoji_150","[幽灵]":"emoticon_emoji_151","[礼盒]":"emoticon_emoji_152","[拜托]":"emoticon_emoji_153","[气球]":"emoticon_emoji_154","[拍掌]":"emoticon_emoji_155","[泪中带笑]":"emoticon_emoji_156","[糗大了]":"emoticon_emoji_157"}};PromisePolyfill.prototype.then=function(e,t){var o=this;return new PromisePolyfill(function(i,n){if("fulfilled"===o.state)setTimeout(function(){try{if("function"==typeof e)i(e(o.value));else i(o.value)}catch(t){n(t)}},0);else if("rejected"===o.state)setTimeout(function(){try{if("function"==typeof t)i(t(o.value));else n(o.value)}catch(e){n(e)}},0);else o.handlers.push({onFulfilled:function(t){try{if("function"==typeof e)i(e(t));else i(t)}catch(o){n(o)}},onRejected:function(e){try{if("function"==typeof t)i(t(e));else n(e)}catch(o){n(o)}}})})};PromisePolyfill.prototype["catch"]=function(e){return this.then(null,e)};var seqApi={seq:1,task:{},getSeq:function(){return seqApi.seq++},addTask:function(e,t,o){o=o||5e3;var i=seqApi.getSeq();seqApi.task[i]={resolve:e,reject:t};setTimeout(function(){if(seqApi.task[i]){seqApi.task[i].reject({code:-1,msg:"请求超时"});delete seqApi.task[i]}},o);return i},fireSeqCallback:function(e){var t=e.seq;if(seqApi.task[t]){if(200===e.code)seqApi.task[t].resolve(e);else seqApi.task[t].reject(e);delete seqApi.task[t]}}};var util={isFrameModule:function(){if(util.isMobilePlatform()&&2===window.__YSFMODILEWINTYPE__)return"mobileFrame";else if(!util.isMobilePlatform()&&1===window.__YSFWINTYPE__)return"webFrame";else!1},isMobilePlatform:function(){if(/(iPhone|iPad|iPod|iOS|Android|OpenHarmony)/i.test(navigator.userAgent))return!0;else return!1},isIOSorSafari:function(){if(/(iPhone|iPad|iOS|mini)/i.test(navigator.userAgent)||(navigator.userAgent.indexOf("Safari")>-1||~navigator.userAgent.indexOf("AppleWebKit"))&&navigator.userAgent.indexOf("Chrome")==-1)return!0;else return!1},isFirefox:function(){if(navigator.userAgent.indexOf("Firefox")!=-1)return!0;else return!1},getcookie:function(e){var t=document.cookie,o="\\b"+e+"=",i=t.search(o);if(i<0)return"";i+=o.length-2;var n=t.indexOf(";",i);if(n<0)n=t.length;return t.substring(i,n)||""},createAjax:function(){var e=null;var t=["Msxml2.XMLHTTP.6.0","Msxml2.XMLHTTP.3.0","Msxml2.XMLHTTP.4.0","Msxml2.XMLHTTP.5.0","MSXML2.XMLHTTP","Microsoft.XMLHTTP"];if(window.XMLHttpRequest){e=new XMLHttpRequest;if("withCredentials"in e)return e}if(window.xDomainRequest)e=new Window.xDomainRequest;return e},mergeParams:function(e){var t=[];for(var o in e)if(e.hasOwnProperty(o))t.push(encodeURIComponent(o)+"="+encodeURIComponent(e[o]));return t.join("&")},ajax:function(conf){var method=conf.method||"get",contentType=conf.contentType,url=conf.url,data=conf.data,result={},success=conf.success,error=conf.error,fullResult=conf.fullResult||!1;var xhr=util.createAjax();if(xhr){try{if("GET"===method.toUpperCase())if(data)url=url+"?"+util.mergeParams(data);if(conf.synchronous)xhr.open(method,url,!1);else xhr.open(method,url)}catch(ex){console.error(ex);error(ex);return}xhr.onreadystatechange=function(){if(4==xhr.readyState)if(200===xhr.status){try{result=eval("("+xhr.responseText+")")}catch(err){error(err);return}if(200==(result&&result.code))success(fullResult?result:result.result);else error(result)}else error()};if("GET"==method.toUpperCase())xhr.send(null);else if("json"==contentType){xhr.setRequestHeader("content-type","application/json");xhr.send(JSON.stringify(data))}else{xhr.setRequestHeader("content-type","application/x-www-form-urlencoded");xhr.send(util.mergeParams(data))}}else error("NOT SUPPORT XHR")},findLocalItems:function(e,t){var o,i=[],n;for(o in localStorage)if(o.match(e)||!e&&"string"==typeof o){n=!t?localStorage.getItem(o):JSON.parse(localStorage.getItem(o));i.push({key:o,val:n})}return i},clearLocalItems:function(e){for(var t=0;t<e.length;t++)window.localStorage.removeItem(e[t].key)},addEvent:function(e,t,o){if(e.addEventListener)e.addEventListener(t,o,!1);else if(e.attachEvent)e.attachEvent("on"+t,o)},isIe:function(){if(window.ActiveXObject||"ActiveXObject"in window)return!0;else return!1},isEdge:function(){return navigator.userAgent.indexOf("Edge")>-1},addLoadEventForProxy:function(){function e(){for(var e=o.length-1;e>=0;e--)o[e]()}var t=!1;var o=[];return function(i){o.push(i);if(!t){if(proxy.addEventListener)proxy.addEventListener("load",e,!1);else if(proxy.attachEvent)proxy.attachEvent("onload",e);t=!0}}}(),mergeUrl:function(e,t){var o=e.split("?"),i=o.shift(),n=util.query2Object(o.shift()||"","&");for(var a in t)n[a]=t[a];return i+"?"+serialize(n)},query2Object:function(e,t){var o=e.split(t),i={};for(var n=0;n<o.length;n++){var a=o[n],c=(a||"").split("="),r=c.shift();if(r)i[decodeURIComponent(r)]=decodeURIComponent(c.join("="));else;}return i},isObject:function(e){return"[object object]"==={}.toString.call(e).toLowerCase()},isFunction:function(e){return"[object function]"==={}.toString.call(e).toLowerCase()||"[object asyncfunction]"==={}.toString.call(e).toLowerCase()},isArray:function(e){return"[object array]"==={}.toString.call(e).toLowerCase()},notification:function(){var e,t;return function(o){if(e){clearTimeout(t);e.close()}if(window.Notification&&"granted"!==window.Notification.permission)Notification.requestPermission();if(window.Notification&&"denied"!=window.Notification.permission){e=new Notification(o.notify,{tag:o.tag,body:o.body,icon:o.icon.indexOf("http")>-1?o.icon:window.__YSFSDKADR__+o.icon});util.playAudio();e.onclick=function(){e&&e.close();window.focus();ysf.openLayer();ysf.NotifyMsgAndBubble({category:"clearCircle"})};t=window.setTimeout(function(){ e.close()},2e4)}}}(),playAudio:function(){if(window.__YSFSDKADR__){var e=document.createElement("audio");e.src="//ysf.nosdn.127.net/webapi/38a7410cdf36ef88290e0136e0f86998";return function(){e.play()}}}(),encode:function(e,t){t=""+t;if(!e||!t)return t||"";else return t.replace(e.r,function(t){var o=e[!e.i?t.toLowerCase():t];return null!=o?o:t})},escape:function(){var e=/<br\/?>$/,t={r:/\<|\>|\&|\r|\n|\s|\'|\"/g,"<":"&lt;",">":"&gt;","&":"&amp;"," ":"&nbsp;",'"':"&quot;","'":"&#39;","\n":"<br/>","\r":""};return function(o){o=util.encode(t,o);return o.replace(e,"<br/><br/>")}}(),unescape:function(){var e={r:/\&(?:lt|gt|amp|nbsp|#39|quot)\;|\<br\/\>/gi,"&lt;":"<","&gt;":">","&amp;":"&","&nbsp;":" ","&#39;":"'","&quot;":'"',"<br/>":"\n"};return function(t){return util.encode(e,t)}}(),replaceRichAndUnescape:function(e){var t=/<img[^>]+>/g;e=e.replace(t,"["+(local.IMAGE||"图片")+"]");var o=/<\/?[^>]*>/g;e=e.replace(o,"");return util.unescape(e)},consoleError:function(e){window.console&&window.console.error(e)},consoleWarn:function(e){window.console&&window.console.warn(e)},getToday:function(){var e=new Date;return e.getFullYear()+"-"+e.getMonth()+"-"+e.getDate()},Base64:function(){_keyStr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";this.encode=function(e){var t="";var o,i,n,a,c,r,s;var l=0;e=_utf8_encode(e);for(;l<e.length;){o=e.charCodeAt(l++);i=e.charCodeAt(l++);n=e.charCodeAt(l++);a=o>>2;c=(3&o)<<4|i>>4;r=(15&i)<<2|n>>6;s=63&n;if(isNaN(i))r=s=64;else if(isNaN(n))s=64;t=t+_keyStr.charAt(a)+_keyStr.charAt(c)+_keyStr.charAt(r)+_keyStr.charAt(s)}return t};this.decode=function(e){var t="";var o,i,n;var a,c,r,s;var l=0;e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");for(;l<e.length;){a=_keyStr.indexOf(e.charAt(l++));c=_keyStr.indexOf(e.charAt(l++));r=_keyStr.indexOf(e.charAt(l++));s=_keyStr.indexOf(e.charAt(l++));o=a<<2|c>>4;i=(15&c)<<4|r>>2;n=(3&r)<<6|s;t+=String.fromCharCode(o);if(64!=r)t+=String.fromCharCode(i);if(64!=s)t+=String.fromCharCode(n)}t=_utf8_decode(t);return t};_utf8_encode=function(e){e=e.replace(/\r\n/g,"\n");var t="";for(var o=0;o<e.length;o++){var i=e.charCodeAt(o);if(i<128)t+=String.fromCharCode(i);else if(i>127&&i<2048){t+=String.fromCharCode(i>>6|192);t+=String.fromCharCode(63&i|128)}else{t+=String.fromCharCode(i>>12|224);t+=String.fromCharCode(i>>6&63|128);t+=String.fromCharCode(63&i|128)}}return t};_utf8_decode=function(e){var t="";var o=0;var i=c1=c2=0;for(;o<e.length;){i=e.charCodeAt(o);if(i<128){t+=String.fromCharCode(i);o++}else if(i>191&&i<224){c2=e.charCodeAt(o+1);t+=String.fromCharCode((31&i)<<6|63&c2);o+=2}else{c2=e.charCodeAt(o+1);c3=e.charCodeAt(o+2);t+=String.fromCharCode((15&i)<<12|(63&c2)<<6|63&c3);o+=3}}return t}},throttleDebounce:function(e,t,o){var i,n,a,c;var r=null;var s=null;var l=0;var m=Date.now||function(){return(new Date).getTime()};if(!o)o={};var f=function(){l=o.leading===!1?0:m();r=null;a=e.apply(i,n);if(!r)i=n=null};var d=function(){var o=m()-c;if(o<t&&o>=0)s=setTimeout(d,t-o);else{s=null;a=e.apply(i,n);if(!s)i=n=null}};return function(){var d=m();c=d;if(!l&&o.leading===!1)l=d;var u=t-(d-l);i=this;n=arguments;if(u<=0||u>t){if(r){clearTimeout(r);r=null}l=d;a=e.apply(i,n);if(!r)i=n=null}else{if(!r&&o.trailing!==!1)r=setTimeout(f,u);if(!s)s=setTimeout(f,t)}return a}},loadJS:function(e,t){var o=document.createElement("script"),i=t||function(){};o.type="text/javascript";o.async=!1;if(o.readyState)o.onreadystatechange=function(){if("loaded"==o.readyState||"complete"==o.readyState){o.onreadystatechange=null;i()}};else o.onload=function(){i()};o.src=e;document.getElementsByTagName("head")[0].appendChild(o)},includes:function(e,t){return String(e).indexOf(t)>-1}};var corpStatus=!0;var winParam={};var cache={};var configKeys=[];var proxy;var chatProxy;var layerCnt;var chars="0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz";var firstBtnClick=!0;var hasConnected=!1;var connectTimer;var doConnectInterval=5e3;var CircleNumberFlag=0;var msgSessionIds=[];var getUnreadTimer;var unreadPollingInterval=5e3;var inited=!1;var callbacksBeforeLoad=[];var cachedArgs=[];var dvcswitched=!1;var callbacksBeforeLoadIsRun=!1;var initChatProxyBuild=!1;var chatProxySendConnect=!1;var maxTemplateId;var bodyscrollTop=0;if(window.ysf&&window.ysf.a)cachedArgs=window.ysf.a;if(window.ysf)var RESROOT=window.ysf.RESROOT;window.ysf=function(){try{var e=arguments[0],t=Array.prototype.slice.call(arguments,1);return execEvent(e,t)}catch(o){}};ysf.ROOT=window.__YSFSDKADR__||"";ysf.RESROOT=RESROOT;var each=function(e,t){if(e&&t)for(var o in e)if(e.hasOwnProperty(o))t.call(null,o,e[o])};var rand=function(e){if(e)return"ysf-"+e;var t=[];for(var o=0,i;o<20;++o){i=Math.floor(Math.random()*chars.length);t.push(chars.charAt(i))}return t.join("").toLowerCase()};var initPageId=function(e){e=e||10;var t=[];for(var o=0,i;o<e;++o){i=Math.floor(Math.random()*chars.length);t.push(chars.charAt(i))}return(new Date).getTime()+t.join("")};var migrate=function(){var e;if(/YSF_UID\s*=\s*(.*?)(?=;|$)/i.test(document.cookie))e=RegExp.$1;if(e)localStorage.setItem("YSF_UID",e);var e;if(/YSF_LAST\s*=\s*(.*?)(?=;|$)/i.test(document.cookie))e=RegExp.$1;if(e)localStorage.setItem("YSF_LAST",e);var t=new Date(1990,11,30).toGMTString();document.cookie="YSF_UID=;path=/;expires="+t;document.cookie="YSF_LAST=;path=/;expires="+t};var cmap={ack:function(e){cache.timestamp=parseInt(e,10);if(cache.onackdone){cache.onackdone();delete cache.onackdone}},rdy:function(e){setTimeout(function(){syncProfile({local:1})},100)}};var wrap=function(){var e=document.createElement("div"),t=e.style,o={top:0,left:0,visibility:"hidden",position:"absolute",width:"1px",height:"1px"};each(o,function(e,o){t[e]=o});document.body.appendChild(e);return e};var merge=function(e){each(e,function(e,t){cache[e]=t})};var refresh=function(e){e=e||"";var t=device(),o=lastUID(),i=getUuid();if(!t||""==e&&""!=o){t=e||t||rand(e);sendMsg("synckey:"+t)}cache.device=t;cache.uuid=i||rand();localStorage.setItem("YSF-"+cache["appKey"].toUpperCase()+"-UID",e||t);localStorage.setItem("YSF-"+cache["appKey"].toUpperCase()+"-LAST",e||"");localStorage.setItem("YSF-"+cache["appKey"].toUpperCase()+"-UUID",cache.uuid)};var serialize=function(e,t){var o=[];each(e,function(e,i){if(t&&void 0==i);else o.push(encodeURIComponent(e)+"="+encodeURIComponent(i))});return o.join("&")};var device=function(){return localStorage.getItem("YSF-"+cache["appKey"].toUpperCase()+"-UID")||""};var getUuid=function(){return localStorage.getItem("YSF-"+cache["appKey"].toUpperCase()+"-UUID")};var lastUID=function(){return localStorage.getItem("YSF-"+cache["appKey"].toUpperCase()+"-LAST")||""};var updateDevice=function(){cache.device=rand();localStorage.setItem("YSF-"+cache["appKey"].toUpperCase()+"-UID",cache.device);sendMsg("synckey:"+cache.device)};var sendChatMsg=function(e,t){chatProxy.contentWindow.postMessage(""+e+":"+JSON.stringify(t),"*")};var sendChatMsgNew=function(e){return new PromisePolyfill(function(t,o){var i=seqApi.addTask(t,o);e.seq=i;if(chatProxy)chatProxy.contentWindow.postMessage(e,"*");if(window.windowurl)window.windowurl.postMessage(e,"*")})};var visit=function(){if(cache.appKey){var e=new Image,t=serialize({uri:location.href,title:document.title,appkey:cache.appKey});e.src=ysf.DOMAIN+"webapi/user/accesshistory.action?"+t}};var syncProfile=function(e){sendMsg("KEY:"+cache.appKey||"");var t={title:document.title||""};var o=function(e,t){var o=!1;e.forEach(function(e){if(e.key==t)o=!0});return o};each({uid:"",reallyUid:"",name:"",email:"",mobile:"",avatar:"",profile:"data",bid:"",level:"",authToken:""},function(e,o){var i=cache[o]||cache[e];if(i)t[e]=i});each({avatar:local.AVATAR||"头像"},function(e,i){try{if(!t[e])return;var n=JSON.parse(t["profile"]||"[]"),a=n.length;if(!o(n,e)){n.push({key:e,value:t[e],index:a,label:i});t["profile"]=JSON.stringify(n)}}catch(c){util.consoleError("parse profile error: [crm]"+e,c)}});t.referrer=cache.referrer||location.href;t.title=cache.title||"";t.landPage=localStorage.getItem("DA-LANDPAGE")||"";t.landPageTitle=localStorage.getItem("DA-LANDPAGE-TITLE")||"";t.landPageReferrer=localStorage.getItem("DA-LANDPAGE-REFERRER")||"";t.sessionInfo=cache.sessionInfo||"";t.deviceId=cache.uid||cache.device;t.channelExtendInfo=cache.channelExtendInfo||"";sendMsg("USR:"+serialize(t));var i=navigator.userAgent;if(e&&e.bid||cache.bid)childMerchantsIsOpen(e&&e.bid||cache.bid,syncProfileFun.bind(this,e,t));else syncProfileFun(e,t)};var syncProfileFun=function(e,t){if(e.upToServer&&(util.isIOSorSafari()||util.isFirefox()||!util.isFrameModule())||window.__YSFISUPINFOTOSERVER__){var o=[{key:"userInfo",value:JSON.stringify(t)}];if(1===e.local)setTimeout(function(){if(!cache.uid)reportInfo(o,e.success,e.error,e.local)},200);else reportInfo(o,e.success,e.error,e.local)}else{var o=[{key:"userInfo",value:JSON.stringify(t)}];if(e&&e.manualRequired)reportInfo(o,e.success,e.error,e.local);if(util.isFunction(e.success))e.success()}};var syncCustomProfile=function(e){sendMsg("PRODUCT:"+serialize(e.data,!0));if(util.isIOSorSafari()||window.__YSFISUPINFOTOSERVER__||util.isFirefox()||!util.isFrameModule())reportInfo([{key:"orderInfo",value:JSON.stringify(e.data)}],e.success,e.error);else if(util.isFunction(e.success))e.success()};var syncCardInfoProfile=function(e){sendMsg(JSON.stringify({data:e.data,appkey:cache.appKey,type:"cardMessage"}));if(util.isIOSorSafari()||window.__YSFISUPINFOTOSERVER__||util.isFirefox()||!util.isFrameModule())reportInfo([{key:"cardInfo",value:JSON.stringify(e.data)}],e.success,e.error);else if(util.isFunction(e.success))e.success()};var syncCustomMessageProfile=function(e){sendMsg(JSON.stringify({data:e.data,appkey:cache.appKey,type:"customMessage"}));if(util.isIOSorSafari()||window.__YSFISUPINFOTOSERVER__||util.isFirefox()||!util.isFrameModule())reportInfo([{key:"customMessage",value:JSON.stringify(e.data)}],e.success,e.error);else if(util.isFunction(e.success))e.success()};var syncWebAnalytics=function(){if(window.__YSFDASWITCH__){var e={ak:cache.appKey, dv:device(),si:"",su:encodeURIComponent(document.referrer),cup:encodeURIComponent(location.href),cy:"",lp:localStorage.getItem("DA-LANDPAGE")||"",tm:(new Date).getTime()};sendMsg("WEBANALYTICS:"+serialize(e));if(util.isIOSorSafari()||window.__YSFISUPINFOTOSERVER__||util.isFirefox()||!util.isFrameModule())reportInfo([{key:"analyticInfo",value:JSON.stringify(e)}])}};var sendMsg=function(e){try{proxy.contentWindow.postMessage(e,"*")}catch(t){util.consoleError("proxy.contentWindow.postMessage---error")}};var canSendMsg=function(){if(proxy.contentWindow&&proxy.contentWindow.postMessage)return!0;else return!1};var msgNotifyLock=function(){var e=null;return function(e,t){setTimeout(function(){var o=("YSFMSG-"+cache["appKey"]+"-"+e.id).toUpperCase();if(null==window.localStorage.getItem(o)){window.localStorage.setItem(o,1);t(!0)}t(!1)},100*cache["dvcTimer"])}}();var receiveMsg=function(e){if(e.origin==ysf.ROOT||""==ysf.ROOT){var t=[];if("string"==typeof e.data)t=e.data.split(":");var o=t.shift();if("pkg"!=o){var i=cmap[(o||"").toLowerCase()];if(i)i(t.join(":"));if("object"==typeof e.data)if(e.data.seq)seqApi.fireSeqCallback(e.data)}else receivePkg(JSON.parse(t.join(":")))}};var receivePkg=function(e){var t={notify:function(e){var t="YSF-"+device()+"-MSGNUMBERS";msgNotifyLock(e,function(o){var i=Number(window.localStorage.getItem(t)||0),n=o?++i:i;cache["notifyContent"]=e;cache["notifyNumber"]=n;if(o)ysf._unread(ysf.getUnreadMsg());ysf.NotifyMsgAndBubble({category:"notifyCircle",data:{circleNum:n,notifyCnt:e.content,type:e.type}})})},winfocus:function(e){util.notification(e)},closeIframe:function(e){var t=document.getElementById("YSF-PANEL-CORPINFO")||document.getElementById("YSF-PANEL-INFO")||document.getElementById("YSF-PANEL-MOBILE"),o=document.getElementById("YSF-BTN-HOLDER"),i=document.getElementById("YSF-IFRAME-LAYER");t.className="ysf-chat-layer";ysf.checkOpenLayerSize(t,!0);if(util.isMobilePlatform()&&i)i.className="";if(util.isMobilePlatform()&&t){document.body.classList.remove("YSF-PANEL-BODY-FIXED");document.body.scrollTop=bodyscrollTop}t.setAttribute("data-switch",0);if(!util.isMobilePlatform()&&t&&ysf.inviteLayer.isInviteLayerShow())ysf.inviteLayer.resetLayer();if(e.closeType){1==cache["dvcswitch"];firstBtnClick=!0}try{sendChatMsg("status",{layerOpen:0})}catch(n){}if(0==cache["hidden"])o.style.display="block"},toggleLabel:function(){cache.winType=3;initWinConfig();syncProfile({manualRequired:!0});ysf.open(maxTemplateId);receivePkg({category:"closeIframe"});cache.winType=1;initWinConfig()},leaveOk:function(e){delete ysf.openInline.loadStatus;if(util.resetTimer)clearTimeout(util.resetTimer);util.resetTimer=setTimeout(function(){reset()},1e3);var t=document.getElementById("YSF-PANEL-CORPINFO")||document.getElementById("YSF-PANEL-INFO")||document.getElementById("YSF-PANEL-MOBILE");if(util.isMobilePlatform()&&t){document.body.classList.remove("YSF-PANEL-BODY-FIXED");document.body.scrollTop=bodyscrollTop}},pushMsg:function(e){if(e.data.sdkAppend){CircleNumberFlag+=1;msgSessionIds.push(e.data.msgSessionId);ysf.NotifyMsgAndBubble({category:"notifyCircle",data:{circleNum:CircleNumberFlag,notifyCnt:e.data.content,type:"text"}})}},inputblur:function(e){if(util.isMobilePlatform()){document.body.scrollIntoView(!1);var t=document.documentElement.scrollTop||document.body.scrollTop;window.scrollTo(0,Math.max(t-1))}},inputonfocus:function(e){var t=navigator.userAgent.toLowerCase();var o=t.match(/cpu iphone os (.*?) like mac os/);if(o)if(11==parseInt(o[1].replace(/_/g,".")))return;document.body.scrollTop=document.body.scrollHeight+100},chatProxyBuild:function(e){initChatProxyBuild=!0;if("loaded"==ysf.openInline.loadStatus&&!chatProxySendConnect&&(util.isEdge()||util.isIe())){chatProxySendConnect=!0;var t=layerCnt&&layerCnt.getAttribute("data-switch");if("1"==t||1==cache["dvcswitch"])sendChatMsg("doconnect",{doconnect:1})}},inviteModalClick:function(){if(util.isMobilePlatform()){var e="ysf-chat-mobile-invite-layer";var t=document.querySelector("."+e);if(t){t.classList.remove(e);ysf.inviteLayer.inviteLayerHide()}}}};var o=t[e.category];if(o)o(e)};var reset=function(){var e=document.getElementById("YSF-PANEL-CORPINFO")||document.getElementById("YSF-PANEL-INFO")||document.getElementById("YSF-PANEL-MOBILE"),t=document.getElementById("YSF-BTN-HOLDER");document.body.removeChild(e);document.body.removeChild(t);ysf.init(cache["imgSrc"]);firstBtnClick=!0};var buildProxy=function(){if(!proxy){if(window.addEventListener)window.addEventListener("message",receiveMsg,!1);else window.attachEvent("onmessage",receiveMsg);proxy=wrap();proxy.innerHTML='<iframe style="height:0px; width:0px;" src="'+ysf.RESROOT+"res/delegate.html?"+ +new Date+'"></iframe>';proxy=proxy.getElementsByTagName("IFRAME")[0];proxy.domain=document.domain;util.addLoadEventForProxy(function(){inited=!0;syncWebAnalytics();if(!util.isMobilePlatform());ysf.analytics(window.__YSFDASWITCH__);if(util.isFrameModule()){if(dvcswitched&&!callbacksBeforeLoadIsRun){callbacksBeforeLoadIsRun=!0;for(var e=0;e<callbacksBeforeLoad.length;e++){var t=callbacksBeforeLoad[e],o=t.func,i=t.arg;o.apply(null,i)}}}else for(var e=0;e<callbacksBeforeLoad.length;e++){var t=callbacksBeforeLoad[e],o=t.func,i=t.arg;o.apply(null,i)}})}};var recordVisitorLeave=function(e){var t=cache.appKey,o=device(),i=encodeURIComponent(location.href),n=(new Date).getTime(),a=encodeURIComponent(document.title),c=1;var r=function(){try{n=(new Date).getTime();var e=window.__YSFDAROOT__+"?ak="+t+"&dv="+o+"&cup="+i+"&tm="+n+"&ct="+a+"&lt="+c+"&u="+window.ysf.PAGEID;loadImage(e)}catch(r){}};if(e)r();else if(util.isMobilePlatform)util.addEvent(window,"pagehide",function(){r()});else util.addEvent(window,"beforeunload",function(){r()})};ysf.analytics=function(e){var t=cache.appKey,o=device(),i="",n=encodeURIComponent(document.referrer),a=encodeURIComponent(location.href),c="",r=localStorage.getItem("DA-LANDPAGE")||"",s=(new Date).getTime(),l=encodeURIComponent(document.title),m=0;var f=location.hostname;if(document.referrer.indexOf(f)==-1){r=encodeURIComponent(location.href);r=r.slice(0,1e3);localStorage.setItem("DA-LANDPAGE",r);localStorage.setItem("DA-LANDPAGE-TITLE",document.title);localStorage.setItem("DA-LANDPAGE-REFERRER",n)}if(e){var d=window.__YSFDAROOT__+"?ak="+t+"&dv="+o+"&si="+i+"&su="+n+"&cup="+a+"&tm="+s+"&cy="+c+"&lp="+r+"&ct="+l+"&lt="+m+"&u="+window.ysf.PAGEID;loadImage(d)}else if(window.__YSFVISITORRECORD__){var d=window.__YSFDAROOT__+"?ak="+t+"&dv="+o+"&cup="+a+"&tm="+s+"&ct="+l+"&lt="+m+"&u="+window.ysf.PAGEID;loadImage(d)}if(window.__YSFVISITORRECORD__)recordVisitorLeave()};var loadImage=function(e,t){t=t||function(){};var o=new Image;o.onerror=function(){util.consoleWarn("faild to load qa.gif")};o.onload=function(){t()};o.src=e;o.width=1;o.height=1;return o};var initWinConfig=function(){var e=window.screen||{};var t={base:",location=0,menubar=0,scrollbars=0,status=0,toolbar=0,resizable=0",layerNoInfo:{param:""},layerHasInfo:{param:""}};if(cache.bid){t.winNoInfo={width:724,height:575,left:Math.max(0,((e.width||0)-724)/2),top:Math.max(0,((e.height||0)-575)/2)};t.winHasInfo={width:944,height:575,left:Math.max(0,((e.width||0)-944)/2),top:Math.max(0,((e.height||0)-570)/2)}}else{t.winNoInfo={width:600,height:630,left:Math.max(0,((e.width||0)-600)/2),top:Math.max(0,((e.height||0)-630)/2)};t.winHasInfo={width:842,height:632,left:Math.max(0,((e.width||0)-840)/2),top:Math.max(0,((e.height||0)-630)/2)}}t.winNoInfo.param="top="+t.winNoInfo.top+",left="+t.winNoInfo.left+",width="+t.winNoInfo.width+",height="+t.winNoInfo.height+t.base;t.winHasInfo.param="top="+t.winHasInfo.top+",left="+t.winHasInfo.left+",width="+t.winHasInfo.width+",height="+t.winHasInfo.height+t.base;if(!util.isMobilePlatform())switch(cache["winType"]){case 1:winParam=cache["corpInfo"]?t.layerHasInfo:t.layerNoInfo;winParam.type="layer";break;case 3:winParam={type:"url",param:""};break;default:winParam=cache["corpInfo"]?t.winHasInfo:t.winNoInfo;winParam.type="win"}else switch(cache["winMobileType"]){case 2:winParam.type="layer";break;default:winParam=cache["corpInfo"]?t.winHasInfo:t.winNoInfo;winParam.type="win"}};var createDvcTimer=function(){var e=localStorage.getItem("YSFDVC-"+cache.device),t=0;if(null!=e)t=Number(e)+1;localStorage.setItem("YSFDVC-"+cache.device,t);cache.dvctimer=t};var reportInfo=function(){var e=0,t=3;return function(o,i,n,a){var c=serialize({appKey:cache.appKey,timestamp:(new Date).getTime(),token:cache.uuid,local:a||0});util.ajax({url:ysf.DOMAIN+"webapi/user/remoteStorage.action?"+c,method:"post",contentType:"json",data:o,success:function(e){if(util.isFunction(i))i()},error:function(c){console.info(c,"reportInfo err");if(e<t){e++;reportInfo(o,i,n,a)}else if(util.isFunction(n))n(c)}})}}();var getUnread=function(){getUnreadTimer&&clearTimeout(getUnreadTimer);if(!cache.noUnreadPolling)if(!document.hidden)util.ajax({url:ysf.DOMAIN+"webapi/user/getUnread.action",data:{appKey:cache.appKey,foreignId:cache.uid||"",deviceId:device(),bid:cache.bid||"",ts:(new Date).getTime()},success:function(e){ysf.HAS_MESSAGE_COUNT=e.count>0;cache["IN_SESSION"]=1===e.dvcSwitch;updateUnread(e.count,e.lastMessage);if(e.count>0){var t=layerCnt&&layerCnt.getAttribute("data-switch");if("1"==t)sendChatMsg("doconnect",{reconnect:1})}if(e.needPolling)getUnreadTimer=setTimeout(getUnread,e.delay||unreadPollingInterval)},error:function(e){var t=unreadPollingInterval;if(e&&e.result&&e.result.delay)t=e.result.delay;if(!e||8112!=e.code&&19104!=e.code&&14001!=e.code&&16001!=e.code&&8013!=e.code)getUnreadTimer=setTimeout(getUnread,t)}});else getUnreadTimer=setTimeout(getUnread,unreadPollingInterval)};var updateUnread=function(e,t){if(e>0){if(ysf.isInInviteLayerMode())ysf.displayInviteLayer(!1);formatMessage(t);if(cache["notifyContent"]&&t.time<cache["notifyContent"].time)return;cache["notifyContent"]=t;cache["notifyNumber"]=e;ysf.NotifyMsgAndBubble({category:"notifyCircle",data:{circleNum:cache["notifyNumber"],notifyCnt:cache["notifyContent"].content,type:cache["notifyContent"].type }})}else ysf.NotifyMsgAndBubble({category:"clearCircle"});var o=layerCnt&&layerCnt.getAttribute("data-switch");if("1"!=o)ysf._unread(ysf.getUnreadMsg())};var formatMessage=function(){var e={image:function(e){try{var t=JSON.parse(e.content);e.content=t}catch(o){}},richtext:function(e){try{var t=JSON.parse(e.content);e.content=t.content}catch(o){e.content="["+(local.RICHTEXT||"富文本")+"]"}e.type="rich"},custom:function(e){try{var t=JSON.parse(e.content);e.content=t}catch(o){e.content="["+(local.RICHTEXT||"富文本")+"]"}if(121==e.content.cmd)e.type="productinfo"},staffInviteDetail:function(e){try{var t=JSON.parse(e.content);e.content=t.message}catch(o){e.content="["+(local.EVALUATIONINVITATION||"评价邀请")+"]"}},userJoinEvaluate:function(e){try{var t=JSON.parse(e.content);e.content=t.message}catch(o){e.content="["+(local.EVALUATIONTHANK||"评价感谢")+"]"}}};e["file"]=e["image"];return function(t){if(e[t.type])e[t.type](t)}}();var formatSessionList=function(e){var t=[];if(!util.isArray(e)||!e.length)return t;e.forEach(function(e){if(!e.lastMessage)e.lastMessage={content:"",fromUser:0,time:e.endTime==-1?new Date.getTime:e.endTime,type:"custom"};if("richtext"===e.lastMessage.type)try{var t=JSON.parse(e.lastMessage.content);t.content=util.replaceRichAndUnescape(t.content);e.lastMessage.content=JSON.stringify(t)}catch(o){}});return e};var getSessionList=function(){var e=0,t=1e5;var o=0,i=3;return function(n,a){util.ajax({url:ysf.DOMAIN+"webapi/user/platform/session.action",data:{appKey:cache.appKey,offset:e,limit:t,total:!0,u:cache.uid},method:"post",success:function(e){var t=formatSessionList(e);if(util.isFunction(n))n(t)},error:function(e){if(!e||200!==e.code)if(o<i){o++;getSessionList(n,a)}else if(util.isFunction(a))a()}})}}();ysf.style=function(e){if(e){var t=document.getElementsByTagName("head")[0]||document.body,o=document.createElement("style");o.type="text/css";t.appendChild(o);if("textContent"in o)o.textContent=e;else if(o.styleSheet)o.styleSheet.cssText=e}};ysf.openInline=function(e,t){if("loading"!=ysf.openInline.loadStatus){if("loaded"==ysf.openInline.loadStatus&&util.isFunction(t))return t();var o=document.getElementById("YSF-PANEL-CORPINFO")||document.getElementById("YSF-PANEL-INFO")||document.getElementById("YSF-PANEL-MOBILE");if(e&&o){ysf.openInline.loadStatus="loading";e=util.mergeUrl(e,{w:cache["winType"]});var i=function(e){var t=document.createElement("iframe");t.src=e;t.id="YSF-IFRAME-LAYER";t.style.width="100%";t.style.height="100%";t.setAttribute("allowFullScreen",!0);t.setAttribute("frameborder","0");return t};chatProxy=i(e);o.appendChild(chatProxy);util.addEvent(chatProxy,"load",function(){if(!util.isEdge()&&!util.isIe()){if(1==cache["dvcswitch"]){chatProxySendConnect=!0;sendChatMsg("doconnect",{doconnect:1})}}else if(1==cache["dvcswitch"]&&initChatProxyBuild){chatProxySendConnect=!0;sendChatMsg("doconnect",{doconnect:1})}ysf.openInline.loadStatus="loaded";ysf._onLayerload();if(util.isFunction(t))t()})}}};ysf._$text2portrait=function(){var e=function(e){var t='<img class="portrait_icon" data-id="'+e.id+'" src="'+e.src+'" title="'+e.tag+'" alt="'+e.tag+'">';return t};var t=/(\[[^\]]+\])/gi;var o=window.__YSFSDKADR__+window.__YSFAPPPATH__+"res/portrait/emoji/";var i=window.__YSF_EMOJI__;return function(n){n=n.replace(/alt="(\[[^\]]+\])"/gi,'alt=""');var a=i.pmap;var c=i.pmap2;n=n.replace(t,function(t,n){if(c[n]){var r=e({id:c[n],tag:n,src:o+a[c[n]].file});return r}else if(i.mapEmojiLists&&i.mapEmojiLists.length){var s;for(var l=0;l<i.mapEmojiLists.length;l++)if(i.mapEmojiLists[l].tag==n)s=i.mapEmojiLists[l];if(s)if(s.tag==n){var r=e({id:n,tag:n,src:s.url});return r}else return n;else return n}else return n});return n}}();ysf.showInviteLayerAuto=function(e){if(e)window.setTimeout(function(){var t=!ysf.HAS_QIYU_OPEND&&ysf.hasTodayInviteTimes(e)&&e.invitePatternVo&&2===e.invitePatternVo.mode&&1===e.switchType&&e.waitTime&&!!cache["sessionInvite"]&&1!=cache["hidden"];if(t){ysf.setTodayShowInviteTimes();ysf.open({invitePatternVo:e.invitePatternVo,inviteTemplateId:e.id},ysf.INVITE_TPYE);ysf.inviteLayer.showInvite()}},1e3*e.waitTime)};ysf.inviteLayer=function(){return{resetLayer:function(){ysf.reset();cache["INIITE_LAYER"]=null},showInvite:function(){cache["INIITE_LAYER"]=1},isInviteLayerShow:function(){return 1==cache["INIITE_LAYER"]},inviteLayerHide:function(){cache["INIITE_LAYER"]=null}}}();ysf.getImRoot=function(e){var t=ysf.IMROOT;if(e===ysf.INVITE_TPYE&&!util.isMobilePlatform()){var o="/client/iframe";t=ysf.ROOT+o}return t};ysf.entry=function(e){var t=function(){var t=document.createElement("div");if(window.__YSFTHEMELAYEROUT__)t.className="layer-"+window.__YSFTHEMELAYEROUT__;t.setAttribute("id","YSF-BTN-HOLDER");if(1==cache["hidden"])t.style.display="none";var o=ysf.inviteText(e.inviteSetting);var i="YSF-CUSTOM-ENTRY-"+window.__YSFTHEMELAYEROUT__;var n="YSF-INVITE-IMG YSF_INVITE-IMG-"+window.__YSFTHEMELAYEROUT__;var a=o&&o.replace(/(<img.*?>)/g,"["+local.IMAGE+"]").replace(/<p><br><\/p>/g,"").replace(/<p>/g,"pStartElement").replace(/<\/p>/g,"pCloseElement").replace(/<.+?>/g,"").replace(/pStartElement/g,"<p>").replace(/pCloseElement/g,"</p>");var c="";if(o)c='<div class="YSF-INVITE-LAYER-WRAPPER" style="display: none"><div class="YSF-INVITE-LAYER-HEADER"><div class="YSF-INVITE-LAYER-HEADER-LEFT"><img class="YSF-INVITE-LAYER-AVATAR" src="'+e.inviteSetting.invitePatternVo.inviteAvatar+'" alt="" /><div class="YSF-INVITE-LAYER-KEFU-NAME">'+e.inviteSetting.invitePatternVo.realName+'</div></div><div id="YSF-ONLINE-INVITE-LAYER-CLOSE"></div></div><div class="YSF-INVITE-LAYER-RICHTEXT">'+ysf._$text2portrait(a)+'</div></div><div id="YSF-INVITE-LAYER-WRAPPER-ENTRY" style="display: none"><div class="YSF-LAYER-ENTRY-CIRCLE"></div><img class="'+n+'" src="'+e.src+'"/></div>';t.innerHTML=c+'<div id="'+i+'" class="YSF-CUSTOM-ENTRY"><img src="'+e.src+'"/></div>';t.onclick=function(){if(ysf.isInInviteLayerMode())ysf.open({invitePatternVo:e.inviteSetting.invitePatternVo,inviteTemplateId:e.inviteSetting.id});else ysf.open();ysf.HAS_QIYU_OPEND=!0;ysf.displayInviteLayer(!1)};document.body.appendChild(t);if(o)document.querySelector("#YSF-ONLINE-INVITE-LAYER-CLOSE").onclick=function(e){e.stopPropagation();ysf.displayInviteLayer(!1);ysf.INVITE_REJECT=!0;setTimeout(function(){ysf.displayInviteLayer(!0)},1e3*inviteSetting.intervalTime)};if(e.inviteSetting&&1===e.inviteSetting.switchType&&e.inviteSetting.waitTime)setTimeout(function(){ysf.displayInviteLayer(!0)},1e3*e.inviteSetting.waitTime);return t};var o=function(e){var t=document.createElement("span");t.setAttribute("id","YSF-BTN-CIRCLE");e.appendChild(t)};var i=function(e){var t=document.createElement("div"),o=document.createElement("div"),i=document.createElement("span"),n=document.createElement("span");t.setAttribute("id","YSF-BTN-BUBBLE");o.setAttribute("id","YSF-BTN-CONTENT");i.setAttribute("id","YSF-BTN-ARROW");n.setAttribute("id","YSF-BTN-CLOSE");n.onclick=function(e){e.stopPropagation();e.preventDefault();ysf.NotifyMsgAndBubble({category:"clearCircle"})};e.appendChild(t);t.appendChild(o);t.appendChild(i);t.appendChild(n)};var n=t();o(n);i(n)};ysf.hasTodayInviteTimes=function(e){var t="YSF_INVITE_SHOW_TIMES";var o=JSON.parse(localStorage.getItem(t)||"{}")[util.getToday()]||0;var i=e&&1===e.switchType&&(0===o||1===e.rejectType&&(!e.timesLimitType||e.timesLimit>o)||0===e.rejectType&&!ysf.INVITE_REJECT&&(!e.timesLimitType||e.timesLimit>o));return i};ysf.setTodayShowInviteTimes=function(){var e="YSF_INVITE_SHOW_TIMES";var t=JSON.parse(localStorage.getItem(e)||"{}");var o=util.getToday();var i={};i[o]=t[o]?t[o]+1:1;localStorage.setItem(e,JSON.stringify(i));util.ajax({url:ysf.DOMAIN+"webapi/invite/num",method:"get",data:{appKey:cache.appKey},success:function(e){console.log("[success]会话邀请次数统计接口")},error:function(e){console.log("[err]会话邀请次数统计接口",e)}})};ysf.inviteText=function(e){var t=e&&e.invitePatternVo;var o="";if(t)o=0===t.robotSwitch?t.humanWelcomePrompt:t.robotWelcomePrompt;return o};ysf.isInInviteLayerMode=function(){return document.querySelector(".YSF-INVITE-LAYER-WRAPPER")&&"none"!==document.querySelector(".YSF-INVITE-LAYER-WRAPPER").style.display};ysf.isInvitedLayer=function(){var e=ysf.inviteText(window.inviteSetting);var t=window.inviteSetting&&window.inviteSetting.invitePatternVo&&1===window.inviteSetting.invitePatternVo.mode&&e&&ysf.hasTodayInviteTimes(window.inviteSetting)&&!ysf.HAS_MESSAGE_COUNT&&!cache["IN_SESSION"]&&!ysf.HAS_QIYU_OPEND&&!!cache["sessionInvite"];return t};ysf.displayInviteLayer=function(e){function t(e){return e?"block":"none"}e=e&&ysf.isInvitedLayer();if(e)ysf.setTodayShowInviteTimes();var o=document.querySelector("#YSF-BTN-HOLDER");var i="#YSF-CUSTOM-ENTRY-"+window.__YSFTHEMELAYEROUT__;var n=document.querySelector(".YSF-INVITE-LAYER-WRAPPER");var a=document.querySelector("#YSF-INVITE-LAYER-WRAPPER-ENTRY");var c=document.querySelector(i);var r=document.querySelector("#YSF-BTN-CIRCLE");var s=document.querySelector("#YSF-BTN-BUBBLE");if(ysf.inviteText(window.inviteSetting)){n.style.display=t(e);a.style.display=t(e)}c.style.display=t(!e);if(e){o.style.maxHeight="none";o.style.maxWidth="none";r.style.display=t(!1);s.style.display=t(!1)}};ysf.entryPanel=function(e,t){var o=document.createElement("div"),i=util.isFrameModule()?0:1;if(util.isMobilePlatform()){o.setAttribute("id","YSF-PANEL-MOBILE");o.onclick=function(){if(!ysf.inviteLayer.isInviteLayerShow())receivePkg({category:"closeIframe"})}}else parseInt(e)?o.setAttribute("id","YSF-PANEL-CORPINFO"):o.setAttribute("id","YSF-PANEL-INFO");o.className="ysf-chat-layer";document.body.appendChild(o);o.setAttribute("data-switch",i);layerCnt=o;try{sendChatMsg("status",{layerOpen:i})}catch(n){}createDvcTimer();if(cache.preloadIframe)ysf.openInline(ysf.url());if(t&&util.isFunction(t.success))t.success()};ysf.invite=function(){var e,t,o,i=document.createDocumentFragment();var n=function(){if(!e){e=document.createElement("div");e.className="ysf-online-invite-wrap";if(o.style&&o.style.type){e.innerHTML='<div class="ysf-online-invite" style="cursor:default;width:'+o.style.bdWidth+"px;height:"+o.style.bdHeight+"px;margin-top:"+-o.style.bdHeight/2+'px"><div></div><div class="close custom" title="'+(local.CLOSE||"关闭")+'"></div><img class="ysf-online-invite-img"/></div>'; var t=e.childNodes[0],i=t.childNodes,n=i[0];if(util.isArray(o.style.oprs)){var s=null,l=null;for(var m=0;m<o.style.oprs.length;m++){s=o.style.oprs[m];l=document.createElement("a");if(s){l.style.position="absolute";l.style.cursor="pointer";l.style.height=s.height+"px";l.style.width=s.width+"px";l.style.top=s.top+"px";l.style.left=s.left+"px";if(3==s.type)l.onclick=c;else if(2==s.type)l.onclick=function(e){return function(){ysf.open({invited:1,templateId:e});r()}}(s.tplid);else if(1==s.type){l.href=s.url;l.target="_blank"}n.appendChild(l)}else;}}i[1].onclick=c;i[2].onload=function(){window.setTimeout(a,100)}}else if(o.style){e.innerHTML='<div class="ysf-online-invite"><div class="text"></div><div class="close" title="'+(local.CLOSE||"关闭")+'"></div><img/></div>';var t=e.childNodes[0],i=t.childNodes,f=i[0];if("innerText"in f)f.innerText=o.style.welcomeText;else f.textContent=o.style.welcomeText;t.onclick=function(){ysf.open({invited:1});r()};i[1].onclick=c;i[2].onload=function(){window.setTimeout(a,100)}}}};var a=function(){e.style.visibility="visible"};var c=function(e){e=e||window.event||{};if(e.stopPropagation)e.stopPropagation();else e.cancelBubble=!0;r();ysf.INVITE_REJECT=!0;if(0!=o.rejectType)window.setTimeout(s,1e3*o.intervalTime)};var r=function(){i.appendChild(e);e.getElementsByTagName("IMG")[0].src=ysf.CDNROOT+"res/nej_blank.gif"};var s=function(){if(cache["sessionInvite"]&&ysf.hasTodayInviteTimes(window.inviteSetting)){ysf.setTodayShowInviteTimes();n();e.style.visibility="hidden";document.body.appendChild(e);e.getElementsByTagName("IMG")[0].src=o.style&&o.style.type?o.style.bdImageUrl:ysf.CDNROOT+"res/invite/1/bg.png"}};return function(e){if(!e||!e.invitePatternVo||0===e.invitePatternVo.mode){if(!o){o=e||{};if(util.isMobilePlatform())o.style=o.h5Style}var t=function(){window.setTimeout(s,1e3*(o.waitTime||15))};if(cache.timestamp)t();else cache.onackdone=t}}}();ysf.checkOpenLayerSize=function(e,t){var o=360,i=500;if(cache.layerSize&&!util.isMobilePlatform()){var n=cache.layerSize;var a=parseInt(n.height)>i;var c=parseInt(n.width)>o;if(!t){if(c)e.style.width=util.includes(n.width,"px")?n.width:n.width+"px";if(a)e.style.height=util.includes(n.height,"px")?n.height:n.height+"px"}else{c&&(e.style.width=0);a&&(e.style.height=0)}}};ysf.openLayer=function(){return function(e){var t=document.getElementById("YSF-PANEL-CORPINFO")||document.getElementById("YSF-PANEL-INFO")||document.getElementById("YSF-PANEL-MOBILE"),o=document.getElementById("YSF-BTN-HOLDER"),i=document.getElementById("YSF-IFRAME-LAYER");if(t){o.style.display="none";t.className="ysf-chat-layer ysf-chat-layeropen";ysf.checkOpenLayerSize(t);if(util.isMobilePlatform()&&i)i.className="ysf-chat-mobile-layeropen"+(e===ysf.INVITE_TPYE?" ysf-chat-mobile-invite-layer":"");bodyscrollTop=document.body.scrollTop||document.documentElement.scrollTop;if(document.getElementById("YSF-PANEL-MOBILE")){document.body.classList.add("YSF-PANEL-BODY-FIXED");document.getElementById("YSF-PANEL-MOBILE").addEventListener("touchmove",function(e){e.stopPropagation()})}t.setAttribute("data-switch",1);try{sendChatMsg("status",{layerOpen:1})}catch(n){}}}}();window.windowurl="";ysf.openWin=function(){return function(e,t){if(util.isMobilePlatform())window.location.href=e;else{window.windowurl=window.open(e,"YSF_SERVICE_"+(cache.appKey||"").toUpperCase(),t.param);if(!window.windowurl&&!util.isIe())window.location.href=e}}}();ysf.openUrl=function(){return function(e,t){var o=e.replace(/\/iframe/g,"");window.windowurl=window.open(o,"YSF_SERVICE_"+(cache.appKey||"").toUpperCase(),t.param);if(!window.windowurl&&!util.isIe())window.location.href=o}}();ysf.close=function(){if(window.windowurl)window.windowurl.close();else if("layer"===winParam.type){var e=document.getElementById("YSF-PANEL-CORPINFO")||document.getElementById("YSF-PANEL-INFO")||document.getElementById("YSF-PANEL-MOBILE"),t=document.getElementById("YSF-BTN-HOLDER");delete ysf.openInline.loadStatus;document.body.removeChild(e);document.body.removeChild(t);firstBtnClick=!0}};ysf.NotifyMsgAndBubble=function(e){var t={clearCircle:function(e){var t="YSF-"+device()+"-MSGNUMBERS",o=document.getElementById("YSF-BTN-CIRCLE"),i=document.getElementById("YSF-BTN-BUBBLE");i&&(i.style.display="none");o&&(o.style.display="none");localStorage.setItem(t,0);cache["notifyNumber"]=0;cache["notifyContent"]="";CircleNumberFlag=0},notifyCircle:function(e){var t="YSF-"+device()+"-MSGNUMBERS";localStorage.setItem(t,e.data.circleNum);var o=document.getElementById("YSF-BTN-BUBBLE"),i=document.getElementById("YSF-BTN-CONTENT"),n=document.getElementById("YSF-BTN-CIRCLE");var a=document.getElementById("YSF-PANEL-CORPINFO")||document.getElementById("YSF-PANEL-INFO")||document.getElementById("YSF-PANEL-MOBILE");var c={image:function(e){return"["+(local.IMAGE||"图片")+"]"},audio:function(e){return"["+(local.AUDIO||"音频")+"]"},video:function(e){return"["+(local.VIDEO||"视频")+"]"},file:function(e){return"["+(local.FILE||"文件")+"]"+e.name},text:function(e){return e},rich:function(e){return util.replaceRichAndUnescape(e)},productinfo:function(e){return"["+(local.PRD_LINK||"商品链接")+"]"},staffInviteDetail:function(e){return util.replaceRichAndUnescape(e)},userJoinEvaluate:function(e){return util.replaceRichAndUnescape(e)}};var r=util.isFrameModule()&&a&&0==a.getAttribute("data-switch")&&c[e.data.type]&&0==cache["sdkCustom"];if(!util.isFrameModule()||r){n&&(n.style.display="block");o&&(o.style.display="block");n&&(n.innerHTML=e.data.circleNum>99?"99+":e.data.circleNum);i&&(i.innerHTML=c[e.data.type](e.data.notifyCnt))}}};var o=t[e.category];if(o)o(e)};ysf.getUnreadMsg=function(){return{type:cache["notifyContent"].type,message:cache["notifyContent"].content,total:cache["notifyNumber"]}};ysf.getConversationThrottle=util.throttleDebounce(function(){getSessionList(ysf._onConversation)},2e3);ysf.initNim=function(){function e(e){var o=cache["yunxin-user"];if(!o)cache["yunxin-user"]=e;else t(o,e)}function t(e,t){each(t,function(t,o){e[t]=o});return e}function o(e){var o=e.idClient.split("#")[0];if(!o){console.error("msg idClient without #",e.idClient,e);return e}return t(e,{id:e.idClient,bid:e.from,sessionid:o})}function i(e,i){console.log(e,i,"_cbReceiveMessage");var c={image:"file",audio:"file",video:"file"};var r={60:!0,90:!0,95:!0,72:!0,200:!0,65:!0,203:!0,121:!0,59:!0};e=o(e);if("custom"!=e.type)a(t(e,{content:e[c[e.type]||e.type],reallyType:e.type}));else{e.isOffline=i;n(e,r)}}function n(e,o,i){var n=e.content||"";var c=[9,17,59,42,1002,"-1000",11056,108,11060];try{n=JSON.parse(n);if(o&&!o[n.cmd]&&!i)return;if(~c.indexOf(n.cmd)||~c.indexOf(i))return;if(65==n.cmd){n.content=util.replaceRichAndUnescape(n.content);e.content=JSON.stringify(n)}}catch(r){}var s=n.sessionid||n.currentSessionId||n.workSheetTemplateVo&&n.workSheetTemplateVo.sessionId;a(t(e,{cmd:n.cmd,sessionid:s}))}function a(e){ysf._onSessionMessage(e);if(cache["onConversation"])ysf.getConversationThrottle()}function c(t){console.log(t,"_cbConnect2Yunxin");if(t.ip)e({ip:t.ip});j(_.CONNECTED);m()}function r(e){var t={302:function(){console.error("连接失败")}};var o=t[e.code];if(util.isFunction(o))o.call(this);else if("string"==typeof o)t[o].call(this)}function s(){j(_.NULL)}function l(e){e.sort(function(e,t){return e.time>t.time});e.forEach(function(e){i(e,!0)})}function m(){var e=this;var t=function(){f({content:{cmd:-1e3,deviceid:device()}});if(e._timeout)clearTimeout(e._timeout);e._timeout=setTimeout(function(){t()},h)};t()}function f(e){ysf.nim.sendCustomSysMsg({to:e.bid||g,cc:!0,filter:!0,scene:"p2p",content:JSON.stringify(e.content),done:e.done})}function d(){ysf.nim&&ysf.nim.disconnect();j(_.NULL)}function u(){return~ysf.DOMAIN.indexOf("qytest.netease")}var _={NULL:0,INIT:1,CONNECTED:2};var g=-1;var p=3e3;var y=_.NULL;var h=8e3;var v=function(){return{db:!1,syncBroadcastMsgs:!1,syncExtraTeamInfo:!1,syncFilter:!0,syncFriendUsers:!1,syncFriends:!1,syncRelations:!1,syncRoamingMsgs:!1,syncRobots:!1,syncSessionUnread:!1,syncStickTopSessions:!1,syncSuperTeamRoamingMsgs:!1,syncSuperTeams:!1,syncTeamMembers:!1,syncTeams:!1,defaultLinkUrl:"https://weblink-qiyu.netease.im",privateConf:{lbs_web:window.MIN_LBS},onmsg:i,onerror:r,onconnect:c,ondisconnect:s,oncustomsysmsg:n,onofflinefiltermsgs:l}}();var j=function(e){cache["yinxin-state"]=e};var I=function(){return cache["yinxin-state"]};var w=function(e){var t=cache["yunxin-user"]||{};if(window.localStorage){var o="X-"+e.toUpperCase()+"-YSF-INFO",i=localStorage.getItem(o);if(!i)return;var n=util.query2Object(i);if(n.uid)t.fid=n.uid;n.source=i;return n}};var S=function(e){try{return JSON.parse(e)}catch(t){return null}};var T=function(o,i){if(i){e(i);i.appKey=o;t(i,v);window.nim=ysf.nim=new NIM(i);getSessionList(ysf._getConversation)()}else window.setTimeout(C._$bind(this,appkey),p)};var E=function(e,t){util.ajax({url:ysf.DOMAIN+"webapi/user/create.action?appKey="+t,data:e,method:"post",fullResult:!0,contentType:"json",success:function(e){var o=e.info;e.result={token:o.token,account:o.accid,exchange:o.exchange,bid:o.bid};T(t,e.result)},error:function(e){console.log(e,"userCreate error")}})};var C=function(e){var t={deviceid:cache.uid||cache.device,appKey:e,token:cache.uuid||""};if(cache.uid)t.foreignid=cache.uid;if(cache.authToken)t.authtoken=cache.authToken;E(t,e)};var N=function(e,t){if(util.isArray(e)&&util.isFunction(t)){var o=e.length;var i=0;e.forEach(function(e){util.loadJS(e,function(){i++;if(i>=o)t()})})}};return{_$connect:function(e){d();if(I()===_.NULL){j(_.INIT);var t=["//res.qiyukf.net/storage/NIM_Web_NIM_v9.8.103.js"];N(t,function(){C(e)})}},_$disConnect:function(){d()}}}();ysf.config=function(e,t){if(e){e.reallyUid=e.uid;merge(e);if(!t)each(e,function(e){if(configKeys.indexOf(e)<0)configKeys.push(e)});if(cache.appKey){refresh(e.uid);syncProfile({upToServer:!0,success:e.success,error:e.error,local:t||0});initWinConfig();getUnread();if(cache["connectYunxin"])ysf.initNim._$connect(cache["appKey"])}}};var childMerchantsIsOpen=function(e,t){util.ajax({url:ysf.DOMAIN+"webapi/user/company/get/getCorpStatus.action", method:"get",data:{appKey:cache.appKey,shopCode:e},success:function(e){corpStatus=e;if(util.isFunction(t))t()},error:function(e){console.log("[err]平台企业子企业是否开启",e);if(util.isFunction(t))t()}})};ysf.url=function(e,t){if(!cache.appKey)return"";var o={k:cache.appKey,u:cache.uid||"",d:device(),uuid:cache.uuid,gid:cache.groupid||0,sid:cache.staffid||0,qtype:cache.qtype||0,welcomeTemplateId:cache.welcomeTemplateId||0,dvctimer:cache.dvctimer||0,robotShuntSwitch:cache.robotShuntSwitch||0,hc:cache.hc||0,robotId:cache.robotId||0,pageId:ysf.PAGEID,shuntId:cache.shuntId||0,ctm:(new util.Base64).encode(cache.uid+"--"+(new Date).getTime()),wxwId:cache.wxworkAppId||"",language:cache.language||"",isShowBack:cache.isShowBack||0,shortcutTemplateId:cache.shortcutTemplateId||""};if(1==cache.spkf)o.spkf=1;if(cache.APPbackPop)o.APPbackPop=cache.APPbackPop;if(cache.APPBridgePermission)o.APPBridgePermission=cache.APPBridgePermission;if(cache.usealipayjs)o.usealipayjs=cache.usealipayjs;if(cache.isCaptureSupported)o.isCaptureSupported=cache.isCaptureSupported;if(cache.usewxjs)o.usewxjs=cache.usewxjs;if(cache.downloadPageUrl)o.downloadPageUrl=cache.downloadPageUrl;if(cache.customUrl)o.customUrl=cache.customUrl;if(cache.appName)o.appName=cache.appName;if(cache.checkPermission)o.checkPermission=cache.checkPermission;if(cache.disableMultiple)o.disableMultiple=cache.disableMultiple;if(cache.emojiPopoverWidth)o.emojiPopoverWidth=cache.emojiPopoverWidth;if(t==ysf.INVITE_TPYE)o.isInvite=1;if(window.sdkTemplateId)o.templateId=window.sdkTemplateId;if(window.shuntId)o.shuntId=window.shuntId;if(e&&e.templateId)o.templateId=e.templateId;if(e&&e.invited)o.invited=1;if(e&&e.invitePatternVo&&t!=ysf.INVITE_TPYE){o.invited=1;o.inviteTemplateId=e.inviteTemplateId;o.robotSwitch=e.invitePatternVo.robotSwitch;o.humanWelcomeTemplateId=e.invitePatternVo.welcomeTemplateId;o.robotId=e.invitePatternVo.robotId;o.welcomeTemplateId=e.invitePatternVo.robotWelcomeTemplateId;o.inviteGroupId=e.invitePatternVo.inviteGroupId}if(!util.isMobilePlatform()&&1==window.__YSFWINTYPE__&&cache.layerSize&&cache.layerSize.inputHeight)o.ipth=cache.layerSize.inputHeight;if(e&&e.language)o.language=e.language;if(cache.unconfirm)o.unconfirm=cache.unconfirm;if(cache["debugger"])o["debugger"]=cache["debugger"];if(!util.isMobilePlatform()&&1==window.__YSFWINTYPE__)o.allowNewTab=cache.allowNewTab||0;each({n:"name",e:"email",m:"mobile"},function(e,t){var i=cache[t];if(i)o[e]=i});if(cache.customSkin)each({topbarBgColor:"topbarBgColor",chatPanelBgColor:"chatPanelBgColor",lMsgColor:"lMsgColor",rMsgColor:"rMsgColor",lMsgBgColor:"lMsgBgColor",rMsgBgColor:"rMsgBgColor",buttonColor:"buttonColor",buttonBgColor:"buttonBgColor",editorColor:"editorColor",editorBgColor:"editorBgColor",corpInfoColor:"corpInfoColor",corpInfoTitleColor:"corpInfoTitleColor",corpInfoBgColor:"corpInfoBgColor",tipColor:"tipColor",linkColor:"linkColor",borderColor:"borderColor"},function(e,t){var i=cache.customSkin[t];if(i)o[e]=i});o.t=encodeURIComponent(cache.title||document.title);if(cache.goodsCId)o.goodsCId=cache.goodsCId;if(cache.goodsCName)o.goodsCName=cache.goodsCName;if(cache.goodsId)o.goodsId=cache.goodsId;if(cache.orderId)o.orderId=cache.orderId;if(cache.orderStageType)o.orderStageType=cache.orderStageType;if((e&&e.bid||cache.bid)&&corpStatus){o.bid=e&&e.bid||cache.bid;return ysf.getImRoot(t)+"/trade?"+serialize(o)}return ysf.getImRoot(t)+"?"+serialize(o)};ysf.track=function(e,t){var o=cache.appKey,i=device(),n=encodeURIComponent(location.href),a=localStorage.getItem("DA-LANDPAGE")||"",c=(new Date).getTime(),r=e,s="";desc=JSON.stringify(t),tp=1;var l=function(){try{var e=window.__YSFDAROOT__+"?ak="+o+"&dv="+i+"&cup="+n+"&lp="+a+"&tm="+c+"&ct="+r+"&lt="+s+"&tp="+tp+"&desc="+desc+"&u="+window.ysf.PAGEID;loadImage(e)}catch(t){}};l()};ysf.logoff=function(){updateDevice();util.clearLocalItems(util.findLocalItems(/msgnumbers/gi));configKeys.forEach(function(e){delete cache[e]})};ysf.openByLink=function(e){var t=ysf.url();if(t){e=e||{};var o=e.target||e.srcElement;if(o&&"A"==o.tagName)o.href=t}};var formatProduct=function(e){if(e.clear)return null;e.title=e.title&&e.title.length>100?e.title.slice(0,100):e.title;e.desc=e.desc&&e.desc.length>300?e.desc.slice(0,300):e.desc;e.note=e.note&&e.note.length>100?e.note.slice(0,100):e.note;var t={pageId:ysf.PAGEID,template:e.template,cardType:e.cardType,sendByUser:e.sendByUser,auto:1===e.sendByUser?0:1,show:1===e.sendByUser?1:e.show,actionText:e.actionText,actionTextColor:e.actionTextColor,sendProToRobot:e.sendProToRobot,picture:e.picture,title:e.title,desc:e.desc,url:e.url,note:e.note,price:e.price,goodsId:e.goodsId,goodsCId:e.goodsCId,goodsCName:e.goodsCName,intent:e.intent,payMoney:e.payMoney,orderId:e.orderId,orderTime:e.orderTime,orderSku:e.orderSku,orderCount:e.orderCount,orderStatus:e.orderStatus,orderStageType:e.orderStageType,floatCardSend:e.floatCardSend||0};if(e.tags&&util.isArray(e.tags))t.tags=JSON.stringify(e.tags);if(e.sendKefuOffline)t.sendKefuOffline=e.sendKefuOffline;if(e.params)t.params=e.params;if(cache.bid)t.bid=cache.bid;return t};ysf.product=function(e){var t=formatProduct(e);syncCustomProfile({data:t,success:e.success,error:e.error})};ysf.clearProduct=function(e){e=e||{};ysf.product({clear:!0,success:e.success,error:e.error})};var formatCardMessage=function(e){if(e.clear)return null;var t={uuid:e.uuid,type:e.type,cards:e.cards||[],floatCards:e.floatCards||[],auto:1===e.sendByUser?0:1,sendByUser:e.sendByUser,sendKefuOffline:e.sendKefuOffline||0,sendProToRobot:e.sendProToRobot,intent:e.intent,params:e.params||{}};if(1===e.sendByUser){if(e.actionText)t.actionText=e.actionText;if(e.actionTextColor)t.actionTextColor=e.actionTextColor;if(e.hideAction)t.hideAction=e.hideAction}if(cache.bid)t.bid=cache.bid;return t};ysf.cardMessage=function(e){if(!e.clear)if(e.cards&&0!==e.cards.length){var t=formatCardMessage(e);syncCardInfoProfile({data:t,success:e.success,error:e.error})}else{console.error("cards is required");e.error&&e.error("cards is required")}else syncCardInfoProfile({data:null,success:e.success,error:e.error})};var formatCustomMessage=function(e){if(void 0===e.data||null===e.data)return null;var t={uuid:e.uuid,data:e.data,description:e.description||"",auto:1===e.sendByUser?0:1,sendByUser:e.sendByUser};if(1===e.sendByUser){if(e.actionText)t.actionText=e.actionText;if(e.actionTextColor)t.actionTextColor=e.actionTextColor;if(e.hideAction)t.hideAction=e.hideAction}if(cache.bid)t.bid=cache.bid;return t};ysf.customMessage=function(e){var t=formatCustomMessage(e);syncCustomMessageProfile({data:t,success:e.success,error:e.error})};ysf.sendProduct=function(e){var t=e.success;var o=e.error;delete e.success;delete e.error;var i=formatProduct(e);sendChatMsgNew({type:"sendProduct",data:i}).then(t)["catch"](o)};ysf.sendCardMessage=function(e){if(e.cards&&0!==e.cards.length){var t=e.success;var o=e.error;delete e.success;delete e.error;var i=formatCardMessage(e);sendChatMsgNew({type:"sendCardMessage",data:i}).then(t)["catch"](o)}else{console.error("cards is required");e.error&&e.error("cards is required")}};ysf.clearCustomCard=function(e){e=e||{};ysf.cardMessage({clear:!0,success:e.success,error:e.error})};ysf.sendCustomMessage=function(e){var t=e.success;var o=e.error;delete e.success;delete e.error;var i=formatCustomMessage(e);sendChatMsgNew({type:"sendCustomMessage",data:i}).then(t)["catch"](o)};ysf.open=function(e,t){maxTemplateId=e;var o=ysf.url(e,t);if(o){var i=t||winParam.type;switch(i){case"win":ysf.openWin(o,winParam);break;case"layer":ysf.openInline(o,function(){var o=e&&e.invitePatternVo&&2==e.invitePatternVo.screenType&&util.isMobilePlatform()?"":t;ysf.openLayer(o);try{if(firstBtnClick&&0==cache["dvcswitch"]){sendChatMsg("doconnect",{doconnect:1});firstBtnClick=!1}}catch(i){}if(0==cache["dvcswitch"]&&1==cache["pushswitch"]||CircleNumberFlag>0){sendChatMsg("dopushmsgread",{ids:msgSessionIds});msgSessionIds=[]}if(!firstBtnClick&&cache["notifyNumber"]>0)sendChatMsg("doconnect");ysf.NotifyMsgAndBubble({category:"clearCircle"})});break;case"url":ysf.openUrl(o,winParam)}}};ysf.init=function(e,t,o){window.inviteSetting=t||{};var i=function(){ysf.entry({src:e,inviteSetting:t||{}});var i=t&&t.invitePatternVo&&2===t.invitePatternVo.mode&&1===t.switchType;if(util.isFrameModule()||i)ysf.entryPanel(cache["corpInfo"],o);ysf.showInviteLayerAuto(t)};if(util.isFrameModule())setTimeout(function(){util.ajax({url:ysf.DOMAIN+"webapi/user/dvcSession.action?appKey="+cache["appKey"]+"&d="+cache["device"]+"&f="+cache["uid"],method:"post",success:function(e){dvcswitched=!0;cache["dvcswitch"]=e.dvcSwitch;cache["pushswitch"]=e.pushSwitch||0;cache["pushmsgid"]=e.batchIdList||0;if("mobileFrame"==util.isFrameModule())cache["dvcswitch"]=0;i();if(inited&&!callbacksBeforeLoadIsRun){callbacksBeforeLoadIsRun=!0;for(var t=0;t<callbacksBeforeLoad.length;t++){var o=callbacksBeforeLoad[t],n=o.func,a=o.arg;n.apply(null,a)}}},error:function(){dvcswitched=!0;cache["dvcswitch"]=0;cache["pushswitch"]=0;i();if(inited&&!callbacksBeforeLoadIsRun){callbacksBeforeLoadIsRun=!0;for(var e=0;e<callbacksBeforeLoad.length;e++){var t=callbacksBeforeLoad[e],o=t.func,n=t.arg;o.apply(null,n)}}}})},1e3);else{cache["dvcswitch"]=0;cache["pushswitch"]=0;i()}};ysf.reset=function(e){var t=document.getElementById("YSF-PANEL-CORPINFO")||document.getElementById("YSF-PANEL-INFO")||document.getElementById("YSF-PANEL-MOBILE"),o=document.getElementById("YSF-BTN-HOLDER");delete ysf.openInline.loadStatus;document.body.removeChild(t);document.body.removeChild(o);ysf.init(cache["imgSrc"],window.inviteSetting,{success:e&&e.success,error:e&&e.error});firstBtnClick=!0};util.addEvent(window,"beforeunload",function(){var e="YSFDVC-"+cache["device"],t="YSFMSG-"+cache["appKey"],o=Number(localStorage.getItem(e));if(o>0)localStorage.setItem(e,--o);util.clearLocalItems(util.findLocalItems(new RegExp(t,"ig")))});ysf.on=function(){var e={onload:"load",unread:1};return function(e){var t=Object.prototype.toString.call(e).slice(8,-1);if(/object/gi.test(t)){ for(var o in e)if("onload"==o&&util.isFunction(e[o]))if(!inited)util.addLoadEventForProxy(e[o]);else e[o]();else if("onLayerload"==o&&util.isFunction(e[o]))if("loaded"==ysf.openInline.loadStatus)e[o]();else ysf["_"+o]=e[o];else if(util.isFunction(ysf[o])&&util.isFunction(e[o]))ysf["_"+o]=e[o]}else util.consoleWarn("波比(｡･∀･)ﾉ: 请保持正确的监听姿势...")}}();ysf.getPushMessage=function(e){sendChatMsg("dogetpushmsg",{ids:e})};ysf._unread=function(){};ysf.unread=function(){return{type:cache["notifyContent"].type,message:cache["notifyContent"].content,total:cache["notifyNumber"]}};ysf._onLayerload=function(){};ysf.clearDragresize=function(e){try{var t=window.location.hostname.split(".")[0];var o=["callcenter","session","qualitysession","qualitycallsession","worksheet","callflow","leave","monitor","usercenter"];if(o.indexOf(e)>-1)util.clearLocalItems(util.findLocalItems(new RegExp(t+"-"+e,"ig")));else for(var i=0;i<o.length;i++)util.clearLocalItems(util.findLocalItems(new RegExp(t+"-"+o[i],"ig")))}catch(n){}};ysf.setAuthToken=ysf.setToken=function(e){cache["authToken"]=e;syncProfile({})};ysf.pollAuthToken=function(){var e=0;var t=-1;var o=4;return function(i,n){var a=function(o){var a=o&&o.authToken;if(a){ysf.setAuthToken(o.authToken);e=0;clearInterval(t);t=setTimeout(function(){ysf.pollAuthToken(i,n)},n.interval);if(util.isFunction(n.onsuccess))n.onsuccess(o)}else util.consoleError("result has not token",o)};var c=function(t){if(e<o){e++;ysf.pollAuthToken(i,n)}if(util.isFunction(n.onerror))n.onerror(t)},r=n.method||"GET";o=n.tryTime||4;if(null!==n.interval)util.ajax({url:i,method:r,data:n.data,success:a,error:c});else util.consoleError("pollauthtoken is not set interval",n)}}();ysf.onready=function(e){util.isFunction(e)&&e()};ysf.onunread=function(e){ysf.on({unread:e})};ysf.onLayerload=function(e){ysf.on({onLayerload:e})};ysf.getConversation=function(e){ysf.on({getConversation:e})};ysf._getConversation=function(){};ysf.onConversation=function(e){ysf.on({onConversation:e});cache["onConversation"]=!0};ysf._onConversation=function(){};ysf.onSessionMessage=function(e){ysf.on({onSessionMessage:e})};ysf._onSessionMessage=function(){};!function(){each({DOMAIN:ysf.ROOT+"/",IMROOT:function(){var e="/client/iframe";if(util.isMobilePlatform()&&2===window.__YSFMODILEWINTYPE__)e="/client";else if(!util.isMobilePlatform()&&1==window.__YSFWINTYPE__)e="/client/iframe";else e="/client";var t=ysf.ROOT+e;return t}(),RESROOT:ysf.ROOT+"/sdk/",CDNROOT:"//res.qiyukf.net/sdk/",INVITE_TPYE:"layer"},function(e,t){if(null==ysf[e])ysf[e]=t});migrate();buildProxy();ysf.PAGEID=initPageId();for(var e=0;e<cachedArgs.length;e++)try{var t=cachedArgs[e],o=t[0],i=Array.prototype.slice.call(t,1);execEvent(o,i)}catch(n){util.consoleWarn(n)}}()}();var __YSFOPTION__ = { corpInfo: Number('1'), winType : Number('1'), winMobileType : Number('1'), sdkCustom : 0, hidden : 1, preloadIframe: 0, appKey:'24e2aca48fb99e6110acb54e48a22d06' }; __YSFOPTION__.uid = ''; try{ __YSFOPTION__.profile = JSON.stringify(__YSFOPTION__.profile); } catch(ex) { __YSFOPTION__.profile = ''; } __YSFOPTION__.imgSrc = 'https://qiyukf.nosdn.127.net/sdk/res/kefu/custom/4.png'; __YSFOPTION__.sessionInvite = 1; ysf.config(__YSFOPTION__, 1); ysf.style([ '#YSF-BTN-HOLDER{position: fixed;max-width:70px;max-height:70px;right: 30px; bottom: 24px; cursor: pointer; overflow: visible; filter: alpha(opacity=100);opacity:1;z-index: 9990}', '#YSF-BTN-HOLDER:hover{filter: alpha(opacity=95);opacity:.95}', '#YSF-BTN-HOLDER img{ display: block;overflow: hidden; }', '#YSF-BTN-CIRCLE{display: none;position: absolute;right: -5px;top: -6px;width: auto;min-width: 12px;height: 20px;padding: 0 4px;background-color: #f00;font-size: 12px;line-height: 20px;color: #fff;text-align: center;white-space: nowrap;font-family: sans-serif;border-radius: 10px;z-index:1;}', '#YSF-BTN-BUBBLE{display: none;position: absolute;left: -274px;bottom:-15px;width: 278px;height: 80px;box-sizing: border-box;padding: 14px 22px;filter: alpha(opacity=100);opacity:1;background: url(https://res.qiyukf.net/sdk/res/img/sdk/bg_floatMsg2x.png) no-repeat;background:url(https://res.qiyukf.net/sdk/res/img/sdk/bg_floatMsg.png)\9; background-size: 278px 80px; z-index: 1;}', '#YSF-BTN-HOLDER.layer-6{bottom:0;}', '#YSF-BTN-HOLDER.layer-1 #YSF-BTN-BUBBLE{top:0}', '#YSF-BTN-HOLDER.layer-6 #YSF-BTN-BUBBLE{bottom:-6px;}', '#YSF-BTN-BUBBLE:hover{filter: alpha(opacity=95);opacity:.95}', '#YSF-BTN-CONTENT{height:45px;padding: 0;white-space: normal;word-break: break-all;text-align: left;font-size: 14px;line-height: 1.6;color: #222;overflow: hidden;z-index: 0;}', '#YSF-BTN-ARROW{ display: none; }', '#YSF-BTN-CLOSE{position: absolute; width:15px; height:15px;right: 4px;top: -3px; filter: alpha(opacity=90); opacity:.9; cursor: pointer; background: url(https://res.qiyukf.net/sdk/res/img/sdk/btn-close.png) no-repeat;z-index: 1}', '#YSF-BTN-CLOSE:hover{filter: alpha(opacity=100); opacity: 1;}', '#YSF-PANEL-CORPINFO.ysf-chat-layeropen{ width: 511px; height: 500px; border-radius: 8px; box-shadow: 0 0 20px 0 rgba(0, 0, 0, .15);}', '#YSF-PANEL-CORPINFO{ position: fixed; bottom: 0px; right: 20px; width: 0; height: 0; z-index: 99999; }', '#YSF-PANEL-INFO.ysf-chat-layeropen{ width: 360px; height: 500px; border-radius: 8px; filter: alpha(opacity=100);opacity:1; box-shadow: 0 0 20px 0 rgba(0, 0, 0, .15);}', '#YSF-PANEL-INFO{ position: fixed; bottom: 0px; right: 20px; width: 0px; height: 0px; filter: alpha(opacity=0);opacity:0;z-index: 99999;}', '#YSF-PANEL-INFO .u-btn{background-color: #9cb9ec}', '#YSF-CUSTOM-ENTRY{background-color: #F96868;}', '#YSF-CUSTOM-ENTRY-0{position: relative;width:auto;background-color: #9cb9ec;box-shadow: 0px 6px 10px 0px rgba(0,0,0,0.25);}', '#YSF-CUSTOM-ENTRY-1{position: relative;width:auto;background-color: #9cb9ec;border-radius: 14px; box-shadow: 0px 6px 10px 0px rgba(0,0,0,0.25);}', '#YSF-CUSTOM-ENTRY-2{position: relative;width:auto;background-color: #9cb9ec;border-radius: 8px;box-shadow: 0px 6px 10px 0px rgba(0,0,0,0.25);}', '#YSF-CUSTOM-ENTRY-3{position: relative;width:auto;background-color: #9cb9ec;border-radius: 50%;box-shadow: 0px 6px 10px 0px rgba(0,0,0,0.25);}', '#YSF-CUSTOM-ENTRY-4{position: relative;width:auto;background-color: #9cb9ec;border-radius: 50%;box-shadow: 0px 6px 10px 0px rgba(0,0,0,0.25);}', '#YSF-CUSTOM-ENTRY-5{position: relative;width:auto;background-color: #9cb9ec;border-radius: 8px;box-shadow: 0px 6px 10px 0px rgba(0,0,0,0.25);}', '#YSF-CUSTOM-ENTRY-6{position: relative;width:auto;background-color: #9cb9ec;border-radius: 8px;box-shadow: 0px 6px 10px 0px rgba(0,0,0,0.25);}', '#YSF-CUSTOM-ENTRY-7{position: relative;width:auto;background-color: #9cb9ec;border-radius: 50%;box-shadow: 0px 6px 10px 0px rgba(0,0,0,0.25);}', '#YSF-CUSTOM-ENTRY-8{position: relative;width:auto;background-color: #9cb9ec;border-radius: 8px;box-shadow: 0px 6px 10px 0px rgba(0,0,0,0.25);}', '#YSF-CUSTOM-ENTRY-0 img{max-width: 300px;max-height: 300px;}', '#YSF-CUSTOM-ENTRY-1 img{width:28px;height:auto;}', '#YSF-CUSTOM-ENTRY-2 img{width:58px;height:auto;}', '#YSF-CUSTOM-ENTRY-3 img{width:60px;height:auto;}', '#YSF-CUSTOM-ENTRY-4 img{width:60px;height:auto;}', '#YSF-CUSTOM-ENTRY-5 img{width:60px;height:auto;}', '#YSF-CUSTOM-ENTRY-6 img{width:58px;height:auto;}', '#YSF-CUSTOM-ENTRY-7 img{width:60px;height:auto;}', '#YSF-CUSTOM-ENTRY-8 img{width:60px;height:auto;}', '#YSF-IFRAME-LAYER{ border:0; outline:none; }', '.ysf-online-invite-wrap{z-index:10001;position:fixed;_position:absolute;top:50%;left:50%;}', '.ysf-online-invite{position:relative;top:-50%;left:-50%;cursor:pointer;border-radius: 16px;box-shadow: 0 10px 30px 0 rgba(47,56,111,0.15);}', '.ysf-online-invite img{display:block;width:250px;}', '.ysf-online-invite .ysf-online-invite-img{width:100%;height:100%}', '.ysf-online-invite .text{position:absolute;top:-11px;left:0;right:0;overflow:hidden;margin: 36px 20px 0 67px;line-height:140%;color:#526069;font-size:14px;font-family:"Microsoft YaHei","微软雅黑",tahoma,arial,simsun,"宋体";text-align:left;white-space:normal;word-wrap:break-word;}', '.ysf-online-invite .close{position:absolute;top:12px;right:12px;width:16px;height:16px;background:url("https://ysf.nosdn.127.net/operation/4602027f7e6a0109ac433863d7d0acdf") no-repeat;cursor:pointer;background-size: cover;}', '#YSF-PANEL-MOBILE{ background: rgba(46,47,49,0.7); position: fixed; bottom: 0px; right: 0px; width: 100%; height: 0px; transition-property: height,bottom; transition-duration: 0.3s; transition-timing-function: ease; transition-delay: 0s; z-index: 99999; }', '#YSF-PANEL-MOBILE.ysf-chat-layeropen{ width: 100%; height: 100%; transition-property:height,bottom; transition-duration: 0.3s; transition-timing-function: ease; transition-delay: 0s; }', '#YSF-IFRAME-LAYER.ysf-chat-mobile-layeropen{ height: 90% !important; transition-property: height,bottom; transition-duration: 0.3s; transition-timing-function: ease; transition-delay: 0s; }', '#YSF-IFRAME-LAYER.ysf-chat-mobile-layeropen.ysf-chat-mobile-invite-layer{ height: 33% !important; }', '#YSF-IFRAME-LAYER{ position: absolute; bottom: 0px;left: 0; right: 0px; width: 0px; height: 0px; transition-property: height,bottom; transition-duration: 0.3s; transition-timing-function: ease; transition-delay: 0s; z-index: 9999; }', '.YSF-PANEL-BODY-FIXED{ position: fixed !important; left: 0; right: 0;}', '.YSF-INVITE-LAYER-WRAPPER { cursor: pointer; position: relative; border: 1px solid #eaf0f6; background: #fff;box-shadow: 0 5px 20px rgba(0,0,0,0.1); border-radius: 4px; width: 220px; padding: 16px; box-sizing: border-box; margin-bottom: 12px; background: #FFFFFF; box-shadow: 0 6px 20px 0 rgba(40,53,75,0.17); border-radius: 8px; }', '.YSF-INVITE-LAYER-HEADER { display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px; }', '.YSF-INVITE-LAYER-HEADER-LEFT { display: flex; flex: 1; align-items: center; }', '.YSF-INVITE-LAYER-RICHTEXT { height: 44px; max-width: 168px; word-break: break-all; overflow: hidden; text-overflow: ellipsis; font-size: 14px; color: #333333; line-height: 22px; text-align: left; }', '.YSF-INVITE-LAYER-RICHTEXT p { margin: 0 }', '#YSF-BTN-HOLDER .YSF-INVITE-LAYER-AVATAR { width: 20px; height: 20px; border-radius: 50%; object-fit: cover; }', '.YSF-INVITE-LAYER-KEFU-NAME { font-size: 12px; color: #999999; margin-left: 4px; max-width: 150px; overflow: hidden; text-overflow: ellipsis; }', '#YSF-ONLINE-INVITE-LAYER-CLOSE{ width:12px; height:12px; background:url("https://ysf.nosdn.127.net/operation/0e5ee37a71d33e05b74d1a5aa3cdd99a") no-repeat; background-size: cover; cursor:pointer; }', '#YSF-INVITE-LAYER-WRAPPER-ENTRY { position: relative; float: right; }', '.YSF-LAYER-ENTRY-CIRCLE { width: auto; min-width: 12px; height: 20px; padding: 0 4px; border-radius: 10px; color: #FFFFFF; background-color: #f00; font-size: 12px; line-height: 20px; white-space: nowrap; z-index:1; background: url("https://ysf.nosdn.127.net/operation/997228b687d805f7a5d6244865615ec9") no-repeat; background-size: cover; position: absolute; top: 6px; right: -8px; }', '.YSF-INVITE-IMG {background-color: #9cb9ec;box-shadow: 0px 6px 10px 0px rgba(0,0,0,0.25);float: right;}', '.YSF_INVITE-IMG-0{max-width: 300px; height:auto;}', '.YSF_INVITE-IMG-1{width:28px; height:auto; border-radius: 14px;}', '.YSF_INVITE-IMG-2{width:58px; height:auto; border-radius: 8px;}', '.YSF_INVITE-IMG-3{width:60px; height:auto; border-radius: 50%;}', '.YSF_INVITE-IMG-4{width:60px; height:auto; border-radius: 50%;}', '.YSF_INVITE-IMG-5{width:60px; height:auto; border-radius: 8px;}', '.YSF_INVITE-IMG-6{width:58px; height:auto; border-radius: 8px;}', '.YSF_INVITE-IMG-7{width:60px; height:auto; border-radius: 50%;}', '.YSF_INVITE-IMG-8{width:60px; height:auto; border-radius: 8px;}', '#YSF-BTN-HOLDER .portrait_icon{width: 24px;height: 24px;vertical-align: middle;display: inline-block;}' ].join(' ')); ysf.init('https://qiyukf.nosdn.127.net/sdk/res/kefu/custom/4.png', {switchType:0,h5Style:{bdImageUrl:"",bdImageY:0,bdHeight:280,bdWidth:240,welcomeText:"您好，请问有什么可以帮助您？",type:0,bdImageX:0},invitePatternVo:{inviteAvatar:"https://bjshdqyhflpxxx1.qiyukf.net/73e2aed14a1468616394615c99d2292d.png",mode:0,realName:"",robotSwitch:1,welcomeTemplateId:0,robotWelcomeTemplateId:0,screenType:1,inviteGroupId:484736929,robotId:-5406414},timesLimitType:true,createTime:1711604762000,name:"",rejectType:0,style:{bdImageUrl:"",bdImageY:0,bdHeight:280,bdWidth:400,welcomeText:"您好，请问有什么可以帮助您？",type:0,bdImageX:0},id:0,waitTime:15,timesLimit:1,intervalTime:45}); window.inviteSetting = {switchType:0,h5Style:{bdImageUrl:"",bdImageY:0,bdHeight:280,bdWidth:240,welcomeText:"您好，请问有什么可以帮助您？",type:0,bdImageX:0},invitePatternVo:{inviteAvatar:"https://bjshdqyhflpxxx1.qiyukf.net/73e2aed14a1468616394615c99d2292d.png",mode:0,realName:"",robotSwitch:1,welcomeTemplateId:0,robotWelcomeTemplateId:0,screenType:1,inviteGroupId:484736929,robotId:-5406414},timesLimitType:true,createTime:1711604762000,name:"",rejectType:0,style:{bdImageUrl:"",bdImageY:0,bdHeight:280,bdWidth:400,welcomeText:"您好，请问有什么可以帮助您？",type:0,bdImageX:0},id:0,waitTime:15,timesLimit:1,intervalTime:45}; window.sdkTemplateId = 0; window.shuntId = 0;